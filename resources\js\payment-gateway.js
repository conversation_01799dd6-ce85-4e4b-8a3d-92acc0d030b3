/**
 * Payment Gateway Integration
 *
 * This file contains functions for integrating with payment gateways (Xendit).
 */

class PaymentGateway {
    /**
     * Initialize payment gateway
     *
     * @param {string} gateway - The payment gateway to use ('xendit')
     * @param {object} config - Configuration for the payment gateway
     */
    static init(gateway, config) {
        if (gateway === "xendit") {
            return new XenditGateway(config);
        } else {
            throw new Error(`Unsupported payment gateway: ${gateway}`);
        }
    }
}

/**
 * Xendit Payment Gateway
 */
class XenditGateway {
    /**
     * Constructor
     *
     * @param {object} config - Configuration for Xendit
     * @param {string} config.publicKey - Xendit Public Key
     * @param {boolean} config.isProduction - Whether to use production environment
     */
    constructor(config) {
        this.publicKey = config.publicKey;
        this.isProduction = config.isProduction;
        this.scriptLoaded = false;

        // Load Xendit script
        this.loadScript();
    }

    /**
     * Load Xendit script
     */
    loadScript() {
        if (this.scriptLoaded) return Promise.resolve();

        return new Promise((resolve, reject) => {
            const script = document.createElement("script");
            script.src = "https://js.xendit.co/v1/xendit.min.js";
            script.async = true;

            script.onload = () => {
                this.scriptLoaded = true;
                Xendit.setPublishableKey(this.publicKey);
                resolve();
            };

            script.onerror = () => {
                reject(new Error("Failed to load Xendit script"));
            };

            document.head.appendChild(script);
        });
    }

    /**
     * Pay with Xendit
     *
     * @param {string} invoiceUrl - Xendit Invoice URL
     */
    pay(invoiceUrl) {
        // Redirect to Xendit Invoice URL
        window.location.href = invoiceUrl;
    }
}

// Export the PaymentGateway class
window.PaymentGateway = PaymentGateway;

// Log when the script is loaded
console.log("Payment Gateway script loaded successfully");

// Make sure the PaymentGateway class is available globally
document.addEventListener("DOMContentLoaded", function () {
    if (!window.PaymentGateway) {
        console.error("PaymentGateway is not defined after DOMContentLoaded");
        window.PaymentGateway = PaymentGateway;
    } else {
        console.log("PaymentGateway is already defined");
    }
});
