<?php

namespace App\Http\Controllers;

use App\Models\KategoriPekerjaan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log; // Tambahkan ini

class KategoriPekerjaanController extends Controller
{
    // Method untuk mengambil semua data kategori
    public function all(Request $request)
    {
        $projectId = $request->input('project_id');

        if (!$projectId) {
            return response()->json(['error' => 'Project ID tidak ditemukan.'], 400);
        }

        $kategoriPekerjaan = KategoriPekerjaan::where('project_id', $projectId)->get();

        return response()->json($kategoriPekerjaan);
    }

    // Method untuk mengambil semua data kategori beserta item di dalamnya
    public function withItems(Request $request)
    {
        $projectId = $request->input('project_id');

        if (!$projectId) {
            return response()->json(['error' => 'Project ID tidak ditemukan.'], 400);
        }

        $kategoriPekerjaan = KategoriPekerjaan::where('project_id', $projectId)
            ->with('items') // Eager load items
            ->get();

        return response()->json($kategoriPekerjaan);
    }

    // Method untuk menyimpan kategori baru
    public function store(Request $request)
    {
        Log::info('Data diterima:', $request->all()); // Log data yang diterima

        try {
            // Validasi data
            $validated = $request->validate([
                'nama_kategori' => 'required|string|max:255',
                'project_id' => 'required|exists:projects,id', // Validasi project_id
            ]);

            // Simpan data kategori pekerjaan
            $kategoriPekerjaan = KategoriPekerjaan::create($validated);

            return response()->json([
                'success' => true,
                'message' => 'Kategori pekerjaan berhasil disimpan.',
                'data' => $kategoriPekerjaan,
            ]);
        } catch (\Exception $e) {
            Log::error('Error saat menyimpan kategori:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan kategori.',
            ], 500);
        }
    }

    // Method untuk mengupdate kategori yang sudah ada
    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'nama_kategori' => 'required|string|max:255',
        ]);

        $kategori = KategoriPekerjaan::findOrFail($id);
        $kategori->update(['nama_kategori' => $validated['nama_kategori']]);

        return response()->json([
            'message' => 'Kategori berhasil diupdate',
            'data' => $kategori
        ]);
    }

    // Method untuk menghapus kategori
    public function destroy($id)
    {
        $kategori = KategoriPekerjaan::findOrFail($id);
        $kategori->delete();

        return response()->json([
            'message' => 'Kategori berhasil dihapus'
        ]);
    }

    public function updateOrder(Request $request)
    {
        $orderData = $request->input('order');
        if (!is_array($orderData)) {
            return response()->json(['message' => 'Data urutan tidak valid.'], 400);
        }

        foreach ($orderData as $item) {
            $kategori = KategoriPekerjaan::find($item['id']);
            if ($kategori) {
                $kategori->order = $item['order'];
                $kategori->save();
            }
        }

        return response()->json(['message' => 'Urutan kategori berhasil diperbarui.']);
    }
}
