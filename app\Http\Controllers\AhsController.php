<?php

namespace App\Http\Controllers;

use App\Models\Ahs;
use App\Models\AhspDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class AhsController extends Controller
{
    /**
     * Menampilkan halaman summary AHS beserta data item untuk modal.
     */
    public function index(Request $request)
    {
        $search = $request->input('search');
        $query = Ahs::query();

        // Filter data AHS berdasarkan role pengguna
        if (Auth::user()->role === 'admin') {
            // Admin melihat semua data
            // No additional filter
        } else {
            // Customer hanya melihat data yang dibuat oleh admin dan data miliknya sendiri
            $query->where(function ($q) {
                $q->where('creator_role', 'admin')
                    ->orWhere('created_by', Auth::id());
            });
        }

        // Terapkan pencarian jika ada
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('judul', 'like', "%{$search}%")
                    ->orWhere('satuan', 'like', "%{$search}%")
                    ->orWhere('sumber', 'like', "%{$search}%")
                    ->orWhere('kode', 'like', "%{$search}%");
            });
        }

        // Urutkan data berdasarkan tanggal terbaru
        $query->orderBy('created_at', 'desc');

        // Paginate hasil query
        $ahsRecords = $query->paginate(15);

        // Append search query to pagination links
        if ($search) {
            $ahsRecords->appends(['search' => $search]);
        }

        // Ambil data untuk modal detail input
        $bahanItems = \App\Models\Bahan::all();
        $upahItems  = \App\Models\Upah::all();
        $alatItems  = \App\Models\Alat::all();

        return view('ahsp.index', compact('ahsRecords', 'bahanItems', 'upahItems', 'alatItems'));
    }

    /**
     * Tampilkan semua data AHS berdasarkan akses
     */
    public function all()
    {
        // Jika user adalah admin, tampilkan semua data
        if (Auth::user()->role === 'admin') {
            $ahsList = Ahs::orderBy('created_at', 'desc')->get();
        } else {
            // Jika user adalah customer, tampilkan data yang dibuat admin (creator_role = admin)
            // dan data yang dibuat oleh customer itu sendiri
            $ahsList = Ahs::where(function ($query) {
                $query->where('creator_role', 'admin')
                    ->orWhere('created_by', Auth::id());
            })->orderBy('created_at', 'desc')->get();
        }

        return response()->json($ahsList);
    }

    /**
     *  data detail AHS melalui Ajax.
     */
    public function storeDetail(Request $request)
    {
        $validated = $request->validate([
            'judul'     => 'required|string',
            'satuan'    => 'required|string',
            'kode'      => 'required|string',
            'overhead'  => 'required|numeric|min:0',
            'sumber'    => 'required|string',
            'detail'    => 'required|array',
        ]);

        $detailData = $validated['detail'];
        $subtotal = 0;

        foreach (['bahan', 'upah', 'alat'] as $kategori) {
            if (isset($detailData[$kategori]) && is_array($detailData[$kategori])) {
                foreach ($detailData[$kategori] as $item) {
                    $subtotal += $item['harga_satuan'];
                }
            }
        }

        $overheadPercent = $validated['overhead'];
        $overhead = $subtotal * ($overheadPercent / 100);
        $grandTotal = $subtotal + $overhead;

        // Set source based on user input rather than overriding for non-admin users
        $source = $validated['sumber'];

        // Simpan summary AHSP (termasuk judul, kode, dan grand total)
        $ahs = Ahs::create([
            'kode'        => $validated['kode'],
            'judul'       => $validated['judul'],
            'satuan'      => $validated['satuan'],
            'overhead'    => $overheadPercent,
            'grand_total' => $grandTotal,
            'sumber'      => $source,
            'user_id'     => Auth::id(),
            'creator_role' => Auth::user()->role,
            'created_by'  => Auth::id(),
        ]);

        // Simpan detail ke tabel ahsp_details (tambahkan field 'judul')
        foreach (['bahan', 'upah', 'alat'] as $kategori) {
            if (isset($detailData[$kategori]) && is_array($detailData[$kategori])) {
                foreach ($detailData[$kategori] as $item) {
                    AhspDetail::create([
                        'ahs_id'       => $ahs->id,
                        'kategori'     => $kategori,
                        'item_id'      => $item['item_id'],
                        'kode'         => $validated['kode'], // menggunakan kode summary
                        'judul'        => $validated['judul'], // simpan judul juga ke detail
                        'item_text'    => $item['item_text'] ?? '',
                        'satuan'       => $item['satuan'] ?? '',
                        'harga_dasar'  => $item['harga_dasar'] ?? 0,
                        'koefisien'    => $item['koefisien'],
                        'harga_satuan' => $item['harga_satuan'],
                    ]);
                }
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Data detail berhasil disimpan.',
            'data' => $ahs
        ]);
    }

    /**
     * Menghapus data summary AHS (dan detail terkait secara cascade).
     */
    public function destroy($id)
    {
        $ahs = Ahs::findOrFail($id);

        // Periksa apakah user berhak menghapus AHS ini
        if (Auth::user()->role !== 'admin' && $ahs->created_by !== Auth::id()) {
            return redirect()->route('ahs.index')->with('error', 'Anda tidak memiliki akses untuk menghapus AHS ini.');
        }

        $ahs->delete();
        return redirect()->route('ahs.index')->with('success', 'Data AHS berhasil dihapus.');
    }

    /**
     * Tampilkan detail AHS berdasarkan akses
     */
    public function getDetail($id)
    {
        $ahs = Ahs::findOrFail($id);

        // Periksa apakah user berhak mengakses
        if (
            Auth::user()->role !== 'admin' &&
            $ahs->creator_role !== 'admin' &&
            $ahs->created_by !== Auth::id()
        ) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $bahan = [];
        $upah  = [];
        $alat  = [];

        foreach ($ahs->details as $detail) {
            $kategori = strtolower($detail->kategori);
            if ($kategori === 'bahan') {
                if (is_null($detail->item_text) || is_null($detail->satuan) || is_null($detail->harga_dasar)) {
                    $item = \App\Models\Bahan::find($detail->item_id);
                    if ($item) {
                        $detail->item_text = $item->uraian_bahan;
                        $detail->satuan = $item->satuan;
                        $detail->harga_dasar = $item->harga_bahan;
                    }
                }
                $bahan[] = $detail;
            } elseif ($kategori === 'upah') {
                if (is_null($detail->item_text) || is_null($detail->satuan) || is_null($detail->harga_dasar)) {
                    $item = \App\Models\Upah::find($detail->item_id);
                    if ($item) {
                        $detail->item_text = $item->uraian_tenaga;
                        $detail->satuan = $item->satuan;
                        $detail->harga_dasar = $item->harga;
                    }
                }
                $upah[] = $detail;
            } elseif ($kategori === 'alat') {
                if (is_null($detail->item_text) || is_null($detail->satuan) || is_null($detail->harga_dasar)) {
                    $item = \App\Models\Alat::find($detail->item_id);
                    if ($item) {
                        $detail->item_text = $item->uraian_alat;
                        $detail->satuan = $item->satuan;
                        $detail->harga_dasar = $item->harga_alat;
                    }
                }
                $alat[] = $detail;
            }
        }

        return response()->json([
            'kode' => $ahs->kode,
            'sumber' => $ahs->sumber,
            'overhead' => $ahs->overhead,
            'bahan' => $bahan,
            'upah'  => $upah,
            'alat'  => $alat,
        ]);
    }

    public function updateDetail(Request $request, $id)
    {
        $validated = $request->validate([
            'judul'  => 'required|string',
            'satuan'  => 'required|string',
            'kode'   => 'required|string',
            'overhead' => 'required|numeric|min:0',
            'sumber' => 'required|string',
            'detail' => 'required|array',
        ]);

        $detailData = $validated['detail'];
        $subtotal = 0;

        foreach (['bahan', 'upah', 'alat'] as $kategori) {
            if (isset($detailData[$kategori]) && is_array($detailData[$kategori])) {
                foreach ($detailData[$kategori] as $item) {
                    $subtotal += $item['harga_satuan'];
                }
            }
        }

        $overheadPercent = $validated['overhead'];
        $overhead = $subtotal * ($overheadPercent / 100);
        $grandTotal = $subtotal + $overhead;

        // Set source based on user input rather than overriding for non-admin users
        $source = $validated['sumber'];

        $ahs = Ahs::findOrFail($id);

        // Ubah logika: Hanya tambahkan AHS baru jika pembuat aslinya adalah admin dan user saat ini adalah customer
        $currentUserIsAdmin = Auth::user()->role === 'admin';
        $originalCreatorIsAdmin = $ahs->creator_role === 'admin';

        // Cek apakah ada flag create_new_ahs dari request
        $createNewAhs = $request->has('create_new_ahs') ? $request->input('create_new_ahs') : false;

        // Buat data AHSP baru jika:
        // 1. Pembuat aslinya admin dan user saat ini customer, ATAU
        // 2. Flag create_new_ahs bernilai true dan user saat ini bukan admin
        if (($originalCreatorIsAdmin && !$currentUserIsAdmin) || ($createNewAhs && !$currentUserIsAdmin)) {
            $ahs = Ahs::create([
                'kode'        => $validated['kode'],
                'judul'       => $validated['judul'],
                'satuan'      => $validated['satuan'],
                'overhead'    => $overheadPercent,
                'grand_total' => $grandTotal,
                'sumber'      => $source,
                'user_id'     => Auth::id(),
                'creator_role' => Auth::user()->role,
                'created_by'  => Auth::id(),
            ]);

            // Update item_pekerjaans dengan ahs_id baru
            \App\Models\ItemPekerjaan::where('ahs_id', $id)->update([
                'ahs_id' => $ahs->id,
                'harga_satuan' => $grandTotal,
                'harga_total' => DB::raw('volume * ' . $grandTotal),
            ]);
        } else {
            // Selain itu, update data yang ada
            $ahs->update([
                'kode'        => $validated['kode'],
                'judul'       => $validated['judul'],
                'satuan'      => $validated['satuan'],
                'overhead'    => $overheadPercent,
                'grand_total' => $grandTotal,
                'sumber'      => $source,
                'user_id'     => Auth::id(),
                'creator_role' => Auth::user()->role,
                'created_by'  => Auth::id(),
            ]);

            // Hapus detail lama
            $ahs->details()->delete();
        }

        foreach (['bahan', 'upah', 'alat'] as $kategori) {
            if (isset($detailData[$kategori]) && is_array($detailData[$kategori])) {
                foreach ($detailData[$kategori] as $item) {
                    AhspDetail::create([
                        'ahs_id'       => $ahs->id,
                        'kategori'     => $kategori,
                        'item_id'      => $item['item_id'],
                        'kode'         => $validated['kode'],
                        'judul'        => $validated['judul'],
                        'item_text'    => $item['item_text'] ?? '',
                        'satuan'       => $item['satuan'] ?? '',
                        'harga_dasar'  => $item['harga_dasar'] ?? 0,
                        'koefisien'    => $item['koefisien'],
                        'harga_satuan' => $item['harga_satuan'],
                    ]);
                }
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Data AHSP berhasil diperbarui.',
            'data' => $ahs
        ]);
    }

    public function updateDetail1(Request $request, $id)
    {
        $validated = $request->validate([
            'judul'  => 'required|string',
            'satuan'  => 'required|string',
            'kode'   => 'required|string',
            'overhead' => 'required|numeric|min:0',
            'sumber' => 'required|string',
            'detail' => 'required|array',
        ]);

        $detailData = $validated['detail'];
        $subtotal = 0;

        foreach (['bahan', 'upah', 'alat'] as $kategori) {
            if (isset($detailData[$kategori]) && is_array($detailData[$kategori])) {
                foreach ($detailData[$kategori] as $item) {
                    $subtotal += $item['harga_satuan'];
                }
            }
        }

        $overheadPercent = $validated['overhead'];
        $overhead = $subtotal * ($overheadPercent / 100);
        $grandTotal = $subtotal + $overhead;

        // Set source based on user input rather than overriding for non-admin users
        $source = $validated['sumber'];

        $ahs = Ahs::findOrFail($id);

        // Ubah logika: Hanya tambahkan AHS baru jika pembuat aslinya adalah admin dan user saat ini adalah customer
        $currentUserIsAdmin = Auth::user()->role === 'admin';
        $originalCreatorIsAdmin = $ahs->creator_role === 'admin';

        // Cek apakah ada flag create_new_ahs dari request
        $createNewAhs = $request->has('create_new_ahs') ? $request->input('create_new_ahs') : false;

        // Buat data AHSP baru jika:
        // 1. Pembuat aslinya admin dan user saat ini customer, ATAU
        // 2. Flag create_new_ahs bernilai true dan user saat ini bukan admin
        if (($originalCreatorIsAdmin && !$currentUserIsAdmin) || ($createNewAhs && !$currentUserIsAdmin)) {
            $ahs = Ahs::create([
                'kode'        => $validated['kode'],
                'judul'       => $validated['judul'],
                'satuan'      => $validated['satuan'],
                'overhead'    => $overheadPercent,
                'grand_total' => $grandTotal,
                'sumber'      => $source,
                'user_id'     => Auth::id(),
                'creator_role' => Auth::user()->role,
                'created_by'  => Auth::id(),
            ]);

            // Update item_pekerjaans dengan ahs_id baru
            \App\Models\ItemPekerjaan::where('ahs_id', $id)->update([
                'ahs_id' => $ahs->id,
                'harga_satuan' => $grandTotal,
                'harga_total' => DB::raw('volume * ' . $grandTotal),
            ]);
        } else {
            // Selain itu, update data yang ada
            $ahs->update([
                'kode'        => $validated['kode'],
                'judul'       => $validated['judul'],
                'satuan'      => $validated['satuan'],
                'overhead'    => $overheadPercent,
                'grand_total' => $grandTotal,
                'sumber'      => $source,
                'user_id'     => Auth::id(),
                'creator_role' => Auth::user()->role,
                'created_by'  => Auth::id(),
            ]);

            // Hapus detail lama
            $ahs->details()->delete();
        }

        foreach (['bahan', 'upah', 'alat'] as $kategori) {
            if (isset($detailData[$kategori]) && is_array($detailData[$kategori])) {
                foreach ($detailData[$kategori] as $item) {
                    AhspDetail::create([
                        'ahs_id'       => $ahs->id,
                        'kategori'     => $kategori,
                        'item_id'      => $item['item_id'],
                        'kode'         => $validated['kode'],
                        'judul'        => $validated['judul'],
                        'item_text'    => $item['item_text'] ?? '',
                        'satuan'       => $item['satuan'] ?? '',
                        'harga_dasar'  => $item['harga_dasar'] ?? 0,
                        'koefisien'    => $item['koefisien'],
                        'harga_satuan' => $item['harga_satuan'],
                    ]);
                }
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Data AHSP berhasil diperbarui.',
            'data' => $ahs
        ]);
    }

    /**
     * Update an item and all associated AHSP details with the same item_id
     */
    public function updateItem(Request $request, $id)
    {
        try {
            // Tambahkan delay simulasi untuk menampilkan loading (DEMO ONLY - HAPUS DI PRODUKSI)
            usleep(1000000); // delay 1 detik

            $validated = $request->validate([
                'originalItemId' => 'required|integer',
                'newItemId' => 'required|integer',
                'detail' => 'required|array',
            ]);

            $ahs = Ahs::findOrFail($id);
            $originalItemId = $validated['originalItemId'];
            $newItemId = $validated['newItemId'];
            $detail = $validated['detail'];
            $kategori = $detail['kategori'];

            // Check if we should update the source to "Empiris"
            // Only update source if explicitly requested (for koefisien changes)
            $updateSumber = isset($detail['update_sumber']) ? $detail['update_sumber'] : false;

            // Check if we should create a new record instead of updating
            $createNewDetail = isset($detail['create_new_detail']) ? $detail['create_new_detail'] : false;

            // First, find the current detail in this AHS
            $ahsDetail = AhspDetail::where('ahs_id', $ahs->id)
                ->where('kategori', $kategori)
                ->where('item_id', $originalItemId)
                ->first();

            if ($ahsDetail) {
                if ($createNewDetail) {
                    // Create a new detail record instead of updating the existing one
                    $newDetail = AhspDetail::create([
                        'ahs_id' => $ahs->id,
                        'kategori' => $kategori,
                        'item_id' => $newItemId,
                        'item_text' => $detail['item_text'],
                        'satuan' => $detail['satuan'],
                        'harga_dasar' => $detail['harga_dasar'],
                        'koefisien' => $detail['koefisien'],
                        'harga_satuan' => $detail['harga_satuan'],
                    ]);

                    // Delete the old detail record to avoid duplicates
                    $ahsDetail->delete();
                } else {
                    // Update existing record (original behavior)
                    $ahsDetail->update([
                        'item_id' => $newItemId,
                        'item_text' => $detail['item_text'],
                        'satuan' => $detail['satuan'],
                        'harga_dasar' => $detail['harga_dasar'],
                        'koefisien' => $detail['koefisien'],
                        'harga_satuan' => $detail['harga_satuan'],
                    ]);
                }

                // Update the AHS's source to Empiris ONLY if koefisien changed
                if ($updateSumber) {
                    $ahs->update(['sumber' => 'Empiris']);
                }

                // Now update all other details with the same original item_id
                // This batch updates all other AHSP details with the same item ID
                if ($createNewDetail) {
                    // For create_new_detail, don't automatically update other items
                    // as each update is meant to create an independent new record
                } else {
                    // Original behavior: update all related details
                    AhspDetail::where('item_id', $originalItemId)
                        ->where('kategori', $kategori)
                        ->whereNot('id', $ahsDetail->id) // Exclude the one we just updated
                        ->update([
                            'item_id' => $newItemId,
                            'item_text' => $detail['item_text'],
                            'satuan' => $detail['satuan'],
                            'harga_dasar' => $detail['harga_dasar'],
                        ]);
                }

                // Update all AHS with these details to have "Empiris" as source
                // ONLY if koefisien was changed
                if ($updateSumber) {
                    $affectedAhsIds = AhspDetail::where('item_id', $newItemId)
                        ->where('kategori', $kategori)
                        ->pluck('ahs_id')
                        ->unique();

                    Ahs::whereIn('id', $affectedAhsIds)->update(['sumber' => 'Empiris']);
                }

                // Recalculate harga_satuan for all affected details
                $affectedDetails = AhspDetail::where('item_id', $newItemId)
                    ->where('kategori', $kategori)
                    ->get();

                foreach ($affectedDetails as $affectedDetail) {
                    $affectedDetail->harga_satuan = $affectedDetail->harga_dasar * $affectedDetail->koefisien;
                    $affectedDetail->save();
                }

                return response()->json([
                    'success' => true,
                    'message' => $createNewDetail ? 'Item baru berhasil dibuat' : 'Item berhasil diperbarui dan semua referensi diperbarui',
                    'updated_count' => $affectedDetails->count()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Detail item tidak ditemukan'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }
}
