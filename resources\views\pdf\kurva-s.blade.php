@if(isset($timeSchedules) && count($timeSchedules) > 0)
<div class="page-break"></div>

<!-- <PERSON><PERSON> S -->
<h2>KURVA S</h2>
@php
    // Determine project start and end dates
    $projectStartDate = $timeSchedules->min('tanggal_mulai');
    $projectEndDate = $timeSchedules->max('tanggal_selesai');

    // Calculate total weeks
    $totalWeeks = ceil($projectStartDate->diffInDays($projectEndDate) / 7) + 1;
    $totalWeeks = min($totalWeeks, 52); // Limit to 52 weeks (1 year)

    // Generate week labels
    $weekLabels = [];
    $currentDate = clone $projectStartDate;
    for ($i = 0; $i < $totalWeeks; $i++) {
        $weekEnd = (clone $currentDate)->addDays(6);
        $weekLabels[] = $currentDate->format('d/m') . '-' . $weekEnd->format('d/m');
        $currentDate->addDays(7);
    }

    // Calculate cumulative progress per week
    $weeklyProgress = array_fill(0, $totalWeeks, 0);
    $weeklyPlan = array_fill(0, $totalWeeks, 0);

    foreach ($timeSchedules as $schedule) {
        $scheduleStart = $schedule->tanggal_mulai;
        $scheduleEnd = $schedule->tanggal_selesai;
        $scheduleDuration = $scheduleStart->diffInDays($scheduleEnd) + 1;
        $scheduleBobot = $schedule->bobot;
        $scheduleProgress = $schedule->progress;

        // Calculate daily weight
        $dailyWeight = $scheduleDuration > 0 ? $scheduleBobot / $scheduleDuration : 0;

        // Distribute weight across weeks
        for ($i = 0; $i < $totalWeeks; $i++) {
            $weekStart = (clone $projectStartDate)->addDays($i * 7);
            $weekEnd = (clone $weekStart)->addDays(6);

            // Check if schedule overlaps with this week
            if ($scheduleStart <= $weekEnd && $scheduleEnd >= $weekStart) {
                // Calculate overlap days
                $overlapStart = max($scheduleStart, $weekStart);
                $overlapEnd = min($scheduleEnd, $weekEnd);
                $overlapDays = $overlapStart->diffInDays($overlapEnd) + 1;

                // Add weight to this week
                $weekWeight = $dailyWeight * $overlapDays;
                $weeklyPlan[$i] += $weekWeight;

                // Add actual progress to this week (proportional to completion)
                $weeklyProgress[$i] += $weekWeight * $scheduleProgress / 100;
            }
        }
    }

    // Calculate cumulative values
    $cumulativePlan = [];
    $cumulativeProgress = [];
    $runningPlan = 0;
    $runningProgress = 0;

    for ($i = 0; $i < $totalWeeks; $i++) {
        $runningPlan += $weeklyPlan[$i];
        $runningProgress += $weeklyProgress[$i];
        $cumulativePlan[$i] = $runningPlan;
        $cumulativeProgress[$i] = $runningProgress;
    }
@endphp

<table>
    <thead>
        <tr>
            <th width="20%">Minggu</th>
            <th width="20%">Rencana (%)</th>
            <th width="20%">Kumulatif Rencana (%)</th>
            <th width="20%">Realisasi (%)</th>
            <th width="20%">Kumulatif Realisasi (%)</th>
        </tr>
    </thead>
    <tbody>
        @for($i = 0; $i < $totalWeeks; $i++)
            <tr>
                <td class="text-center">{{ $weekLabels[$i] }}</td>
                <td class="text-center">{{ number_format($weeklyPlan[$i], 2) }}</td>
                <td class="text-center">{{ number_format($cumulativePlan[$i], 2) }}</td>
                <td class="text-center">{{ number_format($weeklyProgress[$i], 2) }}</td>
                <td class="text-center">{{ number_format($cumulativeProgress[$i], 2) }}</td>
            </tr>
        @endfor
    </tbody>
    <tfoot>
        <tr class="total-row">
            <td class="text-right">TOTAL</td>
            <td class="text-center">{{ number_format(array_sum($weeklyPlan), 2) }}</td>
            <td class="text-center">{{ number_format(end($cumulativePlan), 2) }}</td>
            <td class="text-center">{{ number_format(array_sum($weeklyProgress), 2) }}</td>
            <td class="text-center">{{ number_format(end($cumulativeProgress), 2) }}</td>
        </tr>
    </tfoot>
</table>

<div class="page-break"></div>

<!-- Grafik Kurva S -->
<h2>GRAFIK KURVA S</h2>

<table style="width: 100%; border: none;">
    <tr>
        <td style="border: none;">
            <div style="width: 100%; height: 300px; border: 1px solid #000; position: relative;">
                <!-- Grid lines and labels -->
                @for($i = 0; $i <= 10; $i++)
                    <div style="position: absolute; left: 40px; right: 10px; bottom: {{ $i * 10 }}%; border-top: 1px dashed #ccc;"></div>
                    <div style="position: absolute; left: 5px; bottom: {{ $i * 10 }}%; margin-bottom: -7px; font-size: 8pt;">{{ $i * 10 }}%</div>
                @endfor

                <!-- X-axis labels -->
                @foreach($weekLabels as $index => $week)
                    @if($index % max(1, intval($totalWeeks / 5)) === 0 || $index == $totalWeeks - 1)
                        <div style="position: absolute; left: {{ 40 + ($index / max(1, $totalWeeks - 1) * (100 - 50)) }}%; bottom: -20px; font-size: 7pt; text-align: center; width: 40px; margin-left: -20px;">{{ $week }}</div>
                    @endif
                @endforeach

                <!-- Draw the lines manually using HTML elements -->
                @php
                    // Draw plan line (blue) using HTML elements
                    $prevX = null;
                    $prevY = null;

                    for ($i = 0; $i < $totalWeeks; $i++) {
                        $x = 40 + ($i / max(1, $totalWeeks - 1) * (100 - 50));
                        $y = 100 - min(100, $cumulativePlan[$i]);

                        if ($prevX !== null) {
                            // Calculate line length and angle
                            $length = sqrt(pow($x - $prevX, 2) + pow($y - $prevY, 2));
                            $angle = atan2($y - $prevY, $x - $prevX) * 180 / M_PI;

                            echo '<div style="position: absolute; left: ' . $prevX . '%; top: ' . $prevY . '%; width: ' . $length . '%; height: 2px; background-color: blue; transform: rotate(' . $angle . 'deg); transform-origin: 0 0;"></div>';
                        }

                        // Draw point
                        echo '<div style="position: absolute; left: ' . $x . '%; top: ' . $y . '%; width: 4px; height: 4px; background-color: blue; border-radius: 50%; margin-left: -2px; margin-top: -2px;"></div>';

                        $prevX = $x;
                        $prevY = $y;
                    }

                    // Draw progress line (red) using HTML elements
                    $prevX = null;
                    $prevY = null;

                    for ($i = 0; $i < $totalWeeks; $i++) {
                        $x = 40 + ($i / max(1, $totalWeeks - 1) * (100 - 50));
                        $y = 100 - min(100, $cumulativeProgress[$i]);

                        if ($prevX !== null) {
                            // Calculate line length and angle
                            $length = sqrt(pow($x - $prevX, 2) + pow($y - $prevY, 2));
                            $angle = atan2($y - $prevY, $x - $prevX) * 180 / M_PI;

                            echo '<div style="position: absolute; left: ' . $prevX . '%; top: ' . $prevY . '%; width: ' . $length . '%; height: 2px; background-color: red; transform: rotate(' . $angle . 'deg); transform-origin: 0 0;"></div>';
                        }

                        // Draw point
                        echo '<div style="position: absolute; left: ' . $x . '%; top: ' . $y . '%; width: 4px; height: 4px; background-color: red; border-radius: 50%; margin-left: -2px; margin-top: -2px;"></div>';

                        $prevX = $x;
                        $prevY = $y;
                    }
                @endphp

                <!-- Y-axis line -->
                <div style="position: absolute; left: 40px; top: 0; bottom: 0; border-left: 1px solid #000;"></div>

                <!-- X-axis line -->
                <div style="position: absolute; left: 40px; right: 10px; bottom: 0; border-top: 1px solid #000;"></div>
            </div>
        </td>
    </tr>
</table>

<!-- Legend -->
<table style="width: 100%; border: none; margin-top: 10px;">
    <tr>
        <td style="border: none; text-align: center;">
            <span style="display: inline-block; width: 20px; height: 2px; background-color: blue; vertical-align: middle;"></span>
            <span style="vertical-align: middle; margin-right: 20px;"> Rencana</span>
            <span style="display: inline-block; width: 20px; height: 2px; background-color: red; vertical-align: middle;"></span>
            <span style="vertical-align: middle;"> Realisasi</span>
        </td>
    </tr>
</table>
@endif
