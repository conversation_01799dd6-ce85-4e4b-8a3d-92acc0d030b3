<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ahs_alat', function (Blueprint $table) {
            $table->unsignedBigInteger('ahs_id');
            $table->unsignedBigInteger('alat_id');
            $table->decimal('koefisien', 10, 4)->default(0);
            $table->timestamps();

            $table->primary(['ahs_id', 'alat_id']);
            $table->foreign('ahs_id')->references('id')->on('ahs')->onDelete('cascade');
            $table->foreign('alat_id')->references('id')->on('alats')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ahs_alat');
    }
};
