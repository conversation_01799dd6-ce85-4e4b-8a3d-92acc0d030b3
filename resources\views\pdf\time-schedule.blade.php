<!-- Time Schedule Table -->
@if(isset($timeSchedules) && count($timeSchedules) > 0)
<h2>TIME SCHEDULE (KURVA S)</h2>
<table>
    <thead>
        <tr>
            <th width="5%">No</th>
            <th width="40%"><PERSON><PERSON><PERSON></th>
            <th width="10%">Bobot (%)</th>
            <th width="15%">Tanggal Mulai</th>
            <th width="15%">Tanggal Selesai</th>
            <th width="15%">Progress (%)</th>
        </tr>
    </thead>
    <tbody>
        @php
            $itemNo = 1;
            $totalBobot = 0;
            $totalProgress = 0;
        @endphp
        @foreach($timeSchedules as $schedule)
            <tr>
                <td class="text-center">{{ $itemNo++ }}</td>
                <td>{{ $schedule->nama_kegiatan }}</td>
                <td class="text-center">{{ number_format($schedule->bobot, 2) }}</td>
                <td class="text-center">{{ $schedule->tanggal_mulai->format('d/m/Y') }}</td>
                <td class="text-center">{{ $schedule->tanggal_selesai->format('d/m/Y') }}</td>
                <td class="text-center">{{ number_format($schedule->progress, 2) }}</td>
            </tr>
            @php
                $totalBobot += $schedule->bobot;
                $totalProgress += ($schedule->bobot * $schedule->progress / 100);
            @endphp
        @endforeach
    </tbody>
    <tfoot>
        <tr class="total-row">
            <td colspan="2" class="text-right">TOTAL</td>
            <td class="text-center">{{ number_format($totalBobot, 2) }}</td>
            <td colspan="2" class="text-right">PROGRESS TOTAL</td>
            <td class="text-center">{{ $totalBobot > 0 ? number_format(($totalProgress / $totalBobot) * 100, 2) : '0.00' }}%</td>
        </tr>
    </tfoot>
</table>
@else
<h2>TIME SCHEDULE (KURVA S)</h2>
<p class="text-center">Tidak ada data time schedule</p>
@endif

@if(isset($timeSchedules) && count($timeSchedules) > 0)
<div class="page-break"></div>

<!-- Gantt Chart -->
<h2>GANTT CHART</h2>
@php
    // Determine project start and end dates
    $projectStartDate = $timeSchedules->min('tanggal_mulai');
    $projectEndDate = $timeSchedules->max('tanggal_selesai');

    // Calculate total weeks
    $totalWeeks = ceil($projectStartDate->diffInDays($projectEndDate) / 7) + 1;
    $totalWeeks = min($totalWeeks, 52); // Limit to 52 weeks (1 year)

    // Generate week labels
    $weekLabels = [];
    $currentDate = clone $projectStartDate;
    for ($i = 0; $i < $totalWeeks; $i++) {
        $weekEnd = (clone $currentDate)->addDays(6);
        $weekLabels[] = $currentDate->format('d/m') . '-' . $weekEnd->format('d/m');
        $currentDate->addDays(7);
    }
@endphp

<table>
    <thead>
        <tr>
            <th width="5%">No</th>
            <th width="35%">Uraian Pekerjaan</th>
            <th width="10%">Bobot (%)</th>
            @foreach($weekLabels as $week)
                <th width="{{ 50 / $totalWeeks }}%">{{ $week }}</th>
            @endforeach
        </tr>
    </thead>
    <tbody>
        @php $itemNo = 1; @endphp
        @foreach($timeSchedules as $schedule)
            <tr>
                <td class="text-center">{{ $itemNo++ }}</td>
                <td>{{ $schedule->nama_kegiatan }}</td>
                <td class="text-center">{{ number_format($schedule->bobot, 2) }}</td>

                @php
                    $scheduleStart = $schedule->tanggal_mulai;
                    $scheduleEnd = $schedule->tanggal_selesai;
                @endphp

                @for($i = 0; $i < $totalWeeks; $i++)
                    @php
                        $weekStart = (clone $projectStartDate)->addDays($i * 7);
                        $weekEnd = (clone $weekStart)->addDays(6);

                        $isActive = ($scheduleStart <= $weekEnd && $scheduleEnd >= $weekStart);
                    @endphp

                    <td class="text-center" style="{{ $isActive ? 'background-color: #ccc;' : '' }}">
                        @if($isActive)
                        &#9679;
                        @endif
                    </td>
                @endfor
            </tr>
        @endforeach
    </tbody>
</table>
@endif
