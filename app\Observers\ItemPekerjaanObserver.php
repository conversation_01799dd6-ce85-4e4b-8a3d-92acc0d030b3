<?php

namespace App\Observers;

use App\Models\ItemPekerjaan;
use App\Models\TimeSchedule;
use App\Models\KategoriPekerjaan;
use Illuminate\Support\Facades\Log;

class ItemPekerjaanObserver
{
    /**
     * Event ini akan dipanggil sebelum record disimpan (baik create maupun update).
     */
    public function saving(ItemPekerjaan $item)
    {
        // Pastikan nilai volume dan harga_satuan terkonversi ke numeric
        $volume = $item->volume ?? 0;
        $hargaSatuan = $item->harga_satuan ?? 0;
        $item->harga_total = $volume * $hargaSatuan;
    }

    /**
     * Event ini akan dipanggil setelah record disimpan (baik create maupun update).
     */
    public function saved(ItemPekerjaan $item)
    {
        // Perbarui bobot pada time schedule terkait setelah penyimpanan
        $this->updateTimeScheduleBobot($item);
    }

    /**
     * Handle the ItemPekerjaan "updated" event.
     */
    public function updated(ItemPekerjaan $itemPekerjaan)
    {
        // Periksa apakah harga_total, volume, atau harga_satuan berubah
        if (
            $itemPekerjaan->wasChanged('harga_total') ||
            $itemPekerjaan->wasChanged('volume') ||
            $itemPekerjaan->wasChanged('harga_satuan')
        ) {

            Log::info('ItemPekerjaan updated with new values', [
                'item_id' => $itemPekerjaan->id,
                'old_harga_total' => $itemPekerjaan->getOriginal('harga_total'),
                'new_harga_total' => $itemPekerjaan->harga_total,
                'old_volume' => $itemPekerjaan->getOriginal('volume'),
                'new_volume' => $itemPekerjaan->volume,
                'old_harga_satuan' => $itemPekerjaan->getOriginal('harga_satuan'),
                'new_harga_satuan' => $itemPekerjaan->harga_satuan
            ]);

            // Perbarui bobot pada time schedule terkait
            $this->updateTimeScheduleBobot($itemPekerjaan);
        }
    }

    /**
     * Update bobot pada time schedule terkait
     */
    private function updateTimeScheduleBobot(ItemPekerjaan $itemPekerjaan)
    {
        // Ambil project_id dari kategori pekerjaan
        $kategoriPekerjaan = KategoriPekerjaan::find($itemPekerjaan->kategori_pekerjaan_id);
        if (!$kategoriPekerjaan) {
            Log::error('Kategori pekerjaan not found', ['kategori_id' => $itemPekerjaan->kategori_pekerjaan_id]);
            return;
        }

        $projectId = $kategoriPekerjaan->project_id;

        // Ambil semua item pekerjaan dalam proyek
        $kategoriPekerjaans = KategoriPekerjaan::with('items')
            ->where('project_id', $projectId)
            ->get();

        // Hitung total nilai proyek
        $totalNilaiProyek = 0;
        foreach ($kategoriPekerjaans as $kategori) {
            foreach ($kategori->items as $item) {
                $totalNilaiProyek += $item->harga_total;
            }
        }

        // Jika total nilai proyek adalah 0, tidak ada yang bisa diperbarui
        if ($totalNilaiProyek <= 0) {
            Log::warning('Total nilai proyek is 0, cannot update bobot', ['project_id' => $projectId]);
            return;
        }

        // Ambil semua time schedule yang terkait dengan proyek
        $timeSchedules = TimeSchedule::where('project_id', $projectId)->get();

        // Kelompokkan time schedule berdasarkan item_pekerjaan_id
        $timeSchedulesByItem = [];
        foreach ($timeSchedules as $schedule) {
            if ($schedule->item_pekerjaan_id) {
                if (!isset($timeSchedulesByItem[$schedule->item_pekerjaan_id])) {
                    $timeSchedulesByItem[$schedule->item_pekerjaan_id] = [];
                }
                $timeSchedulesByItem[$schedule->item_pekerjaan_id][] = $schedule;
            }
        }

        // Perbarui bobot untuk setiap item pekerjaan
        foreach ($kategoriPekerjaans as $kategori) {
            foreach ($kategori->items as $item) {
                // Hitung bobot total berdasarkan proporsi harga_total terhadap total nilai proyek
                $totalBobot = ($item->harga_total / $totalNilaiProyek) * 100;
                $totalBobot = round($totalBobot, 2);

                // Jika ada time schedule untuk item ini
                if (isset($timeSchedulesByItem[$item->id])) {
                    $schedules = $timeSchedulesByItem[$item->id];
                    $totalScheduleBobot = array_sum(array_map(function ($schedule) {
                        return $schedule->bobot;
                    }, $schedules));

                    // Jika total bobot jadwal tidak sama dengan total bobot yang dihitung
                    if (abs($totalScheduleBobot - $totalBobot) > 0.01) {
                        // Jika hanya ada satu jadwal, perbarui bobotnya langsung
                        if (count($schedules) === 1) {
                            $schedule = $schedules[0];
                            $oldBobot = $schedule->bobot;
                            $schedule->bobot = $totalBobot;
                            $schedule->save();

                            Log::info('Updated time schedule bobot', [
                                'schedule_id' => $schedule->id,
                                'item_id' => $item->id,
                                'old_bobot' => $oldBobot,
                                'new_bobot' => $totalBobot
                            ]);
                        }
                        // Jika ada beberapa jadwal, distribusikan bobot secara proporsional
                        else if (count($schedules) > 1 && $totalScheduleBobot > 0) {
                            foreach ($schedules as $schedule) {
                                $proportion = $schedule->bobot / $totalScheduleBobot;
                                $newBobot = round($totalBobot * $proportion, 2);

                                $oldBobot = $schedule->bobot;
                                $schedule->bobot = $newBobot;
                                $schedule->save();

                                Log::info('Updated time schedule bobot (proportional)', [
                                    'schedule_id' => $schedule->id,
                                    'item_id' => $item->id,
                                    'proportion' => $proportion,
                                    'old_bobot' => $oldBobot,
                                    'new_bobot' => $newBobot
                                ]);
                            }
                        }
                    }
                }
            }
        }
    }
}
