import { getHeaderHtml, getFormHtml } from "./volumeTemplates.js";
import {
    generatePlusRows,
    generateMinusRows,
} from "./volumeLoadTemplatesNew.js";

// Objek global untuk menyimpan data modal volume
window.VolumeModal = {
    currentSatuan: null,
    currentItemId: null,
    currentCalculationMode: null,
    editingId: null, // untuk mode edit
    hargaSatuan: 0, // tambahkan properti untuk harga satuan item yang dipilih
};

// Fungsi untuk membuka modal volume dan menyimpan data harga satuan item yang dipilih
function openVolumeModal(satuan, itemId, hargaSatuan) {
    VolumeModal.currentSatuan = satuan;
    VolumeModal.currentItemId = itemId; // Simpan ID item (rab_id)
    VolumeModal.editingId = null; // Reset mode edit
    VolumeModal.hargaSatuan = hargaSatuan; // Simpan harga satuan untuk item yang dipilih
    generateVolumeTableHeader(satuan);
    loadVolumeData();
    document.getElementById("rabVolumeModal").classList.remove("hidden");

    // Tooltips removed
}
window.openVolumeModal = openVolumeModal;

// Fungsi untuk menutup modal volume
function closeVolumeModal() {
    document.getElementById("rabVolumeModal").classList.add("hidden");
    // Memanggil fungsi untuk memperbarui tampilan volume secara realtime
    updateVolumeDisplay(VolumeModal.currentItemId);
}
window.closeVolumeModal = closeVolumeModal;

// Fungsi untuk memperbarui tampilan volume secara realtime
function updateVolumeDisplay(itemId) {
    if (!itemId) return;

    fetch(`/item-pekerjaans/${itemId}`)
        .then((response) => {
            if (!response.ok) {
                throw new Error("Gagal mengambil data item");
            }
            return response.json();
        })
        .then((data) => {
            // Format volume dengan 2 angka desimal
            const formattedVolume = parseFloat(data.volume).toFixed(2);

            // Perbarui tampilan volume di baris item yang sesuai
            const itemRow = document.querySelector(
                `tr[data-item-id="${itemId}"]`
            );
            if (!itemRow) return;

            const volumeCell = itemRow.querySelector(".volume, .volume-cell");
            if (volumeCell) {
                // Update text content
                volumeCell.textContent = formattedVolume;
                // Update data attribute untuk perhitungan
                volumeCell.setAttribute("data-volume", data.volume);
            }

            // Format harga total dengan format mata uang (koma sebagai desimal)
            const formattedHargaTotal = Number(data.harga_total).toLocaleString(
                "id-ID",
                {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                }
            );

            // Perbarui tampilan harga total di baris item yang sesuai
            const hargaTotalCell = itemRow.querySelector(
                ".harga-total, .harga-total-cell"
            );
            if (hargaTotalCell) {
                // Update HTML dengan format yang benar
                const totalSpan = hargaTotalCell.querySelector(".float-right");
                if (totalSpan) {
                    totalSpan.textContent = formattedHargaTotal;
                } else {
                    hargaTotalCell.innerHTML = `
                        <span class="float-left">Rp.</span>
                        <span class="float-right">${formattedHargaTotal}</span>
                    `;
                }

                // Update data attribute untuk perhitungan grand total
                hargaTotalCell.setAttribute(
                    "data-harga-total",
                    data.harga_total
                );
            }

            // Trigger update grand total jika fungsi tersedia
            if (typeof window.calculateTotals === "function") {
                window.calculateTotals();
            } else if (typeof window.updateGrandTotal === "function") {
                window.updateGrandTotal();
            } else {
                // Fallback ke fungsi lama jika updateGrandTotal tidak tersedia
                recalculateRabTotal();
            }
        })
        .catch((error) => {
            console.error("Error updating volume display:", error);
            showCustomAlert(
                "Terjadi kesalahan saat memperbarui tampilan volume."
            );
        });
}
window.updateVolumeDisplay = updateVolumeDisplay;

// Fungsi untuk menghitung ulang total RAB
function recalculateRabTotal() {
    // Ambil semua sel harga total
    const hargaTotalCells = document.querySelectorAll(
        "tr[data-item-id] .harga-total, tr[data-item-id] .harga-total-cell"
    );
    let totalRab = 0;

    // Jumlahkan semua harga total
    hargaTotalCells.forEach((cell) => {
        // Coba ambil dari data attribute jika ada
        if (cell.hasAttribute("data-harga-total")) {
            totalRab += parseFloat(cell.getAttribute("data-harga-total")) || 0;
        } else {
            // Jika tidak ada data attribute, ambil dari text content
            // Bersihkan format mata uang dan konversi ke angka
            const rightSpan = cell.querySelector(".float-right");
            if (rightSpan) {
                const value = rightSpan.textContent
                    .replace(/\./g, "") // Hapus semua titik pemisah ribuan
                    .replace(/,/g, "."); // Ganti koma dengan titik untuk desimal
                totalRab += parseFloat(value) || 0;
            } else {
                const value = cell.textContent
                    .replace(/[^\d,-]/g, "")
                    .replace(",", ".");
                totalRab += parseFloat(value) || 0;
            }
        }
    });

    // Format jumlah harga dengan format mata uang
    const formattedJumlahHarga = Number(totalRab).toLocaleString("id-ID", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });

    // Perbarui tampilan jumlah harga
    const jumlahHargaCell = document.getElementById("jumlahHarga");
    if (jumlahHargaCell) {
        jumlahHargaCell.innerHTML = `
            <span class="float-left">Rp.</span>
            <span class="float-right">${formattedJumlahHarga}</span>
        `;
    }

    // Hitung PPN
    const ppnInput = document.getElementById("ppnInput");
    const ppnPercentage = ppnInput ? parseFloat(ppnInput.value) / 100 : 0;
    const ppnAmount = totalRab * ppnPercentage;

    // Format PPN dengan format mata uang
    const formattedPPN = Number(ppnAmount).toLocaleString("id-ID", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });

    // Perbarui tampilan PPN
    const ppnHargaCell = document.getElementById("ppnHarga");
    if (ppnHargaCell) {
        ppnHargaCell.innerHTML = `
            <span class="float-left">Rp.</span>
            <span class="float-right">${formattedPPN}</span>
        `;
    }

    // Hitung total harga dengan PPN
    const totalHargaWithPPN = totalRab + ppnAmount;

    // Format total harga dengan format mata uang
    const formattedTotalHarga = Number(totalHargaWithPPN).toLocaleString(
        "id-ID",
        {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }
    );

    // Perbarui tampilan total harga
    const totalHargaCell = document.getElementById("totalHarga");
    if (totalHargaCell) {
        totalHargaCell.innerHTML = `
            <span class="float-left">Rp.</span>
            <span class="float-right">${formattedTotalHarga}</span>
        `;
    }

    // Bulatkan ke atas (hingga ratusan ribu terdekat)
    const roundedTotal = Math.floor(totalHargaWithPPN / 1000) * 1000;

    // Format dibulatkan dengan format mata uang
    const formattedRoundedTotal = Number(roundedTotal).toLocaleString("id-ID", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    });

    // Perbarui tampilan dibulatkan
    const dibulatkanHargaCell = document.getElementById("dibulatkanHarga");
    if (dibulatkanHargaCell) {
        dibulatkanHargaCell.innerHTML = `
            <span class="float-left">Rp.</span>
            <span class="float-right">${formattedRoundedTotal}</span>
        `;
    }

    // Konversi angka ke terbilang (memanggil fungsi terbilang jika ada)
    if (typeof angkaTerbilang === "function") {
        const terbilangText = angkaTerbilang(roundedTotal) + " Rupiah";
        const terbilangHargaCell = document.getElementById("terbilangHarga");
        if (terbilangHargaCell) {
            terbilangHargaCell.textContent = terbilangText;
        }
    }

    // Simpan total ke database jika fungsi tersedia
    if (typeof updateTotalsInDatabase === "function") {
        updateTotalsInDatabase(totalRab, totalHargaWithPPN, roundedTotal);
    }
}

// Fungsi untuk menampilkan custom alert
function showCustomAlert(message, callback) {
    // Gunakan window.showInfoToast yang dibuat di notifications.js
    window.showInfoToast(message, null, callback, 3000);
}

// Fungsi showCustomConfirm telah dipindahkan ke file confirm.js
// dan tersedia secara global sebagai window.showCustomConfirm

// Fungsi untuk menghapus data volume calculation
function deleteVolumeCalculation(id) {
    showCustomConfirm(
        "Anda yakin ingin menghapus perhitungan volume ini?",
        () => {
            fetch(`/volume-calculations/${id}`, {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                },
            })
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Gagal menghapus perhitungan volume.");
                    }
                    return response.json();
                })
                .then((result) => {
                    showCustomAlert(result.message);
                    loadVolumeData();
                })
                .catch((error) => {
                    showCustomAlert(
                        "Terjadi kesalahan saat menghapus perhitungan volume."
                    );
                });
        },
        () => {
            console.log("Hapus dibatalkan.");
        }
    );
}
window.deleteVolumeCalculation = deleteVolumeCalculation;

// Fungsi untuk mengedit data volume calculation
function editVolumeCalculation(id) {
    // Pastikan loading overlay disembunyikan jika masih muncul
    if (window.hideLoading) {
        window.hideLoading();
    }

    fetch(`/volume-calculations/${id}`)
        .then((response) => {
            if (!response.ok) {
                throw new Error("Gagal mengambil data perhitungan volume.");
            }
            return response.json();
        })
        .then((calc) => {
            // Panggil addVolumeCalculation untuk membuat form
            addVolumeCalculation(calc.calculation_type);

            // Isi form dengan data yang ada
            document.getElementById("vc_keterangan").value =
                calc.keterangan || "";
            document.getElementById("vc_jumlah").value = calc.jumlah || "";
            if (VolumeModal.currentSatuan === "m3") {
                document.getElementById("vc_panjang").value =
                    calc.panjang || "";
                document.getElementById("vc_lebar").value = calc.lebar || "";
                document.getElementById("vc_tinggi").value = calc.tinggi || "";
                document.getElementById("vc_volume").value = calc.volume || "";
            } else if (VolumeModal.currentSatuan === "m2") {
                document.getElementById("vc_panjang").value =
                    calc.panjang || "";
                document.getElementById("vc_lebar").value = calc.lebar || "";
                document.getElementById("vc_luas").value = calc.luas || "";
            } else if (VolumeModal.currentSatuan === "kg") {
                document.getElementById("vc_panjang").value =
                    calc.panjang || "";
                document.getElementById("vc_berat_jenis").value =
                    calc.berat_jenis || "";
            } else if (VolumeModal.currentSatuan === "ltr") {
                document.getElementById("vc_liter").value = calc.liter || "";
            } else if (VolumeModal.currentSatuan === "m") {
                document.getElementById("vc_panjang").value =
                    calc.panjang || ""; // Tambahkan pengaturan panjang
            }
            document.getElementById("vc_hasil").value = calc.hasil || "";
            VolumeModal.currentCalculationMode = calc.calculation_type;
            VolumeModal.editingId = id;
            document
                .getElementById("inputVolumeModal")
                .classList.remove("hidden");
        })
        .catch((error) => {
            showCustomAlert(
                "Terjadi kesalahan saat mengambil data untuk diedit."
            );
        });
}
window.editVolumeCalculation = editVolumeCalculation;

// Fungsi untuk mengambil data volume calculation dan mengisi tabel
function loadVolumeData() {
    const itemId = VolumeModal.currentItemId;
    const unit = VolumeModal.currentSatuan;
    console.log(
        "Memuat data perhitungan volume untuk item ID: " +
            itemId +
            " dan unit: " +
            unit
    );
    fetch(`/volume-calculations?item_pekerjaan_id=${itemId}&unit=${unit}`)
        .then((response) => {
            if (!response.ok) {
                throw new Error("Gagal mengambil data volume.");
            }
            return response.json();
        })
        .then((data) => {
            const tbody = document.getElementById("volumeTableBody");
            let totalPlus = 0;
            let totalMinus = 0;
            const plusData = data.filter(
                (calc) => calc.calculation_type === "plus"
            );
            const minusData = data.filter(
                (calc) => calc.calculation_type === "minus"
            );

            const plusTemplate = generatePlusRows(plusData, unit);
            const minusTemplate = generateMinusRows(minusData, unit);

            const rows = plusTemplate.rows + minusTemplate.rows;
            totalPlus = plusTemplate.totalPlus;
            totalMinus = minusTemplate.totalMinus;

            tbody.innerHTML = rows;

            // Di dalam loadVolumeData() setelah perhitungan:
            const numericTotal = (totalPlus - totalMinus).toFixed(3);
            document.getElementById("totalHasil").innerText = numericTotal;
            // Misalnya, tampilkan satuan di span terpisah:
            document.getElementById("unitDisplay").innerText =
                VolumeModal.currentSatuan;

            // Tooltips removed
        })
        .catch((error) => {
            showCustomAlert("Error loading volume data: " + error.message);
        });
}
window.loadVolumeData = loadVolumeData;

// Generate header tabel volume menggunakan template
function generateVolumeTableHeader(satuan) {
    document.getElementById("volumeTableHeader").innerHTML =
        getHeaderHtml(satuan);
}

// Fungsi untuk mengatur manual override pada input "Hasil" menggunakan event "blur".
// Jika pengguna mengisi input "Hasil" secara manual, maka semua input angka (selain "vc_keterangan" dan "vc_hasil") akan diset ke 0.
function setupManualOverride() {
    const hasilField = document.getElementById("vc_hasil");
    if (!hasilField) return;
    hasilField.addEventListener("blur", function () {
        if (hasilField.value.trim() !== "") {
            const fieldsToReset = [
                "vc_jumlah",
                "vc_panjang",
                "vc_lebar",
                "vc_tinggi",
                "vc_luas",
                "vc_berat_jenis",
                "vc_liter",
                "vc_volume",
            ];
            fieldsToReset.forEach((id) => {
                const field = document.getElementById(id);
                if (field && id !== "vc_hasil") {
                    field.value = 0;
                }
            });
        }
    });
}

// Fungsi untuk membuka form input volume (untuk tambah) sesuai tombol,
// menggunakan template dari getFormHtml dan mengatur perhitungan otomatis.
function addVolumeCalculation(mode) {
    VolumeModal.currentCalculationMode = mode; // 'plus' atau 'minus'
    VolumeModal.editingId = null; // Reset mode edit
    document.getElementById("volumeFormContainer").innerHTML = getFormHtml(
        VolumeModal.currentSatuan
    );
    document.getElementById("inputVolumeModal").classList.remove("hidden");

    // Set event listener untuk submit form
    const volumeForm = document.getElementById("volumeForm");
    if (volumeForm) {
        // Hapus event listener lama jika ada
        const oldForm = volumeForm.cloneNode(true);
        volumeForm.parentNode.replaceChild(oldForm, volumeForm);

        // Tambahkan event listener baru
        oldForm.addEventListener("submit", handleVolumeFormSubmit);

        // Pastikan tombol submit tidak memiliki event listener ganda
        const btnSaveVolume = document.getElementById("btnSaveVolume");
        if (btnSaveVolume) {
            // Hapus event listener lama jika ada dengan clone node
            const oldBtn = btnSaveVolume.cloneNode(true);
            btnSaveVolume.parentNode.replaceChild(oldBtn, btnSaveVolume);
        }
    }

    if (VolumeModal.currentSatuan === "m3") {
        const panjangField = document.getElementById("vc_panjang");
        const lebarField = document.getElementById("vc_lebar");
        const tinggiField = document.getElementById("vc_tinggi");
        const volumeField = document.getElementById("vc_volume");
        const jumlahField = document.getElementById("vc_jumlah");
        const hasilField = document.getElementById("vc_hasil");

        function computeVolume() {
            const panjang = parseFloat(panjangField.value) || 0;
            const lebar = parseFloat(lebarField.value) || 0;
            const tinggi = parseFloat(tinggiField.value) || 0;
            const volume = panjang * lebar * tinggi;
            volumeField.value = volume;
            computeHasil();
        }
        function computeHasil() {
            const jumlah = parseFloat(jumlahField.value) || 0;
            const volume = parseFloat(volumeField.value) || 0;
            hasilField.value = jumlah * volume;
        }
        panjangField.addEventListener("input", computeVolume);
        lebarField.addEventListener("input", computeVolume);
        tinggiField.addEventListener("input", computeVolume);
        jumlahField.addEventListener("input", computeHasil);
    } else if (VolumeModal.currentSatuan === "m2") {
        const panjangField = document.getElementById("vc_panjang");
        const lebarField = document.getElementById("vc_lebar");
        const luasField = document.getElementById("vc_luas");
        const jumlahField = document.getElementById("vc_jumlah");
        const hasilField = document.getElementById("vc_hasil");

        function computeLuas() {
            const panjang = parseFloat(panjangField.value) || 0;
            const lebar = parseFloat(lebarField.value) || 0;
            const luas = panjang * lebar;
            luasField.value = luas;
            computeHasil();
        }
        function computeHasil() {
            const jumlah = parseFloat(jumlahField.value) || 0;
            const luas = parseFloat(luasField.value) || 0;
            hasilField.value = jumlah * luas;
        }
        panjangField.addEventListener("input", computeLuas);
        lebarField.addEventListener("input", computeLuas);
        jumlahField.addEventListener("input", computeHasil);
    } else if (VolumeModal.currentSatuan === "m") {
        const panjangField = document.getElementById("vc_panjang");
        const jumlahField = document.getElementById("vc_jumlah");
        const hasilField = document.getElementById("vc_hasil");

        function computeHasil() {
            const jumlah = parseFloat(jumlahField.value) || 0;
            const panjang = parseFloat(panjangField.value) || 0;
            hasilField.value = jumlah * panjang;
        }
        panjangField.addEventListener("input", computeHasil);
        jumlahField.addEventListener("input", computeHasil);
    } else if (VolumeModal.currentSatuan === "kg") {
        const panjangField = document.getElementById("vc_panjang");
        const jumlahField = document.getElementById("vc_jumlah");
        const beratJenisField = document.getElementById("vc_berat_jenis");
        const hasilField = document.getElementById("vc_hasil");

        function computeHasil() {
            const jumlah = parseFloat(jumlahField.value) || 0;
            const panjang = parseFloat(panjangField.value) || 0;
            const beratJenis = parseFloat(beratJenisField.value) || 0;
            hasilField.value = jumlah * panjang * beratJenis;
        }
        panjangField.addEventListener("input", computeHasil);
        jumlahField.addEventListener("input", computeHasil);
        beratJenisField.addEventListener("input", computeHasil);
    } else if (VolumeModal.currentSatuan === "ltr") {
        const literField = document.getElementById("vc_liter");
        const jumlahField = document.getElementById("vc_jumlah");
        const hasilField = document.getElementById("vc_hasil");

        function computeHasil() {
            const jumlah = parseFloat(jumlahField.value) || 0;
            const liter = parseFloat(literField.value) || 0;
            hasilField.value = jumlah * liter;
        }
        literField.addEventListener("input", computeHasil);
        jumlahField.addEventListener("input", computeHasil);
    } else if (["bh", "ls", "unit"].includes(VolumeModal.currentSatuan)) {
        const jumlahField = document.getElementById("vc_jumlah");
        const hasilField = document.getElementById("vc_hasil");

        function computeHasil() {
            const jumlah = parseFloat(jumlahField.value) || 0;
            hasilField.value = jumlah;
        }
        jumlahField.addEventListener("input", computeHasil);
    }
    // Pasang manual override pada input "Hasil"
    setupManualOverride();
}
window.addVolumeCalculation = addVolumeCalculation;

// Fungsi handler untuk submit form volume
function handleVolumeFormSubmit(event) {
    // Mencegah default behavior form submission
    event.preventDefault();

    // Dapatkan semua nilai input yang diperlukan
    const keteranganEl = document.getElementById("vc_keterangan");
    const hasilEl = document.getElementById("vc_hasil");
    const jumlahEl = document.getElementById("vc_jumlah");

    // Form validation dilakukan oleh HTML5 required attribute dan custom validation

    // Ambil nilai dari input
    const keterangan = keteranganEl.value.trim();
    const hasil = parseFloat(hasilEl.value) || 0;
    const jumlah = parseFloat(jumlahEl.value) || 0;

    // Validasi hasil tidak boleh 0
    if (hasil <= 0) {
        hasilEl.setCustomValidity("Hasil tidak boleh kosong atau 0");
        hasilEl.reportValidity();
        return; // Hentikan proses submit
    } else {
        hasilEl.setCustomValidity("");
    }

    // Buat objek data yang akan dikirim
    let data = {
        item_pekerjaan_id: VolumeModal.currentItemId,
        satuan: VolumeModal.currentSatuan,
        keterangan: keterangan,
        jumlah: jumlah,
        hasil: hasil,
        calculation_type: VolumeModal.currentCalculationMode, // 'plus' atau 'minus'
    };

    // Tambahkan data sesuai satuan
    if (VolumeModal.currentSatuan === "m3") {
        data.panjang = document.getElementById("vc_panjang")
            ? parseFloat(document.getElementById("vc_panjang").value) || 0
            : 0;
        data.lebar = document.getElementById("vc_lebar")
            ? parseFloat(document.getElementById("vc_lebar").value) || 0
            : 0;
        data.tinggi = document.getElementById("vc_tinggi")
            ? parseFloat(document.getElementById("vc_tinggi").value) || 0
            : 0;
        data.volume = document.getElementById("vc_volume")
            ? parseFloat(document.getElementById("vc_volume").value) || 0
            : 0;
    } else if (VolumeModal.currentSatuan === "m2") {
        data.panjang = document.getElementById("vc_panjang")
            ? parseFloat(document.getElementById("vc_panjang").value) || 0
            : 0;
        data.lebar = document.getElementById("vc_lebar")
            ? parseFloat(document.getElementById("vc_lebar").value) || 0
            : 0;
        data.luas = document.getElementById("vc_luas")
            ? parseFloat(document.getElementById("vc_luas").value) || 0
            : 0;
    } else if (VolumeModal.currentSatuan === "m") {
        data.panjang = document.getElementById("vc_panjang")
            ? parseFloat(document.getElementById("vc_panjang").value) || 0
            : 0;
    } else if (VolumeModal.currentSatuan === "kg") {
        data.panjang = document.getElementById("vc_panjang")
            ? parseFloat(document.getElementById("vc_panjang").value) || 0
            : 0;
        data.berat_jenis = document.getElementById("vc_berat_jenis")
            ? parseFloat(document.getElementById("vc_berat_jenis").value) || 0
            : 0;
    } else if (VolumeModal.currentSatuan === "ltr") {
        data.liter = document.getElementById("vc_liter")
            ? parseFloat(document.getElementById("vc_liter").value) || 0
            : 0;
    }

    // Nonaktifkan tombol submit saat proses (jika belum dinonaktifkan oleh saveVolumeCalculationWithLoading)
    const submitButton = document.getElementById("btnSaveVolume");
    if (submitButton && !submitButton.disabled) {
        if (window.setButtonLoading) {
            window.setButtonLoading(submitButton, true, "Simpan");
        } else {
            submitButton.disabled = true;
            submitButton.innerHTML = "Menyimpan...";
        }
    }

    console.log("Data yang dikirim:", data);

    // Set URL dan method berdasarkan mode (tambah atau edit)
    let url = "/volume-calculations";
    let method = "POST";
    if (VolumeModal.editingId) {
        url = `/volume-calculations/${VolumeModal.editingId}`;
        method = "PUT";
    }

    // Kirim data ke server
    fetch(url, {
        method: method,
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
        body: JSON.stringify(data),
    })
        .then((response) => {
            if (!response.ok)
                throw new Error("Gagal menyimpan perhitungan volume.");
            return response.json();
        })
        .then((result) => {
            showCustomAlert(result.message);
            VolumeModal.editingId = null;
            closeInputVolumeModal(); // Hanya tutup modal jika sukses
            loadVolumeData();
        })
        .catch((error) => {
            showCustomAlert(
                "Terjadi kesalahan saat menyimpan perhitungan volume."
            );
            // Modal tetap terbuka jika terjadi error
        })
        .finally(() => {
            // Aktifkan kembali tombol submit
            if (submitButton) {
                if (window.setButtonLoading) {
                    window.setButtonLoading(submitButton, false, "Simpan");
                } else {
                    submitButton.disabled = false;
                    submitButton.innerHTML =
                        '<i class="fas fa-save"></i> Simpan';
                }
            }

            // Sembunyikan loading overlay jika masih muncul
            if (window.hideLoading) {
                window.hideLoading();
            }
        });
}

// Fungsi untuk menutup modal input volume calculation
function closeInputVolumeModal() {
    document.getElementById("inputVolumeModal").classList.add("hidden");

    // Pastikan loading overlay disembunyikan
    if (window.hideLoading) {
        window.hideLoading();
    }

    // Reset tombol submit jika masih dalam keadaan loading
    const submitButton = document.getElementById("btnSaveVolume");
    if (submitButton && submitButton.disabled) {
        if (window.setButtonLoading) {
            window.setButtonLoading(submitButton, false, "Simpan");
        } else {
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-save"></i> Simpan';
        }
    }
}
window.closeInputVolumeModal = closeInputVolumeModal;

// Fungsi untuk menginisialisasi tooltip khusus untuk tombol volume
function initVolumeTooltips() {
    const volumeButtons = document.querySelectorAll(
        "[data-tooltip-target='tooltip-add-volume-plus'], [data-tooltip-target='tooltip-add-volume-minus']"
    );

    volumeButtons.forEach((button) => {
        // Remove existing event listeners
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        const targetId = newButton.getAttribute("data-tooltip-target");
        const tooltipEl = document.getElementById(targetId);

        if (tooltipEl) {
            // Mark as initialized to prevent default tooltip behavior
            newButton.dataset.tooltipInitialized = "true";

            newButton.addEventListener("mouseenter", () => {
                // Move tooltip to body for better positioning
                document.body.appendChild(tooltipEl);

                tooltipEl.classList.remove("invisible", "opacity-0");
                tooltipEl.classList.add("visible", "opacity-100");

                // Position the tooltip ABOVE the button
                const triggerRect = newButton.getBoundingClientRect();

                tooltipEl.style.position = "fixed";
                tooltipEl.style.zIndex = "9999";
                // Position above the button with 10px gap
                tooltipEl.style.top = `${
                    triggerRect.top - tooltipEl.offsetHeight - 10
                }px`;
                tooltipEl.style.left = `${
                    triggerRect.left +
                    triggerRect.width / 2 -
                    tooltipEl.offsetWidth / 2
                }px`;

                // Add arrow position
                const tooltipArrow = tooltipEl.querySelector(".tooltip-arrow");
                if (tooltipArrow) {
                    tooltipArrow.style.left = "50%";
                    tooltipArrow.style.top = "100%";
                    tooltipArrow.style.transform =
                        "translateX(-50%) rotate(45deg)";
                }
            });

            newButton.addEventListener("mouseleave", () => {
                tooltipEl.classList.remove("visible", "opacity-100");
                tooltipEl.classList.add("invisible", "opacity-0");

                // Reset tooltip position and styles
                tooltipEl.style.position = "";
                tooltipEl.style.top = "";
                tooltipEl.style.left = "";
                tooltipEl.style.zIndex = "";
            });

            // Restore onclick handler
            if (newButton.hasAttribute("onclick")) {
                const onclickValue = newButton.getAttribute("onclick");
                newButton.onclick = function () {
                    eval(onclickValue);
                };
            }
        }
    });
}
