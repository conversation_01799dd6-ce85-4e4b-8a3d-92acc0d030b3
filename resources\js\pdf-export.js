// PDF Export Options Handler
document.addEventListener("DOMContentLoaded", function () {
    // Get the download buttons
    const downloadPdfBtn = document.getElementById("download-pdf-btn");
    const downloadExcelBtn = document.getElementById(
        "download-excel-formula-btn"
    );
    if (!downloadPdfBtn && !downloadExcelBtn) return;

    // Get all export option checkboxes
    const exportOptions = [
        "export-rekap",
        "export-rab",
        "export-ahsp",
        "export-upah",
        "export-bahan",
        "export-alat",
        "export-time-schedule",
        "export-volume-calculations",
    ].map((id) => document.getElementById(id));

    // Add event listener to download buttons
    [downloadPdfBtn, downloadExcelBtn].forEach((btn) => {
        if (!btn) return;

        btn.addEventListener("click", function (e) {
            e.preventDefault();

            // Get selected options
            const selectedOptions = exportOptions
                .filter((checkbox) => checkbox && checkbox.checked)
                .map((checkbox) => checkbox.value);

            // If no options selected, show alert
            if (selectedOptions.length === 0) {
                alert("Pilih minimal satu opsi untuk dieksport.");
                return;
            }

            // Build URL with query parameters
            let url = btn.getAttribute("href");
            url += "?export=" + selectedOptions.join(",");

            // Navigate to the URL
            window.location.href = url;
        });
    });
});
