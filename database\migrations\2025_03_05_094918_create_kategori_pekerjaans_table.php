<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateKategoriPekerjaansTable extends Migration
{
    public function up()
    {
        Schema::create('kategori_pekerjaans', function (Blueprint $table) {
            $table->id();
            $table->string('nama_kategori');
            $table->unsignedBigInteger('project_id')->nullable(false); // Pastikan tidak null
            $table->foreign('project_id')
                ->references('id')->on('projects')
                ->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('kategori_pekerjaans');
    }
}
