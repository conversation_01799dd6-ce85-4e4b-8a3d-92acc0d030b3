<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Payment Gateway Callbacks - Sebaiknya gunakan /xendit-webhook di web.php
// Route ini disimpan untuk backward compatibility
Route::post('/payments/callback', [\App\Http\Controllers\PaymentController::class, 'callback'])->name('api.payments.callback');

// Xendit Callback Webhook - Route ini tetap ada untuk backward compatibility
// Sebaiknya gunakan /xendit-webhook di web.php yang tidak melalui middleware API
Route::match(['post', 'options'], '/webhooks/xendit', [\App\Http\Controllers\PaymentController::class, 'xenditWebhook']);

// Test route untuk mengecek apakah API route berfungsi
Route::get('/test', function () {
    return response()->json(['status' => 'success', 'message' => 'API route is working']);
});
