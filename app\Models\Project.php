<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'location',
        'project_owner',
        'cover_photo',
        'user_id',
        'year',
        'ppn',
        'province',
        'city',
        'district'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function rab()
    {
        return $this->hasOne(Rab::class);
    }

    public function rabs()
    {
        return $this->hasMany(Rab::class, 'proyek_id');
    }

    public function timeSchedules()
    {
        return $this->hasMany(TimeSchedule::class);
    }

    /**
     * Get all shares for this project
     */
    public function shares()
    {
        return $this->hasMany(ProjectShare::class);
    }

    /**
     * Get all users that this project is shared with
     */
    public function sharedUsers()
    {
        return $this->belongsToMany(User::class, 'project_shares')
            ->withPivot('role')
            ->withTimestamps();
    }

    /**
     * Check if the project is shared with a specific user
     */
    public function isSharedWith($userId)
    {
        return $this->shares()->where('user_id', $userId)->exists();
    }

    /**
     * Check if the project is shared with the current user
     */
    public function isSharedWithUser()
    {
        if (!Auth::check()) {
            return false;
        }

        // If user is the owner, return false (not shared)
        if ($this->user_id === Auth::id()) {
            return false;
        }

        return $this->isSharedWith(Auth::id());
    }

    /**
     * Get the share role for a specific user
     */
    public function getShareRoleFor($userId)
    {
        $share = $this->shares()->where('user_id', $userId)->first();
        return $share ? $share->role : null;
    }

    protected static function booted()
    {
        static::deleting(function ($project) {
            // Delete the cover photo if it exists
            if ($project->cover_photo) {
                Storage::disk('public')->delete($project->cover_photo);
            }
        });
    }
}
