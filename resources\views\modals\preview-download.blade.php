<!-- Preview Download Modal -->
<div id="previewDownloadModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
    <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-4xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
        <!-- Header -->
        <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
            <div class="flex justify-between items-center">
                <h1 class="text-white dark:text-dark-text text-xl font-semibold">Preview Laporan</h1>
                <button type="button" onclick="closePreviewDownloadModal()" 
                    class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
            <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            <span class="sr-only">Close modal</span>
        </button>
            </div>
        </div>
        
        <!-- Content -->
        <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
            <!-- Modal body -->
            <div class="space-y-4">
                <!-- REKAPITULASI Preview -->
                <div class="mb-6">
                    <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-2">REKAPITULASI RENCANA ANGGARAN BIAYA</h4>
                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400 border-collapse">
                                <thead class="text-xs text-gray-700 uppercase bg-gray-100 dark:bg-gray-700 dark:text-gray-400">
                                    <tr>
                                        <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">No</th>
                                        <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Uraian Pekerjaan</th>
                                        <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Jumlah Harga (Rp)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @php
                                        $rekapNo = 0;
                                        $totalRekapitulasi = 0;
                                        $letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
                                    @endphp
                                    @foreach($kategoriPekerjaans as $kategori)
                                        @php
                                            $kategoriTotal = 0;
                                            foreach($kategori->items as $item) {
                                                $kategoriTotal += $item->harga_total;
                                            }
                                            $totalRekapitulasi += $kategoriTotal;
                                            $letter = isset($letters[$rekapNo]) ? $letters[$rekapNo] : $rekapNo + 1;
                                            $rekapNo++;
                                        @endphp
                                        <tr>
                                            <td class="px-4 py-2 border border-gray-300 dark:border-gray-600">{{ $letter }}</td>
                                            <td class="px-4 py-2 border border-gray-300 dark:border-gray-600">{{ strtoupper($kategori->nama_kategori) }}</td>
                                            <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($kategoriTotal, 2, ',', '.') }}</span></td>
                                        </tr>
                                    @endforeach
                                    <tr>
                                        <td colspan="2" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right font-bold">JUMLAH</td>
                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right font-bold"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($totalRekapitulasi, 2, ',', '.') }}</span></td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right">PPN {{ isset($ppn) ? $ppn : $project->ppn }}%</td>
                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ isset($ppnHarga) ? number_format($ppnHarga, 2, ',', '.') : '0,00' }}</span></td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right font-bold">TOTAL</td>
                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right font-bold"><span class="float-left">Rp.</span> <span class="float-right">{{ isset($totalHarga) ? number_format($totalHarga, 2, ',', '.') : number_format($totalRekapitulasi * (1 + $project->ppn/100), 2, ',', '.') }}</span></td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right font-bold">DIBULATKAN</td>
                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right font-bold"><span class="float-left">Rp.</span> <span class="float-right">{{ isset($dibulatkanHarga) ? number_format($dibulatkanHarga, 2, ',', '.') : number_format(floor($totalRekapitulasi * (1 + $project->ppn/100) / 1000) * 1000, 2, ',', '.') }}</span></td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right">
                                            <i>Terbilang:
                                                @if(isset($terbilangTotal))
                                                    {{ $terbilangTotal }}
                                                @else
                                                    @php
                                                        $dibulatkan = floor($totalRekapitulasi * (1 + $project->ppn/100) / 1000) * 1000;
                                                        function terbilang($angka) {
                                                            $angka = abs($angka);
                                                            $words = array(
                                                                '',
                                                                'satu',
                                                                'dua',
                                                                'tiga',
                                                                'empat',
                                                                'lima',
                                                                'enam',
                                                                'tujuh',
                                                                'delapan',
                                                                'sembilan',
                                                                'sepuluh',
                                                                'sebelas'
                                                            );

                                                            if ($angka < 12) {
                                                                return $words[$angka];
                                                            } elseif ($angka < 20) {
                                                                return terbilang($angka - 10) . ' belas';
                                                            } elseif ($angka < 100) {
                                                                return terbilang(floor($angka / 10)) . ' puluh ' . terbilang($angka % 10);
                                                            } elseif ($angka < 200) {
                                                                return 'seratus ' . terbilang($angka - 100);
                                                            } elseif ($angka < 1000) {
                                                                return terbilang(floor($angka / 100)) . ' ratus ' . terbilang($angka % 100);
                                                            } elseif ($angka < 2000) {
                                                                return 'seribu ' . terbilang($angka - 1000);
                                                            } elseif ($angka < 1000000) {
                                                                return terbilang(floor($angka / 1000)) . ' ribu ' . terbilang($angka % 1000);
                                                            } elseif ($angka < 1000000000) {
                                                                return terbilang(floor($angka / 1000000)) . ' juta ' . terbilang($angka % 1000000);
                                                            } elseif ($angka < 1000000000000) {
                                                                return terbilang(floor($angka / 1000000000)) . ' milyar ' . terbilang($angka % 1000000000);
                                                            } elseif ($angka < 1000000000000000) {
                                                                return terbilang(floor($angka / 1000000000000)) . ' trilyun ' . terbilang($angka % 1000000000000);
                                                            } else {
                                                                return 'Angka terlalu besar';
                                                            }
                                                        }
                                                        echo ucwords(terbilang(floor($dibulatkan))) . ' Rupiah';
                                                    @endphp
                                                @endif
                                            </i>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- RAB Preview -->
                <div class="mb-6">
                    <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-2">RENCANA ANGGARAN BIAYA (RAB)</h4>
                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400 border-collapse">
                                <thead class="text-xs text-gray-700 uppercase bg-gray-100 dark:bg-gray-700 dark:text-gray-400">
                                    <tr>
                                        <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">No</th>
                                        <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Uraian Pekerjaan</th>
                                        <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Volume</th>
                                        <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Satuan</th>
                                        <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Harga Satuan (Rp)</th>
                                        <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Jumlah Harga (Rp)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @php
                                        $kategoriNo = 0;
                                        $letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
                                    @endphp
                                    @foreach($kategoriPekerjaans as $kategori)
                                        @php
                                            $letter = isset($letters[$kategoriNo]) ? $letters[$kategoriNo] : $kategoriNo + 1;
                                            $kategoriNo++;
                                            $itemNo = 1; // Reset item number for each category
                                        @endphp
                                        <tr>
                                            <td class="px-4 py-2 border border-gray-300 dark:border-gray-600">{{ $letter }}</td>
                                            <td class="px-4 py-2 border border-gray-300 dark:border-gray-600" colspan="5">{{ strtoupper($kategori->nama_kategori) }}</td>
                                        </tr>
                                        @foreach($kategori->items as $item)
                                            <tr>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $itemNo++ }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600">{{ $item->uraian_item }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ number_format($item->volume, 2) }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $item->satuan }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($item->harga_satuan, 2, ',', '.') }}</span></td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($item->harga_total, 2, ',', '.') }}</span></td>
                                            </tr>
                                        @endforeach
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- AHSP Preview -->
                <div class="mb-6">
                    <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-2">ANALISA HARGA SATUAN PEKERJAAN</h4>
                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="overflow-y-auto max-h-96">
                            @if(isset($ahsRecords) && count($ahsRecords) > 0)
                            @foreach($ahsRecords as $ahs)
                                <h5 class="font-bold text-gray-800 dark:text-white mt-4 mb-2">{{ $ahs->kode }} - {{ $ahs->judul }}</h5>
                                <div class="overflow-x-auto mb-6">
                                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400 border-collapse">
                                        <thead class="text-xs text-gray-700 uppercase bg-gray-100 dark:bg-gray-700 dark:text-gray-400">
                                            <tr>
                                                <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">No</th>
                                                <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Uraian</th>
                                                <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Koefisien</th>
                                                <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Satuan</th>
                                                <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Harga Satuan (Rp)</th>
                                                <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Jumlah Harga (Rp)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @php
                                                $upahDetails = $ahsDetails->where('ahs_id', $ahs->id)->filter(function($detail) {
                                                    return strtolower($detail->kategori) === 'upah';
                                                });
                                                $bahanDetails = $ahsDetails->where('ahs_id', $ahs->id)->filter(function($detail) {
                                                    return strtolower($detail->kategori) === 'bahan';
                                                });
                                                $alatDetails = $ahsDetails->where('ahs_id', $ahs->id)->filter(function($detail) {
                                                    return strtolower($detail->kategori) === 'alat';
                                                });
                                                $detailNo = 1;
                                            @endphp

                                            <!-- Upah Section - always show -->
                                            <tr>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600" colspan="6">A. TENAGA KERJA</td>
                                            </tr>
                                            @if($upahDetails->count() > 0)
                                                @foreach($upahDetails as $detail)
                                                    @php
                                                        $upah = $upahItems->where('id', $detail->item_id)->first();
                                                        $hargaSatuan = $upah ? $upah->harga : 0;
                                                        $jumlahHarga = $detail->koefisien * $hargaSatuan;
                                                    @endphp
                                                    <tr>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $detailNo++ }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600">{{ $upah ? $upah->uraian_tenaga : 'Upah tidak ditemukan' }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ number_format($detail->koefisien, 4) }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $upah ? $upah->satuan : '-' }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($hargaSatuan, 2, ',', '.') }}</span></td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($jumlahHarga, 2, ',', '.') }}</span></td>
                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="6" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">Tidak ada data tenaga kerja</td>
                                                </tr>
                                            @endif

                                            <!-- Bahan Section - always show -->
                                            <tr>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600" colspan="6">B. BAHAN</td>
                                            </tr>
                                            @if($bahanDetails->count() > 0)
                                                @foreach($bahanDetails as $detail)
                                                    @php
                                                        $bahan = $bahanItems->where('id', $detail->item_id)->first();
                                                        $hargaSatuan = $bahan ? $bahan->harga_bahan : 0;
                                                        $jumlahHarga = $detail->koefisien * $hargaSatuan;
                                                    @endphp
                                                    <tr>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $detailNo++ }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600">{{ $bahan ? $bahan->uraian_bahan : 'Bahan tidak ditemukan' }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ number_format($detail->koefisien, 4) }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $bahan ? $bahan->satuan : '-' }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($hargaSatuan, 2, ',', '.') }}</span></td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($jumlahHarga, 2, ',', '.') }}</span></td>
                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="6" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">Tidak ada data bahan</td>
                                                </tr>
                                            @endif

                                            <!-- Alat Section - always show -->
                                            <tr>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600" colspan="6">C. ALAT</td>
                                            </tr>
                                            @if($alatDetails->count() > 0)
                                                @foreach($alatDetails as $detail)
                                                    @php
                                                        $alat = $alatItems->where('id', $detail->item_id)->first();
                                                        $hargaSatuan = $alat ? $alat->harga_alat : 0;
                                                        $jumlahHarga = $detail->koefisien * $hargaSatuan;
                                                    @endphp
                                                    <tr>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $detailNo++ }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600">{{ $alat ? $alat->uraian_alat : 'Alat tidak ditemukan' }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ number_format($detail->koefisien, 4) }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $alat ? $alat->satuan : '-' }}</td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($hargaSatuan, 2, ',', '.') }}</span></td>
                                                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($jumlahHarga, 2, ',', '.') }}</span></td>
                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="6" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">Tidak ada data alat</td>
                                                </tr>
                                            @endif

                                            <!-- Subtotal and Overhead -->
                                            @php
                                                $subtotal = 0;
                                                foreach($ahsDetails->where('ahs_id', $ahs->id) as $detail) {
                                                    $resourceId = $detail->item_id;
                                                    $hargaSatuan = 0;

                                                    $kategori = strtolower($detail->kategori);
                                                    if($kategori == 'upah') {
                                                        $resource = $upahItems->where('id', $resourceId)->first();
                                                        $hargaSatuan = $resource ? $resource->harga : 0;
                                                    } elseif($kategori == 'bahan') {
                                                        $resource = $bahanItems->where('id', $resourceId)->first();
                                                        $hargaSatuan = $resource ? $resource->harga_bahan : 0;
                                                    } elseif($kategori == 'alat') {
                                                        $resource = $alatItems->where('id', $resourceId)->first();
                                                        $hargaSatuan = $resource ? $resource->harga_alat : 0;
                                                    }

                                                    $subtotal += $detail->koefisien * $hargaSatuan;
                                                }

                                                $overhead = ($subtotal * $ahs->overhead) / 100;
                                                $grandTotal = $subtotal + $overhead;
                                            @endphp

                                            <tr class="font-bold">
                                                <td colspan="5" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right">SUBTOTAL</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($subtotal, 2, ',', '.') }}</span></td>
                                            </tr>
                                            <tr>
                                                <td colspan="5" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right">OVERHEAD {{ $ahs->overhead }}%</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($overhead, 2, ',', '.') }}</span></td>
                                            </tr>
                                            <tr class="font-bold">
                                                <td colspan="5" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right">HARGA SATUAN PEKERJAAN</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($grandTotal, 2, ',', '.') }}</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            @endforeach
                            @else
                            <div class="text-center p-4 text-gray-500 dark:text-gray-400">
                                <p>Tidak ada data AHSP yang digunakan pada RAB ini.</p>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Daftar Harga Satuan Preview -->
                <div class="mb-6">
                    <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-2">DAFTAR HARGA SATUAN</h4>
                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="overflow-y-auto max-h-96">
                            <!-- Upah Table -->
                            <h5 class="font-bold text-gray-800 dark:text-white mt-4 mb-2">A. UPAH</h5>
                            <div class="overflow-x-auto mb-6">
                                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400 border-collapse">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-100 dark:bg-gray-700 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">No</th>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Uraian</th>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Satuan</th>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Harga Satuan (Rp)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($upahItems->take(5) as $index => $upah)
                                            <tr>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $index + 1 }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600">{{ $upah->uraian_tenaga }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $upah->satuan }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($upah->harga, 2, ',', '.') }}</span></td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="4" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">Tidak ada data tenaga kerja</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>

                            <!-- Bahan Table -->
                            <h5 class="font-bold text-gray-800 dark:text-white mt-4 mb-2">B. BAHAN</h5>
                            <div class="overflow-x-auto mb-6">
                                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400 border-collapse">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-100 dark:bg-gray-700 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">No</th>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Uraian</th>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Satuan</th>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Harga Satuan (Rp)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($bahanItems->take(5) as $index => $bahan)
                                            <tr>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $index + 1 }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600">{{ $bahan->uraian_bahan }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $bahan->satuan }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($bahan->harga_bahan, 2, ',', '.') }}</span></td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="4" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">Tidak ada data bahan</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>

                            <!-- Alat Table -->
                            <h5 class="font-bold text-gray-800 dark:text-white mt-4 mb-2">C. ALAT</h5>
                            <div class="overflow-x-auto mb-6">
                                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400 border-collapse">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-100 dark:bg-gray-700 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">No</th>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Uraian</th>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Satuan</th>
                                            <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Harga Satuan (Rp)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($alatItems->take(5) as $index => $alat)
                                            <tr>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $index + 1 }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600">{{ $alat->uraian_alat }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $alat->satuan }}</td>
                                                <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right"><span class="float-left">Rp.</span> <span class="float-right">{{ number_format($alat->harga_alat, 2, ',', '.') }}</span></td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="4" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">Tidak ada data alat</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Kurva S Section -->
                @include('modals.kurva-s-preview')
            </div>
            <!-- Modal footer -->
            <div class="flex flex-col p-4 border-t border-gray-200 dark:border-gray-600 mt-4">
                <!-- Export Options -->
                <div class="mb-4 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Pilihan Ekspor</h4>
                    <div class="flex flex-wrap gap-3">
                        <div class="flex items-center">
                            <input checked id="export-rekap" type="checkbox" value="rekap" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="export-rekap" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">REKAP</label>
                        </div>
                        <div class="flex items-center">
                            <input checked id="export-rab" type="checkbox" value="rab" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="export-rab" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">RAB</label>
                        </div>
                        <div class="flex items-center">
                            <input checked id="export-ahsp" type="checkbox" value="ahsp" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="export-ahsp" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">AHSP</label>
                        </div>
                        <div class="flex items-center">
                            <input checked id="export-upah" type="checkbox" value="upah" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="export-upah" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Upah</label>
                        </div>
                        <div class="flex items-center">
                            <input checked id="export-bahan" type="checkbox" value="bahan" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="export-bahan" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Bahan</label>
                        </div>
                        <div class="flex items-center">
                            <input checked id="export-alat" type="checkbox" value="alat" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="export-alat" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Alat</label>
                        </div>
                        <div class="flex items-center">
                            <input checked id="export-time-schedule" type="checkbox" value="time-schedule" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="export-time-schedule" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Time Schedule</label>
                        </div>
                        <div class="flex items-center">
                            <input checked id="export-volume-calculations" type="checkbox" value="volume-calculations" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="export-volume-calculations" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Volume Calculations</label>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-end space-x-4 mt-4">
                    <button type="button" class="bg-gray-500 hover:bg-gray-700 hover:text-blue-100 text-white px-4 py-2 rounded" onclick="closePreviewDownloadModal()">
                        <i class="fas fa-times-circle"></i> Tutup
                    </button>
                    <a href="{{ route('download.rab-pdf') }}" id="download-pdf-btn" class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105 dark:bg-dark-accent dark:hover:bg-dark-accent/80">
                        <i class="fas fa-download mr-2"></i>Download PDF
                    </a>
                    <a href="{{ route('download.rab-excel-formula') }}" id="download-excel-formula-btn" class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105 dark:bg-dark-accent dark:hover:bg-dark-accent/80">
                        <i class="fas fa-file-excel mr-2"></i>Download Excel dengan Rumus
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
