<?php

namespace App\Policies;

use App\Models\Ahs;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class AhsPolicy
{
    use HandlesAuthorization;

    /**
     * Tentukan apakah pengguna dapat melihat entri.
     */
    public function view(User $user, Ahs $ahs)
    {
        // Admin dapat melihat semua
        if ($user->role === 'admin') {
            return true;
        }

        // AHS yang dibuat admin dapat dilihat semua orang
        if ($ahs->user_id === null || in_array($ahs->user_id, User::where('role', 'admin')->pluck('id')->toArray())) {
            return true;
        }

        // Customer hanya dapat melihat AHS miliknya sendiri
        return $user->id === $ahs->user_id;
    }

    /**
     * Tentukan apakah pengguna dapat memperbarui entri.
     */
    public function update(User $user, Ahs $ahs)
    {
        // Admin dapat memperbarui semua
        if ($user->role === 'admin') {
            return true;
        }

        // Customer hanya dapat memperbarui AHS miliknya sendiri
        return $user->id === $ahs->user_id;
    }

    /**
     * Tentukan apakah pengguna dapat menghapus entri.
     */
    public function delete(User $user, Ahs $ahs)
    {
        // Admin dapat menghapus semua
        if ($user->role === 'admin') {
            return true;
        }

        // Customer hanya dapat menghapus AHS miliknya sendiri
        return $user->id === $ahs->user_id;
    }
}
