<div id="fullFormModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-4xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h1 id="formTitle" class="text-white dark:text-dark-text text-lg font-semibold">Input Data AHSP</h1>
        <button type="button" onclick="closeFullFormModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
      <!-- Total Hasil -->
      <div class="border-t dark:border-dark-border flex justify-center mb-2">
          <span class="font-bold mr-2 dark:text-gray-200">HARGA SATUAN</span>
      </div>
      <!-- Label untuk Grand Total -->
      <div class="mb-6 text-center">
        <span id="grandTotalLabel" class="py-2 px-2 font-bold text-2xl text-light-accent dark:text-dark-accent grand-total-label">Rp 0.00</span>
      </div>

      <!-- Form Input Judul AHSP, Kode dan Detail -->
      <form id="ahspForm" action="{{ route('ahs.store') }}" method="POST">
        @csrf

        <div class="flex flex-wrap space-x-0 md:space-x-4 mb-6">
          <div class="mb-4 md:mb-0 font-semibold">
            <label for="kodeAHSP" class="block mb-2 font-semibold text-center">Kode:</label>
            <input type="text" id="kodeAHSP" name="kode" class="text-center hover:bg-gray-100 dark:hover:bg-gray-700 border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 w-20 p-2 rounded-md" placeholder="Kode" required>
          </div>
          <div class="mb-4 md:mb-0 font-semibold w-full md:w-auto md:flex-1">
            <label for="judulAHSP" class="block mb-2 font-semibold text-center">Judul</label>
            <input type="text" id="judulAHSP" name="judul" class="w-full text-center hover:bg-gray-100 dark:hover:bg-gray-700 border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 p-2 rounded-md" placeholder="Masukkan Judul AHSP" required>
          </div>
          <div class="mb-4 md:mb-0 font-semibold">
            <label for="satuanAHSP" class="block mb-2 font-semibold text-center">Satuan</label>
            <select id="satuanAHSP" name="satuan" class="text-center hover:bg-gray-100 dark:hover:bg-gray-700 border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 w-20 p-2 rounded-md" required>
              <!-- Options akan diisi oleh JavaScript -->
            </select>

            <input type="text" id="satuanAHSPInput" name="satuan" class="text-center hover:bg-gray-100 dark:hover:bg-gray-700 border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 w-20 p-2 rounded-md hidden" readonly>
          </div>
          <div class="font-semibold">
            <label for="sumberAHSP" class="block mb-2 font-semibold text-center">Sumber</label>
            <input type="text" id="sumberAHSP" name="sumber" class="text-center hover:bg-gray-100 dark:hover:bg-gray-700 border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 w-28 p-2 rounded-md" placeholder="Masukkan Sumber" required>
          </div>
        </div>

        <!-- Tabel Detail AHSP -->
        <div class="overflow-x-auto overflow-y-auto">
          <table class="text-sm w-full text-left border-collapse" id="ahspTable">
            <thead class="bg-blue-200 dark:bg-dark-accent/30 sticky top-0 z-10">
              <tr>
                <th class="p-2 border dark:border-gray-600">No.</th>
                <th class="p-2 border dark:border-gray-600">Uraian Kategori</th>
                <th class="p-2 border dark:border-gray-600">Koefisien</th>
                <th class="p-2 border dark:border-gray-600">Satuan</th>
                <th class="p-2 border dark:border-gray-600">Harga Dasar</th>
                <th class="p-2 border dark:border-gray-600">Jumlah Harga</th>
                <th class="p-2 border dark:border-gray-600 text-center">Aksi</th>
              </tr>
            </thead>
            <tbody>
              <!-- Kategori UPAH -->
              <tr class="bg-blue-100 dark:bg-dark-accent/20">
                <td colspan="6" class="p-2 font-semibold justify-between items-center">
                  A. UPAH
                </td>
                <td class="p-2 font-semibold text-center flexitems-center">
                  <button type="button" onclick="openDetailModal('Upah')" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-2 py-1 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                    <i class="fas fa-plus-circle"></i>
                  </button>
                </td>
              </tr>
              <tbody id="tempUpah"></tbody>
              <tr class="bg-gray-100 dark:bg-dark-bg/50">
                <td colspan="5" class="p-2 border dark:border-gray-600 text-right font-semibold">TOTAL HARGA</td>
                <td id="totalUpahCell" class="p-2 border dark:border-gray-600 font-bold w-auto max-w-[110px] truncate hover:max-w-none">Rp 0.00</td>
                <td class="p-2 border dark:border-gray-600"></td>
              </tr>
              <!-- Kategori BAHAN -->
              <tr class="bg-blue-100 dark:bg-dark-accent/20">
                <td colspan="6" class="p-2 font-semibold justify-between items-center">
                  B. BAHAN
                </td>
                <td class="p-2 font-semibold text-center flexitems-center">
                  <button type="button" onclick="openDetailModal('Bahan')" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-2 py-1 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                    <i class="fas fa-plus-circle"></i>
                  </button>
                </td>
              </tr>
              <tbody id="tempBahan"></tbody>
              <tr class="bg-gray-100 dark:bg-dark-bg/50">
                <td colspan="5" class="p-2 border dark:border-gray-600 text-right font-semibold">TOTAL HARGA</td>
                <td id="totalBahanCell" class="p-2 border dark:border-gray-600 font-bold w-auto max-w-[110px] truncate hover:max-w-none">Rp 0.00</td>
                <td class="p-2 border dark:border-gray-600"></td>
              </tr>
              <!-- Kategori ALAT -->
              <tr class="bg-blue-100 dark:bg-dark-accent/20">
                <td colspan="6" class="p-2 font-semibold justify-between items-center">
                  C. ALAT
                </td>
                <td class="p-2 font-semibold text-center flexitems-center">
                  <button type="button" onclick="openDetailModal('Alat')" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-2 py-1 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                    <i class="fas fa-plus-circle"></i>
                  </button>
                </td>
              </tr>
              <tbody id="tempAlat"></tbody>
              <tr class="bg-gray-100 dark:bg-dark-bg/50">
                <td colspan="5" class="p-2 border dark:border-gray-600 text-right font-semibold">TOTAL HARGA</td>
                <td id="totalAlatCell" class="p-2 border dark:border-gray-600 font-bold w-auto max-w-[110px] truncate hover:max-w-none">Rp 0.00</td>
                <td class="p-2 border dark:border-gray-600"></td>
              </tr>
              <!-- SUMMARY -->
              <tr class="bg-blue-100 dark:bg-dark-accent/20">
                <td colspan="5" class="p-2 border dark:border-gray-600 text-left font-semibold">D. JUMLAH</td>
                <td id="subtotalCell" class="p-2 border dark:border-gray-600 font-bold w-auto max-w-[110px] truncate hover:max-w-none">Rp 0.00</td>
                <td class="p-2 border dark:border-gray-600"></td>
              </tr>
              <tr class="bg-blue-100 dark:bg-dark-accent/20">
                <td class="p-2 border dark:border-gray-600 text-left font-semibold">E. Overhead & Profit</td>
                <td colspan="4" class="p-2 border dark:border-gray-600">
                  <input type="number" id="overheadInput" name="overhead" class="bg-blue-100 dark:bg-dark-accent/20 hover:bg-gray-100 dark:hover:bg-gray-700 border dark:border-gray-600 p-2 w-20 text-right rounded-md" value="10" min="0" step="0.01"> %
                </td>
                <td id="overheadCell" class="p-2 border dark:border-gray-600 font-bold w-auto max-w-[110px] truncate hover:max-w-none">Rp 0.00</td>
                <td class="p-2 border dark:border-gray-600"></td>
              </tr>
              <tr class="bg-blue-200 dark:bg-dark-accent/30">
                <td colspan="5" class="p-2 border dark:border-gray-600 text-left font-semibold">F. Harga Satuan Pekerjaan (grand total)</td>
                <td id="grandTotalCell" class="p-2 border dark:border-gray-600 font-bold w-auto max-w-[110px] truncate hover:max-w-none">Rp 0.00</td>
                <td class="p-2 border dark:border-gray-600"></td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Tombol untuk Tambah Data dan Simpan Perubahan -->
        <div class="mt-6 border-t dark:border-dark-border pt-4 flex justify-end space-x-3">
          <button type="button" onclick="closeFullFormModal()" class="bg-gray-500 hover:bg-gray-700 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150">
            <i class="fas fa-times-circle"></i> Batal
          </button>
          <button id="btnTambah" type="button" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
            <i class="fas fa-plus-circle"></i> Tambah
          </button>
          <button id="btnTambah1" type="button" class="hidden bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
            <i class="fas fa-plus-circle"></i> Tambah
          </button>
          <button id="btnUpdate" type="button" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg hidden transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
            <i class="fas fa-save"></i> Simpan Perubahan
          </button>
          <button id="btnUpdate1" type="button" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg hidden transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
            <i class="fas fa-save"></i> Simpan Perubahan 1
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal Detail Input per Kategori -->
<div id="detailModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-3xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h2 id="detailModalTitle" class="text-white dark:text-dark-text text-lg font-semibold">
          Pilih Item <span id="selectedCategoryLabel"></span>
        </h2>
        <button type="button" onclick="closeDetailModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
    <!-- Input Search -->
    <div class="mb-4 flex gap-2 items-center justify-between">
      <div class="mb-4 flex-1">
        <input type="text" id="detailSearch" placeholder="Cari data..." class="w-full p-2 border rounded">
      </div>
      <button type="button" onclick="openTambahModal()"
                class="mb-4 bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg flex items-center whitespace-nowrap shadow-sm hover:shadow transform hover:scale-105 transition-all duration-150">
          <i class="fas fa-plus-circle mr-2"></i> <span id="tambahButtonText">Tambahkan</span>
      </button>
    </div>
    <!-- Tabel Data Master -->
      <div class="overflow-x-auto overflow-y-auto">
      <table class="text-sm w-full text-left border-collapse" id="masterTable">
          <thead class="bg-blue-200 dark:bg-dark-accent/30 sticky top-0 z-10">
          <tr>
            <th class="p-2 border text-center">Pilih</th>
            <th class="p-2 border">No.</th>
            <th class="p-2 border">Nama Item</th>
            <th class="p-2 border">Satuan</th>
            <th class="p-2 border">Harga</th>
            <th class="p-2 border">Sumber</th> <!-- Moved column -->
            <th class="p-2 border">Koefisien</th>
          </tr>
        </thead>
        <tbody id="masterTableBody">
          <!-- Baris data akan diisi secara dinamis oleh JavaScript -->
        </tbody>
      </table>
    </div>
    <!-- Pagination Container for Detail Modal -->
    <div id="detailModalPaginationControls" class="px-4 py-2">
        <!-- Pagination controls akan diisi oleh JavaScript -->
    </div>
    <!-- Tombol Aksi Modal -->
    <div class="text-sm flex justify-end space-x-4 mt-4">
        <button type="button" onclick="closeDetailModal()" class="bg-gray-500 hover:bg-gray-700 hover:text-blue-100 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150">
        <i class="fas fa-times-circle"></i> Batal
      </button>
        <button type="button" onclick="saveDetail()" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150 shadow-sm hover:shadow transform hover:scale-105">
        <i class="fas fa-save"></i> Simpan
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Context Menu untuk Detail Modal (hanya tampil di dalam modal full form) -->
<div id="detail-context-menu" class="hidden absolute bg-white dark:bg-dark-bg-secondary shadow-lg rounded-md py-2 w-32 border border-gray-200 dark:border-dark-accent/20 z-50 text-sm">
    <button onclick="handleDetailEdit(window.currentDetail.kategori, window.currentDetail.index)" class="w-full px-4 py-2 text-left text-blue-500 hover:bg-blue-100 hover:text-blue-700 text-sm flex items-center">
        <i class="fas fa-edit mr-2"></i> Edit
    </button>
    <button onclick="handleDetailDelete(window.currentDetail.kategori, window.currentDetail.index)" class="w-full px-4 py-2 text-left hover:bg-blue-100 text-sm flex items-center hover:text-red-700 text-red-500">
        <i class="fas fa-trash-alt mr-2"></i> Hapus
    </button>
</div>

<!-- Modal Edit Detail Item -->
<div id="editItemModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-md relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h2 id="editItemTitle" class="text-white dark:text-dark-text text-lg font-semibold">Edit Item</h2>
        <button type="button" onclick="closeEditItemModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
      </div>
    </div>

    <!-- Content -->
    <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
      <form id="editItemForm" class="space-y-4">
        <!-- Hidden fields for item metadata -->
        <input type="hidden" id="editItemId" name="editItemId">
        <input type="hidden" id="editItemCategory" name="editItemCategory">
        <input type="hidden" id="editItemIndex" name="editItemIndex">

        <!-- Item Name / Description -->
        <div>
          <label for="editItemText" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Uraian / Nama Item</label>
          <input type="text" id="editItemText" name="editItemText" class="w-full p-2 border rounded hover:bg-gray-100 dark:bg-dark-card dark:border-gray-600" required>
        </div>

        <!-- Satuan (as label, not editable) -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Satuan</label>
          <div id="editItemSatuanLabel" class="p-2 bg-gray-100 dark:bg-dark-bg-secondary border rounded dark:border-dark-accent/20 dark:text-gray-300">-</div>
          <input type="hidden" id="editItemSatuan" name="editItemSatuan">
        </div>

        <!-- Harga Dasar -->
        <div>
          <label for="editItemHarga" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Harga Dasar</label>
          <input type="number" step="0.01" min="0" id="editItemHarga" name="editItemHarga" class="w-full p-2 border rounded hover:bg-gray-100 dark:bg-dark-card dark:border-gray-600" required>
        </div>

        <!-- Koefisien -->
        <div>
          <label for="editItemKoefisien" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Koefisien</label>
          <input type="number" step="0.01" min="0.01" id="editItemKoefisien" name="editItemKoefisien" class="w-full p-2 border rounded hover:bg-gray-100 dark:bg-dark-card dark:border-gray-600" required>
        </div>

        <!-- Buttons -->
        <div class="flex justify-end space-x-2 mt-4">
          <button type="button" onclick="closeEditItemModal()" class="bg-gray-500 hover:bg-gray-700 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150">
            <i class="fas fa-times-circle"></i> Batal
          </button>
          <button type="button" onclick="saveItemEdit()" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150 shadow-sm hover:shadow transform hover:scale-105">
            <i class="fas fa-save"></i> Simpan
          </button>
        </div>
      </form>
    </div>
  </div>
</div>


