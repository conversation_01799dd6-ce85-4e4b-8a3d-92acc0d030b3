<?php

namespace App\Http\Middleware;

use App\Models\Project;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Symfony\Component\HttpFoundation\Response;

class CheckProjectLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Jika bukan request POST ke /projects atau /projects/{project}/duplicate, lewati pemeriksaan
        if (!($request->isMethod('post') && ($request->is('projects') || $request->is('projects/*/duplicate')))) {
            return $next($request);
        }

        $user = Auth::user();

        // Admin selalu memiliki akses penuh
        if ($user->role === 'admin') {
            return $next($request);
        }

        // Jika user dalam masa trial, berikan akses penuh
        if ($user->trial_ends_at && $user->trial_ends_at > Carbon::now()) {
            return $next($request);
        }

        // Jika user tidak memiliki langganan aktif
        if (
            !$user->currentSubscription ||
            !($user->currentSubscription->status === 'active' && $user->currentSubscription->end_date > Carbon::now())
        ) {
            // Jika request adalah AJAX, kembalikan response JSON
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda memerlukan langganan aktif untuk mengakses fitur ini.'
                ], 403);
            }

            // Redirect ke halaman langganan dengan pesan error
            return redirect()->route('subscriptions.index')
                ->with('error', 'Anda memerlukan langganan aktif untuk mengakses fitur ini.');
        }

        // Hitung jumlah proyek yang dimiliki user
        $projectCount = Project::where('user_id', $user->id)->count();

        // Dapatkan batas proyek dari paket langganan
        $subscription = $user->currentSubscription;
        $plan = $subscription ? $subscription->plan : null;

        if ($plan && $projectCount >= $plan->project_limit) {
            // Jika request adalah AJAX, kembalikan response JSON
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda telah mencapai batas jumlah proyek untuk paket langganan Anda. Silakan upgrade paket Anda untuk membuat lebih banyak proyek.'
                ], 403);
            }

            // Redirect ke halaman langganan dengan pesan error
            return redirect()->route('subscriptions.index')
                ->with('error', 'Anda telah mencapai batas jumlah proyek untuk paket langganan Anda. Silakan upgrade paket Anda untuk membuat lebih banyak proyek.');
        }

        return $next($request);
    }
}
