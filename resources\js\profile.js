// Profile Management JavaScript

// MD5 function for Gravatar
function md5(string) {
    // This is a simple implementation of the MD5 algorithm
    // Source: https://css-tricks.com/snippets/javascript/javascript-md5/
    function M(d) {
        for (var _, m = "0123456789abcdef", f = "", r = 0; r < d.length; r++)
            (_ = d.charCodeAt(r)),
                (f += m.charAt((_ >>> 4) & 15) + m.charAt(15 & _));
        return f;
    }
    function X(d) {
        for (var _ = Array(d.length >> 2), m = 0; m < _.length; m++) _[m] = 0;
        for (m = 0; m < 8 * d.length; m += 8)
            _[m >> 5] |= (255 & d.charCodeAt(m / 8)) << m % 32;
        return _;
    }
    function V(d) {
        for (var _ = "", m = 0; m < 32 * d.length; m += 8)
            _ += String.fromCharCode((d[m >> 5] >>> m % 32) & 255);
        return _;
    }
    function Y(d, _) {
        (d[_ >> 5] |= 128 << _ % 32), (d[14 + (((_ + 64) >>> 9) << 4)] = _);
        for (
            var m = 1732584193,
                f = -271733879,
                r = -1732584194,
                i = 271733878,
                n = 0;
            n < d.length;
            n += 16
        ) {
            var h = m,
                t = f,
                g = r,
                e = i;
            (f = md5_ii(
                (f = md5_ii(
                    (f = md5_ii(
                        (f = md5_ii(
                            (f = md5_hh(
                                (f = md5_hh(
                                    (f = md5_hh(
                                        (f = md5_hh(
                                            (f = md5_gg(
                                                (f = md5_gg(
                                                    (f = md5_gg(
                                                        (f = md5_gg(
                                                            (f = md5_ff(
                                                                (f = md5_ff(
                                                                    (f = md5_ff(
                                                                        (f =
                                                                            md5_ff(
                                                                                f,
                                                                                (r =
                                                                                    md5_ff(
                                                                                        r,
                                                                                        (i =
                                                                                            md5_ff(
                                                                                                i,
                                                                                                (m =
                                                                                                    md5_ff(
                                                                                                        m,
                                                                                                        f,
                                                                                                        r,
                                                                                                        i,
                                                                                                        d[
                                                                                                            n +
                                                                                                                0
                                                                                                        ],
                                                                                                        7,
                                                                                                        -680876936
                                                                                                    )),
                                                                                                f,
                                                                                                r,
                                                                                                d[
                                                                                                    n +
                                                                                                        1
                                                                                                ],
                                                                                                12,
                                                                                                -389564586
                                                                                            )),
                                                                                        m,
                                                                                        f,
                                                                                        d[
                                                                                            n +
                                                                                                2
                                                                                        ],
                                                                                        17,
                                                                                        606105819
                                                                                    )),
                                                                                i,
                                                                                m,
                                                                                d[
                                                                                    n +
                                                                                        3
                                                                                ],
                                                                                22,
                                                                                -1044525330
                                                                            )),
                                                                        (r =
                                                                            md5_ff(
                                                                                r,
                                                                                (i =
                                                                                    md5_ff(
                                                                                        i,
                                                                                        (m =
                                                                                            md5_ff(
                                                                                                m,
                                                                                                f,
                                                                                                r,
                                                                                                i,
                                                                                                d[
                                                                                                    n +
                                                                                                        4
                                                                                                ],
                                                                                                7,
                                                                                                -176418897
                                                                                            )),
                                                                                        f,
                                                                                        r,
                                                                                        d[
                                                                                            n +
                                                                                                5
                                                                                        ],
                                                                                        12,
                                                                                        1200080426
                                                                                    )),
                                                                                m,
                                                                                f,
                                                                                d[
                                                                                    n +
                                                                                        6
                                                                                ],
                                                                                17,
                                                                                -1473231341
                                                                            )),
                                                                        i,
                                                                        m,
                                                                        d[
                                                                            n +
                                                                                7
                                                                        ],
                                                                        22,
                                                                        -45705983
                                                                    )),
                                                                    (r = md5_ff(
                                                                        r,
                                                                        (i =
                                                                            md5_ff(
                                                                                i,
                                                                                (m =
                                                                                    md5_ff(
                                                                                        m,
                                                                                        f,
                                                                                        r,
                                                                                        i,
                                                                                        d[
                                                                                            n +
                                                                                                8
                                                                                        ],
                                                                                        7,
                                                                                        1770035416
                                                                                    )),
                                                                                f,
                                                                                r,
                                                                                d[
                                                                                    n +
                                                                                        9
                                                                                ],
                                                                                12,
                                                                                -1958414417
                                                                            )),
                                                                        m,
                                                                        f,
                                                                        d[
                                                                            n +
                                                                                10
                                                                        ],
                                                                        17,
                                                                        -42063
                                                                    )),
                                                                    i,
                                                                    m,
                                                                    d[n + 11],
                                                                    22,
                                                                    -1990404162
                                                                )),
                                                                (r = md5_ff(
                                                                    r,
                                                                    (i = md5_ff(
                                                                        i,
                                                                        (m =
                                                                            md5_ff(
                                                                                m,
                                                                                f,
                                                                                r,
                                                                                i,
                                                                                d[
                                                                                    n +
                                                                                        12
                                                                                ],
                                                                                7,
                                                                                1804603682
                                                                            )),
                                                                        f,
                                                                        r,
                                                                        d[
                                                                            n +
                                                                                13
                                                                        ],
                                                                        12,
                                                                        -40341101
                                                                    )),
                                                                    m,
                                                                    f,
                                                                    d[n + 14],
                                                                    17,
                                                                    -1502002290
                                                                )),
                                                                i,
                                                                m,
                                                                d[n + 15],
                                                                22,
                                                                1236535329
                                                            )),
                                                            (r = md5_gg(
                                                                r,
                                                                (i = md5_gg(
                                                                    i,
                                                                    (m = md5_gg(
                                                                        m,
                                                                        f,
                                                                        r,
                                                                        i,
                                                                        d[
                                                                            n +
                                                                                1
                                                                        ],
                                                                        5,
                                                                        -165796510
                                                                    )),
                                                                    f,
                                                                    r,
                                                                    d[n + 6],
                                                                    9,
                                                                    -1069501632
                                                                )),
                                                                m,
                                                                f,
                                                                d[n + 11],
                                                                14,
                                                                643717713
                                                            )),
                                                            i,
                                                            m,
                                                            d[n + 0],
                                                            20,
                                                            -373897302
                                                        )),
                                                        (r = md5_gg(
                                                            r,
                                                            (i = md5_gg(
                                                                i,
                                                                (m = md5_gg(
                                                                    m,
                                                                    f,
                                                                    r,
                                                                    i,
                                                                    d[n + 5],
                                                                    5,
                                                                    -701558691
                                                                )),
                                                                f,
                                                                r,
                                                                d[n + 10],
                                                                9,
                                                                38016083
                                                            )),
                                                            m,
                                                            f,
                                                            d[n + 15],
                                                            14,
                                                            -660478335
                                                        )),
                                                        i,
                                                        m,
                                                        d[n + 4],
                                                        20,
                                                        -405537848
                                                    )),
                                                    (r = md5_gg(
                                                        r,
                                                        (i = md5_gg(
                                                            i,
                                                            (m = md5_gg(
                                                                m,
                                                                f,
                                                                r,
                                                                i,
                                                                d[n + 9],
                                                                5,
                                                                568446438
                                                            )),
                                                            f,
                                                            r,
                                                            d[n + 14],
                                                            9,
                                                            -1019803690
                                                        )),
                                                        m,
                                                        f,
                                                        d[n + 3],
                                                        14,
                                                        -187363961
                                                    )),
                                                    i,
                                                    m,
                                                    d[n + 8],
                                                    20,
                                                    1163531501
                                                )),
                                                (r = md5_gg(
                                                    r,
                                                    (i = md5_gg(
                                                        i,
                                                        (m = md5_gg(
                                                            m,
                                                            f,
                                                            r,
                                                            i,
                                                            d[n + 13],
                                                            5,
                                                            -1444681467
                                                        )),
                                                        f,
                                                        r,
                                                        d[n + 2],
                                                        9,
                                                        -51403784
                                                    )),
                                                    m,
                                                    f,
                                                    d[n + 7],
                                                    14,
                                                    1735328473
                                                )),
                                                i,
                                                m,
                                                d[n + 12],
                                                20,
                                                -1926607734
                                            )),
                                            (r = md5_hh(
                                                r,
                                                (i = md5_hh(
                                                    i,
                                                    (m = md5_hh(
                                                        m,
                                                        f,
                                                        r,
                                                        i,
                                                        d[n + 5],
                                                        4,
                                                        -378558
                                                    )),
                                                    f,
                                                    r,
                                                    d[n + 8],
                                                    11,
                                                    -2022574463
                                                )),
                                                m,
                                                f,
                                                d[n + 11],
                                                16,
                                                1839030562
                                            )),
                                            i,
                                            m,
                                            d[n + 14],
                                            23,
                                            -35309556
                                        )),
                                        (r = md5_hh(
                                            r,
                                            (i = md5_hh(
                                                i,
                                                (m = md5_hh(
                                                    m,
                                                    f,
                                                    r,
                                                    i,
                                                    d[n + 1],
                                                    4,
                                                    -1530992060
                                                )),
                                                f,
                                                r,
                                                d[n + 4],
                                                11,
                                                1272893353
                                            )),
                                            m,
                                            f,
                                            d[n + 7],
                                            16,
                                            -155497632
                                        )),
                                        i,
                                        m,
                                        d[n + 10],
                                        23,
                                        -1094730640
                                    )),
                                    (r = md5_hh(
                                        r,
                                        (i = md5_hh(
                                            i,
                                            (m = md5_hh(
                                                m,
                                                f,
                                                r,
                                                i,
                                                d[n + 13],
                                                4,
                                                681279174
                                            )),
                                            f,
                                            r,
                                            d[n + 0],
                                            11,
                                            -358537222
                                        )),
                                        m,
                                        f,
                                        d[n + 3],
                                        16,
                                        -722521979
                                    )),
                                    i,
                                    m,
                                    d[n + 6],
                                    23,
                                    76029189
                                )),
                                (r = md5_hh(
                                    r,
                                    (i = md5_hh(
                                        i,
                                        (m = md5_hh(
                                            m,
                                            f,
                                            r,
                                            i,
                                            d[n + 9],
                                            4,
                                            -640364487
                                        )),
                                        f,
                                        r,
                                        d[n + 12],
                                        11,
                                        -421815835
                                    )),
                                    m,
                                    f,
                                    d[n + 15],
                                    16,
                                    530742520
                                )),
                                i,
                                m,
                                d[n + 2],
                                23,
                                -995338651
                            )),
                            (r = md5_ii(
                                r,
                                (i = md5_ii(
                                    i,
                                    (m = md5_ii(
                                        m,
                                        f,
                                        r,
                                        i,
                                        d[n + 0],
                                        6,
                                        -198630844
                                    )),
                                    f,
                                    r,
                                    d[n + 7],
                                    10,
                                    1126891415
                                )),
                                m,
                                f,
                                d[n + 14],
                                15,
                                -1416354905
                            )),
                            i,
                            m,
                            d[n + 5],
                            21,
                            -57434055
                        )),
                        (r = md5_ii(
                            r,
                            (i = md5_ii(
                                i,
                                (m = md5_ii(
                                    m,
                                    f,
                                    r,
                                    i,
                                    d[n + 12],
                                    6,
                                    1700485571
                                )),
                                f,
                                r,
                                d[n + 3],
                                10,
                                -1894986606
                            )),
                            m,
                            f,
                            d[n + 10],
                            15,
                            -1051523
                        )),
                        i,
                        m,
                        d[n + 1],
                        21,
                        -2054922799
                    )),
                    (r = md5_ii(
                        r,
                        (i = md5_ii(
                            i,
                            (m = md5_ii(m, f, r, i, d[n + 8], 6, 1873313359)),
                            f,
                            r,
                            d[n + 15],
                            10,
                            -30611744
                        )),
                        m,
                        f,
                        d[n + 6],
                        15,
                        -1560198380
                    )),
                    i,
                    m,
                    d[n + 13],
                    21,
                    1309151649
                )),
                (r = md5_ii(
                    r,
                    (i = md5_ii(
                        i,
                        (m = md5_ii(m, f, r, i, d[n + 4], 6, -145523070)),
                        f,
                        r,
                        d[n + 11],
                        10,
                        -1120210379
                    )),
                    m,
                    f,
                    d[n + 2],
                    15,
                    718787259
                )),
                i,
                m,
                d[n + 9],
                21,
                -343485551
            )),
                (m = safe_add(m, h)),
                (f = safe_add(f, t)),
                (r = safe_add(r, g)),
                (i = safe_add(i, e));
        }
        return Array(m, f, r, i);
    }
    function md5_cmn(d, _, m, f, r, i) {
        return safe_add(
            bit_rol(safe_add(safe_add(_, d), safe_add(f, i)), r),
            m
        );
    }
    function md5_ff(d, _, m, f, r, i, n) {
        return md5_cmn((_ & m) | (~_ & f), d, _, r, i, n);
    }
    function md5_gg(d, _, m, f, r, i, n) {
        return md5_cmn((_ & f) | (m & ~f), d, _, r, i, n);
    }
    function md5_hh(d, _, m, f, r, i, n) {
        return md5_cmn(_ ^ m ^ f, d, _, r, i, n);
    }
    function md5_ii(d, _, m, f, r, i, n) {
        return md5_cmn(m ^ (_ | ~f), d, _, r, i, n);
    }
    function safe_add(d, _) {
        var m = (65535 & d) + (65535 & _);
        return (((d >> 16) + (_ >> 16) + (m >> 16)) << 16) | (65535 & m);
    }
    function bit_rol(d, _) {
        return (d << _) | (d >>> (32 - _));
    }
    return M(V(Y(X(string), 8 * string.length)));
}

// Inisialisasi modal
document.addEventListener("DOMContentLoaded", function () {
    // Inisialisasi modal jika ada
    const profileModal = document.getElementById("profileModal");
    if (profileModal) {
        // Opsi untuk mencegah modal tertutup saat klik di luar
        const options = {
            placement: "center",
            backdrop: "static",
            backdropClasses: "bg-black bg-opacity-50 fixed inset-0 z-40",
            closable: false,
        };
        window.profileModal = new Modal(profileModal, options);
    }

    // Tambahkan event listener untuk tombol profil
    const profileButtons = document.querySelectorAll(".profile-button");
    profileButtons.forEach((button) => {
        button.addEventListener("click", function (e) {
            e.preventDefault();
            openProfileModal();
        });
    });

    // Tambahkan event listener untuk format nomor telepon
    const phoneInput = document.getElementById("profile-phone");
    if (phoneInput) {
        phoneInput.addEventListener("input", function (e) {
            // Hapus semua karakter non-digit
            let value = e.target.value.replace(/\D/g, "");

            // Pastikan nomor dimulai dengan 8
            if (value.length > 0 && value[0] !== "8") {
                if (value[0] === "0") {
                    // Jika dimulai dengan 0, hapus 0 tersebut
                    value = value.substring(1);
                } else if (value.startsWith("62")) {
                    // Jika dimulai dengan 62, hapus 62 tersebut
                    value = value.substring(2);
                }

                // Pastikan setelah penghapusan, nomor dimulai dengan 8
                if (value.length > 0 && value[0] !== "8") {
                    value = "8" + value;
                }
            }

            // Batasi panjang nomor (8 + 11 digit)
            if (value.length > 12) {
                value = value.substring(0, 12);
            }

            // Update nilai input
            e.target.value = value;
        });
    }

    // Periksa apakah ada pesan sukses dari update profil sebelumnya
    const successMessage = localStorage.getItem("profileSuccessMessage");
    const successTime = localStorage.getItem("profileSuccessTime");

    if (successMessage && successTime) {
        // Pastikan pesan tidak lebih dari 1 menit yang lalu
        const currentTime = new Date().getTime();
        const messageTime = parseInt(successTime);

        if (currentTime - messageTime < 60000) {
            // 60 detik
            // Tampilkan notifikasi sukses
            setTimeout(() => {
                window.showSuccessToast(successMessage, "Berhasil");
            }, 500); // Delay sedikit untuk memastikan halaman sudah dimuat dengan baik
        }

        // Hapus pesan dari localStorage
        localStorage.removeItem("profileSuccessMessage");
        localStorage.removeItem("profileSuccessTime");
    }
});

// Fungsi untuk membuka modal profil
window.openProfileModal = function () {
    // Ambil data profil dari server
    fetch("/profile", {
        method: "GET",
        headers: {
            "X-Requested-With": "XMLHttpRequest",
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]')
                .content,
        },
    })
        .then((response) => response.json())
        .then((data) => {
            if (data.success) {
                // Isi form dengan data profil
                const user = data.data;
                document.getElementById("profile-name").value = user.name;
                document.getElementById("profile-email").value = user.email;
                document.getElementById("profile-address").value =
                    user.address || "";

                // Format nomor telepon untuk Indonesia
                let phoneNumber = user.phone || "";
                // Hapus +62 atau 62 di awal jika ada
                if (phoneNumber.startsWith("+62")) {
                    phoneNumber = phoneNumber.substring(3);
                } else if (phoneNumber.startsWith("62")) {
                    phoneNumber = phoneNumber.substring(2);
                }
                // Hapus 0 di awal jika ada
                if (phoneNumber.startsWith("0")) {
                    phoneNumber = phoneNumber.substring(1);
                }
                document.getElementById("profile-phone").value = phoneNumber;

                // Tampilkan foto profil
                const photoPreview = document.getElementById(
                    "profile-photo-preview"
                );
                const defaultIcon = document.getElementById(
                    "profile-default-icon"
                );

                // Pastikan ikon default selalu tersembunyi
                defaultIcon.classList.add("hidden");

                if (user.photo) {
                    // Gunakan foto yang diupload user
                    photoPreview.src = user.photo;
                } else {
                    // Gunakan Gravatar berdasarkan email (sebagai fallback jika blade directive tidak berfungsi)
                    const email = user.email.trim().toLowerCase();
                    const hash = md5(email);
                    photoPreview.src = `https://www.gravatar.com/avatar/${hash}?d=identicon&s=200`;
                }

                // Pastikan foto profil selalu terlihat
                photoPreview.classList.remove("hidden");

                // Tampilkan modal
                window.profileModal.show();
            } else {
                window.showErrorToast("Gagal mengambil data profil", "Error");
            }
        })
        .catch((error) => {
            console.error("Error:", error);
            window.showErrorToast(
                "Terjadi kesalahan saat mengambil data profil",
                "Error"
            );
        });
};

// Tambahkan variabel global untuk melacak status reset foto
window.photoResetFlag = false;

// Fungsi untuk menyimpan profil
window.saveProfile = function () {
    const form = document.getElementById("profile-form");
    const formData = new FormData(form);

    // Format nomor telepon dengan menambahkan +62 di awal
    const phoneInput = document.getElementById("profile-phone");
    if (phoneInput && phoneInput.value) {
        // Pastikan nomor telepon dimulai dengan 8
        let phoneNumber = phoneInput.value.replace(/\D/g, "");
        if (
            phoneNumber &&
            !phoneNumber.startsWith("+62") &&
            !phoneNumber.startsWith("62")
        ) {
            // Tambahkan +62 di awal
            formData.set("phone", "+62" + phoneNumber);
        }
    }

    // Tambahkan flag reset foto jika diperlukan
    if (window.photoResetFlag) {
        formData.append("reset_photo", "true");
    }

    // Tambahkan CSRF token
    formData.append(
        "_token",
        document.querySelector('meta[name="csrf-token"]').content
    );

    // Tampilkan loading
    const saveButton = document.getElementById("profile-save-button");
    saveButton.classList.add("loading-btn");
    const spinner = document.createElement("i");
    spinner.className = "fas fa-spinner fa-spin mr-2 spinner";
    saveButton.innerHTML = "";
    saveButton.appendChild(spinner);
    saveButton.appendChild(document.createTextNode("Memproses..."));
    saveButton.disabled = true;

    // Kirim request
    fetch("/profile", {
        method: "POST",
        body: formData,
    })
        .then((response) => response.json())
        .then((data) => {
            // Kembalikan tombol ke keadaan semula
            saveButton.innerHTML = '<i class="fas fa-save mr-1"></i> Simpan';
            saveButton.classList.remove("loading-btn");
            saveButton.disabled = false;

            if (data.success) {
                // Simpan pesan sukses di localStorage untuk ditampilkan setelah reload
                localStorage.setItem("profileSuccessMessage", data.message);
                localStorage.setItem(
                    "profileSuccessTime",
                    new Date().getTime().toString()
                );

                // Update foto profil di navbar
                const navbarPhoto = document.querySelector(
                    ".navbar-profile-photo"
                );
                const defaultIcon = document.querySelector(
                    ".navbar-profile-icon"
                );

                // Sembunyikan ikon default
                if (defaultIcon) {
                    defaultIcon.classList.add("hidden");
                }

                if (data.data.photo) {
                    // Gunakan foto yang diupload user
                    if (navbarPhoto) {
                        navbarPhoto.src = data.data.photo;
                        navbarPhoto.classList.remove("hidden");
                    }
                } else {
                    // Gunakan Gravatar berdasarkan email
                    if (navbarPhoto) {
                        const email = data.data.email.trim().toLowerCase();
                        const hash = md5(email);
                        navbarPhoto.src = `https://www.gravatar.com/avatar/${hash}?d=identicon&s=200`;
                        navbarPhoto.classList.remove("hidden");
                    }
                }

                // Tutup modal dan reload halaman
                setTimeout(() => {
                    window.profileModal.hide();
                    // Reload halaman setelah modal tertutup tanpa loading-overlay
                    setTimeout(() => {
                        // Tandai bahwa ini adalah reload dari profil modal
                        localStorage.setItem("profileReload", "true");
                        localStorage.setItem(
                            "profileReloadTime",
                            new Date().getTime().toString()
                        );

                        // Sembunyikan loading-overlay sebelum reload
                        if (window.hideLoading) {
                            window.hideLoading();
                        }

                        // Reload halaman
                        window.location.reload();
                    }, 300);
                }, 300);
            } else {
                window.showErrorToast(data.message, "Gagal");
                console.error("Errors:", data.errors);
            }
        })
        .catch((error) => {
            // Kembalikan tombol ke keadaan semula
            saveButton.innerHTML = '<i class="fas fa-save mr-1"></i> Simpan';
            saveButton.classList.remove("loading-btn");
            saveButton.disabled = false;

            console.error("Error:", error);
            window.showErrorToast(
                "Terjadi kesalahan saat menyimpan profil",
                "Error"
            );
        });
};

// Fungsi untuk menampilkan preview foto
window.previewProfilePhoto = function (input) {
    // Reset flag foto jika mengunggah foto baru
    window.photoResetFlag = false;

    const preview = document.getElementById("profile-photo-preview");
    const defaultIcon = document.getElementById("profile-default-icon");
    const file = input.files[0];

    // Pastikan ikon default selalu tersembunyi
    defaultIcon.classList.add("hidden");

    if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
            preview.src = e.target.result;
            preview.classList.remove("hidden");
        };
        reader.readAsDataURL(file);
    }
};

// Fungsi untuk reset foto profil
window.resetProfilePhoto = function () {
    // Tampilkan konfirmasi menggunakan showCustomConfirm
    window.showCustomConfirm(
        "Apakah Anda yakin ingin menghapus foto profil?",
        function () {
            // Tandai bahwa foto harus direset saat menyimpan
            window.photoResetFlag = true;

            // Update UI tanpa mengirim request
            const photoPreview = document.getElementById(
                "profile-photo-preview"
            );
            const email = document
                .getElementById("profile-email")
                .value.trim()
                .toLowerCase();
            const hash = md5(email);
            photoPreview.src = `https://www.gravatar.com/avatar/${hash}?d=identicon&s=200`;

            // Tampilkan notifikasi bahwa foto akan dihapus setelah disimpan
            window.showSuccessToast(
                "Foto akan dihapus setelah Anda menyimpan profil",
                "Info"
            );
        },
        function () {
            // Kode yang dijalankan jika user menekan tombol "Tidak"
            console.log("Reset foto profil dibatalkan");
        }
    );
};
