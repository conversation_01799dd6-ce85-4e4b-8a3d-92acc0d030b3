// DHTMLX Gantt Chart Implementation
window.initDhtmlxGantt = function () {
    console.log("Initializing DHTMLX Gantt Chart");

    // Pastikan container ada
    const ganttContainer = document.getElementById("gantt-container");
    if (!ganttContainer) {
        console.error("Gantt container not found");
        return;
    }

    // Pastikan data tersedia
    if (!window.timeScheduleData || !window.timeScheduleData.length) {
        console.log("No data available for Gantt Chart");
        ganttContainer.innerHTML =
            '<div class="flex items-center justify-center h-full"><p class="text-gray-500">Belum ada jadwal. Tambahkan jadwal untuk melihat Gantt Chart.</p></div>';
        return;
    }

    // Inisialisasi zoom extension
    gantt.ext.zoom.init({
        levels: [
            {
                name: "day",
                scale_height: 30,
                min_column_width: 30,
                scales: [{ unit: "day", step: 1, format: "%d %M" }],
            },
            {
                name: "week",
                scale_height: 60,
                min_column_width: 50,
                scales: [
                    { unit: "week", step: 1, format: "Minggu #%W" },
                    { unit: "day", step: 1, format: "%d %M" },
                ],
            },
            {
                name: "month",
                scale_height: 60,
                min_column_width: 120,
                scales: [
                    { unit: "month", step: 1, format: "%F %Y" },
                    { unit: "week", step: 1, format: "Minggu #%W" },
                ],
            },
        ],
    });

    // Konfigurasi DHTMLX Gantt
    gantt.config.date_format = "%Y-%m-%d";
    gantt.config.drag_progress = false; // Nonaktifkan drag progress karena kita tidak menggunakan progress lagi

    // Aktifkan fitur drag-and-drop dan resize
    gantt.config.drag_move = true;
    gantt.config.drag_resize = true;

    // Konfigurasi skala waktu
    gantt.config.scale_unit = "week";
    gantt.config.step = 1;
    gantt.config.date_scale = "%d %M";
    gantt.config.subscales = [{ unit: "day", step: 1, date: "%D" }];

    // Konfigurasi tampilan
    gantt.config.min_column_width = 50;
    gantt.config.row_height = 30;

    // Deteksi mode dark/light
    const isDarkMode = document.documentElement.classList.contains("dark");

    // Sesuaikan tema berdasarkan mode dark/light
    if (isDarkMode) {
        // Tema dark
        gantt.templates.task_class = function (start, end, task) {
            return "gantt-task-dark";
        };

        // Tambahkan CSS untuk tema dark
        const darkStyle = document.createElement("style");
        darkStyle.textContent = `
            .gantt_container {
                background-color: #1e293b !important;
                color: #e2e8f0 !important;
            }
            .gantt_grid, .gantt_task, .gantt_grid_scale, .gantt_task_scale {
                background-color: #1e293b !important;
                color: #e2e8f0 !important;
            }
            .gantt_grid_scale, .gantt_task_scale {
                border-bottom: 1px solid #334155 !important;
            }
            .gantt_grid_data .gantt_row, .gantt_task_row {
                background-color: #1e293b !important;
                border-bottom: 1px solid #334155 !important;
            }
            .gantt_grid_data .gantt_row.odd, .gantt_task_row.odd {
                background-color: #0f172a !important;
            }
            .gantt_task_line {
                background-color: #4b5563 !important;
                border: 1px solid #64748b !important;
            }
            .gantt_task_content {
                color: #ffffff !important;
            }
            .gantt_grid_data .gantt_row.gantt_selected, .gantt_task_row.gantt_selected {
                background-color: #334155 !important;
            }
            .gantt_task_line.gantt_selected {
                box-shadow: 0 0 5px #60a5fa !important;
            }
        `;
        document.head.appendChild(darkStyle);
    }

    // Konfigurasi kolom grid
    gantt.config.columns = [
        { name: "text", label: "Kegiatan", tree: true, width: 200 },
        { name: "start_date", label: "Mulai", align: "center", width: 80 },
        { name: "end_date", label: "Selesai", align: "center", width: 80 },
        { name: "duration", label: "Durasi", align: "center", width: 60 },
        { name: "bobot", label: "Bobot (%)", align: "center", width: 80 },
    ];

    // Konfigurasi tooltip
    gantt.templates.tooltip_text = function (start, end, task) {
        return `<div class="font-bold">${task.text}</div>
                <div>Mulai: ${gantt.date.date_to_str("%d %F %Y")(start)}</div>
                <div>Selesai: ${gantt.date.date_to_str("%d %F %Y")(end)}</div>
                <div>Durasi: ${task.duration} hari</div>
                <div>Bobot: ${task.bobot}%</div>`;
    };

    // Event handler untuk perubahan jadwal
    gantt.attachEvent("onAfterTaskDrag", function (id, mode) {
        const task = gantt.getTask(id);
        console.log("Task updated:", task);

        // Perbarui durasi
        task.duration = gantt.calculateDuration(task.start_date, task.end_date);
        gantt.updateTask(id);

        // Simpan perubahan ke server
        saveTaskChanges(task);
    });

    // Fungsi untuk menyimpan perubahan ke server
    function saveTaskChanges(task) {
        // Konversi format tanggal
        const startDate = gantt.date.date_to_str("%Y-%m-%d")(task.start_date);
        const endDate = gantt.date.date_to_str("%Y-%m-%d")(task.end_date);

        // Siapkan data untuk dikirim
        const formData = new FormData();
        formData.append("_method", "PUT");
        formData.append("tanggal_mulai", startDate);
        formData.append("tanggal_selesai", endDate);
        formData.append("bobot", task.bobot);
        formData.append("nama_kegiatan", task.text);

        // Kirim data ke server
        fetch(`/time-schedule/${task.id}`, {
            method: "POST",
            body: formData,
            headers: {
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
                "X-Requested-With": "XMLHttpRequest",
                Accept: "application/json",
            },
        })
            .then((response) => response.json())
            .then((result) => {
                if (result.success) {
                    // Tampilkan notifikasi sukses jika tersedia
                    if (window.showSuccessToast) {
                        window.showSuccessToast("Jadwal berhasil diperbarui");
                    }

                    // Perbarui data di timeScheduleData
                    const index = window.timeScheduleData.findIndex(
                        (item) => item.id === task.id
                    );
                    if (index !== -1) {
                        window.timeScheduleData[index].start = startDate;
                        window.timeScheduleData[index].end = endDate;
                        window.timeScheduleData[index].durasi = task.duration;
                    }
                } else {
                    // Tampilkan notifikasi error jika tersedia
                    if (window.showErrorToast) {
                        window.showErrorToast(
                            result.message || "Gagal memperbarui jadwal"
                        );
                    }

                    // Kembalikan ke posisi semula
                    gantt.refreshData();
                }
            })
            .catch((error) => {
                console.error("Error saving task changes:", error);
                if (window.showErrorToast) {
                    window.showErrorToast("Gagal memperbarui jadwal");
                }

                // Kembalikan ke posisi semula
                gantt.refreshData();
            });
    }

    // Inisialisasi Gantt chart
    gantt.init(ganttContainer);

    // Konversi data dari format yang ada ke format DHTMLX Gantt
    const tasks = {
        data: window.timeScheduleData.map((item) => ({
            id: item.id,
            text: item.title,
            start_date: item.start,
            end_date: item.end,
            duration: item.durasi,
            bobot: item.bobot,
            item_id: item.item_id,
        })),
    };

    // Muat data ke Gantt chart
    gantt.parse(tasks);

    // Sesuaikan tampilan
    gantt.render();

    // Simpan instance Gantt chart
    window.dhtmlxGantt = gantt;

    // Tambahkan event listener untuk perubahan mode dark/light
    const darkModeObserver = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            if (
                mutation.attributeName === "class" &&
                mutation.target === document.documentElement
            ) {
                // Re-render Gantt chart dengan tema yang sesuai
                window.initDhtmlxGantt();
            }
        });
    });

    // Mulai observasi perubahan class pada html element
    darkModeObserver.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ["class"],
    });

    return gantt;
};

// Fungsi untuk menampilkan Gantt chart saat tab aktif
window.showDhtmlxGantt = function () {
    // Jika Gantt chart belum diinisialisasi, inisialisasi
    if (!window.dhtmlxGantt) {
        window.initDhtmlxGantt();
    } else {
        // Jika sudah diinisialisasi, sesuaikan ukuran
        window.dhtmlxGantt.render();
    }
};
