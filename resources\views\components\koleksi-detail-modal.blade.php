<!-- Modal Detail Koleksi -->
<div id="detailModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white dark:bg-dark-card rounded-xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full border-2 border-light-accent dark:border-dark-accent overflow-hidden">
            <!-- Close button -->
            <button type="button" onclick="closeDetailModal()" class="absolute top-2 right-2 bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-gray-700 dark:text-gray-300 rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>

            <!-- Modal content -->
            <div id="modalContent" class="p-6">
                <!-- Content will be loaded here via AJAX -->
                <div class="flex justify-center items-center h-40">
                    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-light-accent dark:border-dark-accent"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function openDetailModal(url) {
        // Show modal
        document.getElementById('detailModal').classList.remove('hidden');
        document.body.classList.add('overflow-hidden');

        // Fetch content
        fetch(url)
            .then(response => response.text())
            .then(html => {
                // Extract only the content section from the response
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const content = doc.querySelector('.bg-white.dark\\:bg-dark-card.rounded-lg.shadow-md.overflow-hidden');

                if (content) {
                    // Remove the back button
                    const backButton = content.querySelector('a[href*="kembali"]');
                    if (backButton && backButton.closest('div')) {
                        backButton.closest('div').remove();
                    }

                    // Set the content
                    document.getElementById('modalContent').innerHTML = content.outerHTML;
                } else {
                    document.getElementById('modalContent').innerHTML = '<div class="text-center text-red-500">Error loading content</div>';
                }
            })
            .catch(error => {
                console.error('Error fetching detail:', error);
                document.getElementById('modalContent').innerHTML = '<div class="text-center text-red-500">Error loading content</div>';
            });
    }

    function closeDetailModal() {
        document.getElementById('detailModal').classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
        document.getElementById('modalContent').innerHTML = '<div class="flex justify-center items-center h-40"><div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-light-accent dark:border-dark-accent"></div></div>';
    }

    // Close modal when clicking outside
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('detailModal');
        const modalPanel = modal.querySelector('.inline-block');

        if (modal && !modal.classList.contains('hidden') && !modalPanel.contains(event.target) && !event.target.closest('a[onclick^="openDetailModal"]')) {
            closeDetailModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeDetailModal();
        }
    });
</script>
