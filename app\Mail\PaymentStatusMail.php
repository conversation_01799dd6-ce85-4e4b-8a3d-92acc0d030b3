<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Models\Payment;

class PaymentStatusMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $payment;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, Payment $payment)
    {
        $this->user = $user;
        $this->payment = $payment;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = 'Status Pembayaran Estimateeng';
        
        if ($this->payment->status === 'completed') {
            $subject = 'Pembayaran Berhasil - Langganan Anda Aktif';
        } elseif ($this->payment->status === 'pending') {
            $subject = 'Menunggu Pembayaran - Estimateeng';
        } elseif ($this->payment->status === 'failed') {
            $subject = 'Pembayaran Gagal - Estimateeng';
        } elseif ($this->payment->status === 'processing') {
            $subject = 'Pembayaran Sedang Diproses - Estimateeng';
        }
        
        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.payment-status',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
