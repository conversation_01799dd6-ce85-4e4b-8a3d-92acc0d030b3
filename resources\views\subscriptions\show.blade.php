@extends('layouts.app')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2">Detail <PERSON></h1>
                <p class="text-gray-600 dark:text-gray-400">Informasi tentang langganan Anda saat ini</p>
            </div>
            <div>
                <a href="{{ route('subscriptions.index') }}"
                    class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                    <i class="fas fa-arrow-left mr-1"></i> Kembali
                </a>
            </div>
        </div>

        @if (session('error'))
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400"
                role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        @if (session('success'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded dark:bg-green-900/30 dark:text-green-400"
                role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif

        <!-- Detail Langganan -->
        <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-blue-600 dark:bg-blue-700 p-4">
                <h2 class="text-white text-lg font-semibold">Informasi Langganan</h2>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-4">Detail Paket</h3>

                        <div class="space-y-3">
                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Nama Paket:</span>
                                <span
                                    class="font-medium text-light-text dark:text-dark-text ml-2">{{ $subscription->plan->name }}</span>
                            </div>

                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Harga:</span>
                                <span
                                    class="font-medium text-light-text dark:text-dark-text ml-2">{{ $subscription->plan->formatted_price }}</span>
                            </div>

                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Durasi:</span>
                                <span
                                    class="font-medium text-light-text dark:text-dark-text ml-2">{{ $subscription->plan->duration_in_months }}
                                    bulan</span>
                            </div>

                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Status:</span>
                                @if ($subscription->isActive())
                                    <span
                                        class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900/30 dark:text-green-400 ml-2">Aktif</span>
                                @elseif($subscription->isOnTrial())
                                    <span
                                        class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900/30 dark:text-blue-400 ml-2">Masa
                                        Uji Coba</span>
                                @elseif($subscription->isCancelled())
                                    <span
                                        class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900/30 dark:text-yellow-400 ml-2">Dibatalkan</span>
                                @elseif($subscription->isExpired())
                                    <span
                                        class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-red-900/30 dark:text-red-400 ml-2">Kadaluarsa</span>
                                @else
                                    <span
                                        class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-gray-300 ml-2">{{ $subscription->status }}</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-4">Periode Langganan</h3>

                        <div class="space-y-3">
                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Tanggal Mulai:</span>
                                <span
                                    class="font-medium text-light-text dark:text-dark-text ml-2">{{ $subscription->start_date->format('d F Y') }}</span>
                            </div>

                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Tanggal Berakhir:</span>
                                <span
                                    class="font-medium text-light-text dark:text-dark-text ml-2">{{ $subscription->end_date->format('d F Y') }}</span>
                            </div>

                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Sisa Waktu:</span>
                                <span id="remaining-time"
                                    class="font-medium text-light-text dark:text-dark-text ml-2">{{ $subscription->daysRemaining(true) }}</span>
                            </div>

                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Perpanjangan Otomatis:</span>
                                @if ($subscription->auto_renew)
                                    <span
                                        class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900/30 dark:text-green-400 ml-2">Aktif</span>
                                @else
                                    <span
                                        class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-gray-300 ml-2">Tidak
                                        Aktif</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-8">
                    <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-4">Fitur Paket</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-project-diagram text-blue-500 dark:text-blue-400 mr-2"></i>
                                <span class="font-medium text-light-text dark:text-dark-text">Jumlah Proyek</span>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400">Maksimal {{ $subscription->plan->project_limit }}
                                proyek</p>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-users text-blue-500 dark:text-blue-400 mr-2"></i>
                                <span class="font-medium text-light-text dark:text-dark-text">Jumlah Pengguna</span>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400">Maksimal {{ $subscription->plan->max_users }}
                                pengguna</p>
                        </div>

                        @if ($subscription->plan->features)
                            @foreach ($subscription->plan->features as $feature)
                                <div class="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-check-circle text-blue-500 dark:text-blue-400 mr-2"></i>
                                        <span
                                            class="font-medium text-light-text dark:text-dark-text">{{ $feature }}</span>
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    </div>
                </div>

                <div class="mt-8 flex flex-wrap gap-4">
                    <a href="{{ route('subscriptions.history') }}"
                        class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                        <i class="fas fa-history mr-1"></i> Riwayat Langganan
                    </a>

                    <a href="{{ route('payments.index') }}"
                        class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                        <i class="fas fa-credit-card mr-1"></i> Riwayat Pembayaran
                    </a>
                </div>
            </div>
        </div>
    </div>



@endsection

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Ambil elemen yang menampilkan sisa waktu
            const remainingTimeElement = document.getElementById('remaining-time');

            // Ambil tanggal berakhir langganan dari data attribute
            const endDateStr = "{{ $subscription->end_date->toIso8601String() }}";
            const endDate = new Date(endDateStr);

            // Fungsi untuk memperbarui sisa waktu
            function updateRemainingTime() {
                const now = new Date();

                // Hitung selisih waktu dalam milidetik
                let diff = endDate - now;

                // Jika waktu sudah habis
                if (diff <= 0) {
                    remainingTimeElement.textContent = '0 hari 0 jam 0 menit 0 detik';
                    return;
                }

                // Hitung hari, jam, menit, detik
                const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                diff -= days * (1000 * 60 * 60 * 24);

                const hours = Math.floor(diff / (1000 * 60 * 60));
                diff -= hours * (1000 * 60 * 60);

                const minutes = Math.floor(diff / (1000 * 60));
                diff -= minutes * (1000 * 60);

                const seconds = Math.floor(diff / 1000);

                // Perbarui teks
                remainingTimeElement.textContent = `${days} hari ${hours} jam ${minutes} menit ${seconds} detik`;
            }

            // Perbarui setiap detik
            updateRemainingTime();
            setInterval(updateRemainingTime, 1000);
        });
    </script>
@endsection
