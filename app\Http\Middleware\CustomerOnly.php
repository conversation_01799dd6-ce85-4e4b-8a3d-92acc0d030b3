<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CustomerOnly
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Log incoming request for debugging
        Log::info('CustomerOnly middleware', [
            'path' => $request->path(),
            'user_role' => Auth::user() ? Auth::user()->role : 'guest',
            'is_ajax' => $request->ajax() || $request->wantsJson()
        ]);

        // Check if user is authenticated and is a customer
        if (Auth::check() && Auth::user()->role === 'customer') {
            return $next($request);
        }

        // Handle AJAX requests with proper JSON response
        if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only customers can access this resource.'
            ], 403);
        }

        // For regular requests, redirect with error message
        if (Auth::check()) {
            if (Auth::user()->role === 'admin') {
                return redirect()->route('admin.dashboard')->with('error', 'Unauthorized. Only customers can access this resource.');
            }
            return redirect()->route('proyek')->with('error', 'Unauthorized. Only customers can access this resource.');
        }
        return redirect()->route('login')->with('error', 'You need to login first.');
    }
}
