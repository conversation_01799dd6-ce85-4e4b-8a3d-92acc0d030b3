@extends('layouts.app')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2">Pengaturan Sistem</h1>
                <p class="text-gray-600 dark:text-gray-400">Kelola pengaturan sistem aplikasi</p>
            </div>
        </div>

        @if (session('success'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded dark:bg-green-900 dark:text-green-200"
                role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif

        <div
            class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 transform hover:shadow-lg dark:bg-dark-card p-6 mb-6">
            <h2 class="text-xl font-semibold text-light-text dark:text-dark-text mb-4">Pengaturan Pembayaran</h2>

            <form action="{{ route('admin.settings.update') }}" method="POST">
                @csrf

                <div class="mb-6">
                    <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2"
                        for="payment_verification_mode">
                        Mode Verifikasi Pembayaran
                    </label>

                    <div class="mt-2 space-y-4">
                        <div class="flex items-center">
                            <input type="radio" id="manual" name="payment_verification_mode" value="manual"
                                {{ $paymentVerificationMode === 'manual' ? 'checked' : '' }}
                                class="h-4 w-4 text-light-accent dark:text-dark-accent focus:ring-light-accent dark:focus:ring-dark-accent border-gray-300 dark:border-gray-600 rounded transition-colors duration-150">
                            <label for="manual" class="ml-2 block text-gray-700 dark:text-gray-300">
                                Verifikasi Manual
                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                    Admin harus memverifikasi setiap pembayaran secara manual. Fitur verifikasi otomatis
                                    akan dinonaktifkan.
                                </p>
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="radio" id="automatic" name="payment_verification_mode" value="automatic"
                                {{ $paymentVerificationMode === 'automatic' ? 'checked' : '' }}
                                class="h-4 w-4 text-light-accent dark:text-dark-accent focus:ring-light-accent dark:focus:ring-dark-accent border-gray-300 dark:border-gray-600 rounded transition-colors duration-150">
                            <label for="automatic" class="ml-2 block text-gray-700 dark:text-gray-300">
                                Verifikasi Otomatis
                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                    Pembayaran akan diverifikasi secara otomatis melalui callback dari payment gateway.
                                    Fitur verifikasi manual akan dinonaktifkan.
                                </p>
                            </label>
                        </div>
                    </div>

                    @error('payment_verification_mode')
                        <p class="text-red-500 text-xs italic mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-6" id="payment_gateway_settings"
                    style="{{ $paymentVerificationMode === 'automatic' ? '' : 'display: none;' }}">
                    <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-4 border-b pb-2">Pengaturan
                        Payment Gateway</h3>

                    <div class="mb-4">
                        <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="payment_gateway">
                            Payment Gateway
                        </label>
                        <select id="payment_gateway" name="payment_gateway"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-dark-bg dark:text-dark-text transition-colors duration-150">
                            <option value="xendit" {{ $paymentGateway === 'xendit' ? 'selected' : '' }}>Xendit</option>
                        </select>
                        @error('payment_gateway')
                            <p class="text-red-500 text-xs italic mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Xendit Settings -->
                    <div id="xendit_settings" class="gateway-settings"
                        style="{{ $paymentGateway === 'xendit' ? '' : 'display: none;' }}">
                        <div class="mb-4">
                            <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2"
                                for="xendit_secret_key">
                                Xendit Secret Key
                            </label>
                            <input type="text" id="xendit_secret_key" name="xendit_secret_key"
                                value="{{ $xenditSecretKey }}"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-dark-bg dark:text-dark-text transition-colors duration-150"
                                placeholder="Masukkan Xendit Secret Key">
                            @error('xendit_secret_key')
                                <p class="text-red-500 text-xs italic mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2"
                                for="xendit_public_key">
                                Xendit Public Key
                            </label>
                            <input type="text" id="xendit_public_key" name="xendit_public_key"
                                value="{{ $xenditPublicKey }}"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-dark-bg dark:text-dark-text transition-colors duration-150"
                                placeholder="Masukkan Xendit Public Key">
                            @error('xendit_public_key')
                                <p class="text-red-500 text-xs italic mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2"
                                for="xendit_callback_token">
                                Xendit Callback Token
                            </label>
                            <input type="text" id="xendit_callback_token" name="xendit_callback_token"
                                value="{{ $xenditCallbackToken }}"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-dark-bg dark:text-dark-text transition-colors duration-150"
                                placeholder="Masukkan Xendit Callback Token">
                            @error('xendit_callback_token')
                                <p class="text-red-500 text-xs italic mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="xendit_production" name="xendit_production" value="1"
                                    {{ $xenditProduction ? 'checked' : '' }}
                                    class="h-4 w-4 text-light-accent dark:text-dark-accent focus:ring-light-accent dark:focus:ring-dark-accent border-gray-300 dark:border-gray-600 rounded transition-colors duration-150">
                                <label for="xendit_production" class="ml-2 block text-gray-700 dark:text-gray-300">
                                    Mode Produksi
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        Aktifkan mode produksi untuk transaksi nyata. Jika tidak dicentang, akan menggunakan
                                        mode sandbox.
                                    </p>
                                </label>
                            </div>
                        </div>

                        <div class="mb-4" id="xendit-settings"
                            style="{{ $paymentGateway === 'xendit' ? '' : 'display: none;' }}">
                            <div
                                class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4 dark:bg-yellow-900/30 dark:border-yellow-700">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-400 dark:text-yellow-600"
                                            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                            aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-yellow-700 dark:text-yellow-400">
                                            <strong>URL Callback Webhook Xendit:</strong> Pastikan URL callback di dashboard
                                            Xendit diatur ke:<br>
                                            <code id="payment_callback_url"
                                                class="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded">{{ url('/xendit-webhook') }}</code>
                                            <button onclick="copyToClipboard('payment_callback_url')"
                                                class="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                                <i class="fas fa-copy"></i> Salin
                                            </button>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2"
                            for="payment_callback_url">
                            Callback URL
                        </label>
                        <div class="flex">
                            <input type="text" id="payment_callback_url" value="{{ url('/payments/callback') }}"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-dark-bg dark:text-dark-text transition-colors duration-150"
                                readonly>
                            <button type="button" onclick="copyToClipboard('payment_callback_url')"
                                class="ml-2 bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            Gunakan URL ini sebagai callback URL di dashboard payment gateway Anda.
                        </p>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit"
                        class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                        <i class="fas fa-save mr-1"></i> Simpan Pengaturan
                    </button>
                </div>
            </form>
        </div>

        <script>
            // Toggle payment gateway settings based on verification mode
            document.querySelectorAll('input[name="payment_verification_mode"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    const gatewaySettings = document.getElementById('payment_gateway_settings');
                    if (this.value === 'automatic') {
                        gatewaySettings.style.display = '';
                    } else {
                        gatewaySettings.style.display = 'none';
                    }
                });
            });

            // Toggle gateway settings based on selected gateway
            document.getElementById('payment_gateway').addEventListener('change', function() {
                const selectedGateway = this.value;

                // Hide all gateway settings
                document.querySelectorAll('.gateway-settings').forEach(el => {
                    el.style.display = 'none';
                });

                // Show selected gateway settings
                if (selectedGateway === 'xendit') {
                    document.getElementById('xendit_settings').style.display = '';
                }
            });

            // Show appropriate settings based on current selection
            const currentGateway = document.getElementById('payment_gateway').value;
            if (currentGateway === 'xendit') {
                document.getElementById('xendit_settings').style.display = '';
            }

            // Copy to clipboard function
            function copyToClipboard(elementId) {
                const text = document.getElementById(elementId).textContent;
                navigator.clipboard.writeText(text).then(function() {
                    // Tampilkan notifikasi sukses
                    alert('URL berhasil disalin ke clipboard!');
                }, function(err) {
                    console.error('Gagal menyalin: ', err);
                });
            }
        </script>
    </div>
@endsection
