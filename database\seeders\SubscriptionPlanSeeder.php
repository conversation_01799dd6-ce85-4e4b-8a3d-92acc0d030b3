<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SubscriptionPlan;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Paket Basic
        SubscriptionPlan::create([
            'name' => 'Basic',
            'slug' => 'basic',
            'description' => 'Paket dasar untuk kebutuhan proyek kecil',
            'price' => 99000,
            'duration_days' => 30,
            'features' => [
                'Akses ke semua fitur dasar',
                'Maksimal 1 proyek',
                'Maksimal 1 pengguna',
                'Dukungan email'
            ],
            'is_active' => true,
            'is_featured' => false,
            'project_limit' => 1,
            'max_users' => 1
        ]);

        // Paket Pro
        SubscriptionPlan::create([
            'name' => 'Pro',
            'slug' => 'pro',
            'description' => 'Paket profesional untuk kebutuhan proyek menengah',
            'price' => 299000,
            'duration_days' => 30,
            'features' => [
                'Semua fitur Basic',
                'Maksimal 5 proyek',
                'Maksimal 3 pengguna',
                'Dukungan prioritas',
                'Export ke Excel dan PDF'
            ],
            'is_active' => true,
            'is_featured' => true,
            'project_limit' => 5,
            'max_users' => 3
        ]);

        // Paket Enterprise
        SubscriptionPlan::create([
            'name' => 'Enterprise',
            'slug' => 'enterprise',
            'description' => 'Paket lengkap untuk kebutuhan proyek besar',
            'price' => 599000,
            'duration_days' => 30,
            'features' => [
                'Semua fitur Pro',
                'Proyek tidak terbatas',
                'Maksimal 10 pengguna',
                'Dukungan 24/7',
                'Export ke Excel dan PDF',
                'Akses ke fitur premium',
                'Pelatihan penggunaan aplikasi'
            ],
            'is_active' => true,
            'is_featured' => false,
            'project_limit' => 999,
            'max_users' => 10
        ]);

        // Paket Tahunan
        SubscriptionPlan::create([
            'name' => 'Tahunan',
            'slug' => 'yearly',
            'description' => 'Paket lengkap untuk kebutuhan proyek besar selama 1 tahun',
            'price' => 5990000,
            'duration_days' => 365,
            'features' => [
                'Semua fitur Enterprise',
                'Proyek tidak terbatas',
                'Pengguna tidak terbatas',
                'Dukungan 24/7 prioritas',
                'Export ke Excel dan PDF',
                'Akses ke fitur premium',
                'Pelatihan penggunaan aplikasi',
                'Konsultasi proyek'
            ],
            'is_active' => true,
            'is_featured' => false,
            'project_limit' => 999,
            'max_users' => 999
        ]);
    }
}
