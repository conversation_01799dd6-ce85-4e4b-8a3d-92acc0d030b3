// volumeTemplates.js
export function getHeaderHtml(satuan) {
    if (satuan === "m3") {
        return `
      <th class="px-4 py-2 border">No.</th>
      <th class="px-4 py-2 border">Keterangan</th>
      <th class="px-4 py-2 border">Ju<PERSON><PERSON></th>
      <th class="px-4 py-2 border">Panjang</th>
      <th class="px-4 py-2 border">Lebar</th>
      <th class="px-4 py-2 border">Tinggi</th>
      <th class="px-4 py-2 border">Volume</th>
      <th class="px-4 py-2 border">Hasil</th>
      <th class="px-4 py-2 border">Aksi</th>
    `;
    } else if (satuan === "m2") {
        return `
      <th class="px-4 py-2 border">No.</th>
      <th class="px-4 py-2 border">Keterangan</th>
      <th class="px-4 py-2 border"><PERSON><PERSON><PERSON></th>
      <th class="px-4 py-2 border">Panjang</th>
      <th class="px-4 py-2 border">Lebar</th>
      <th class="px-4 py-2 border">Luas</th>
      <th class="px-4 py-2 border">Hasil</th>
      <th class="px-4 py-2 border">Aksi</th>
    `;
    } else if (satuan === "m") {
        return `
      <th class="px-4 py-2 border">No.</th>
      <th class="px-4 py-2 border">Keterangan</th>
      <th class="px-4 py-2 border">Jumlah</th>
      <th class="px-4 py-2 border">Panjang</th>
      <th class="px-4 py-2 border">Hasil</th>
      <th class="px-4 py-2 border">Aksi</th>
    `;
    } else if (satuan === "kg") {
        return `
      <th class="px-4 py-2 border">No.</th>
      <th class="px-4 py-2 border">Keterangan</th>
      <th class="px-4 py-2 border">Jumlah</th>
      <th class="px-4 py-2 border">Panjang</th>
      <th class="px-4 py-2 border">Berat Jenis</th>
      <th class="px-4 py-2 border">Hasil</th>
      <th class="px-4 py-2 border">Aksi</th>
    `;
    } else if (satuan === "ltr") {
        return `
      <th class="px-4 py-2 border">No.</th>
      <th class="px-4 py-2 border">Keterangan</th>
      <th class="px-4 py-2 border">Jumlah</th>
      <th class="px-4 py-2 border">Liter</th>
      <th class="px-4 py-2 border">Hasil</th>
      <th class="px-4 py-2 border">Aksi</th>
    `;
    } else if (satuan === "bh" || satuan === "ls" || satuan === "unit") {
        return `
      <th class="px-4 py-2 border">No.</th>
      <th class="px-4 py-2 border">Keterangan</th>
      <th class="px-4 py-2 border">Jumlah</th>
      <th class="px-4 py-2 border">Hasil</th>
      <th class="px-4 py-2 border">Aksi</th>
    `;
    }
}

export function getFormHtml(satuan) {
    const keteranganWidth = {
        m3: "w-1/3",
        m2: "w-1/2",
        kg: "w-1/2",
        m: "w-1/2",
        ltr: "w-1/2",
        unit: "w-3/4",
        bh: "w-3/4",
        ls: "w-3/4",
    };

    const commonStyles = `
      <table class="table-auto w-full border-collapse border border-gray-300 dark:border-gray-700 text-sm">
        <thead>
          <tr>
            <th class="px-3 py-2 border dark:border-gray-700 bg-blue-100 dark:bg-dark-bg-secondary text-sm ${
                keteranganWidth[satuan] || ""
            }">Keterangan</th>
            <th class="px-3 py-2 border dark:border-gray-700 bg-blue-100 dark:bg-dark-bg-secondary text-sm">Jumlah</th>
            ${
                satuan === "m3" ||
                satuan === "m2" ||
                satuan === "m" ||
                satuan === "kg"
                    ? '<th class="px-3 py-2 border dark:border-gray-700 bg-blue-100 dark:bg-dark-bg-secondary text-sm">Panjang</th>'
                    : ""
            }
            ${
                satuan === "m3" || satuan === "m2"
                    ? '<th class="px-3 py-2 border dark:border-gray-700 bg-blue-100 dark:bg-dark-bg-secondary text-sm">Lebar</th>'
                    : ""
            }
            ${
                satuan === "m3"
                    ? '<th class="px-3 py-2 border dark:border-gray-700 bg-blue-100 dark:bg-dark-bg-secondary text-sm">Tinggi</th>'
                    : ""
            }
            ${
                satuan === "m3"
                    ? '<th class="px-3 py-2 border dark:border-gray-700 bg-blue-100 dark:bg-dark-bg-secondary text-sm">Volume</th>'
                    : ""
            }
            ${
                satuan === "m2"
                    ? '<th class="px-3 py-2 border dark:border-gray-700 bg-blue-100 dark:bg-dark-bg-secondary text-sm">Luas</th>'
                    : ""
            }
            ${
                satuan === "kg"
                    ? '<th class="px-3 py-2 border dark:border-gray-700 bg-blue-100 dark:bg-dark-bg-secondary text-sm">Berat Jenis</th>'
                    : ""
            }
            ${
                satuan === "ltr"
                    ? '<th class="px-3 py-2 border dark:border-gray-700 bg-blue-100 dark:bg-dark-bg-secondary text-sm">Liter</th>'
                    : ""
            }
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="px-3 py-2 border dark:border-gray-700 text-sm ${
                keteranganWidth[satuan] || ""
            }"><input type="text" id="vc_keterangan" class="w-full border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded p-1 text-sm" required></td>
            <td class="px-3 py-2 border dark:border-gray-700 text-sm"><input type="number" id="vc_jumlah" class="w-full border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded p-1 text-sm" step="any"></td>
            ${
                satuan === "m3" ||
                satuan === "m2" ||
                satuan === "m" ||
                satuan === "kg"
                    ? '<td class="px-3 py-2 border dark:border-gray-700 text-sm"><input type="number" id="vc_panjang" class="w-full border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded p-1 text-sm" step="any"></td>'
                    : ""
            }
            ${
                satuan === "m3" || satuan === "m2"
                    ? '<td class="px-3 py-2 border dark:border-gray-700 text-sm"><input type="number" id="vc_lebar" class="w-full border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded p-1 text-sm" step="any"></td>'
                    : ""
            }
            ${
                satuan === "m3"
                    ? '<td class="px-3 py-2 border dark:border-gray-700 text-sm"><input type="number" id="vc_tinggi" class="w-full border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded p-1 text-sm" step="any"></td>'
                    : ""
            }
            ${
                satuan === "m3"
                    ? '<td class="px-3 py-2 border dark:border-gray-700 text-sm"><input type="number" id="vc_volume" class="w-full border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded p-1 text-sm" step="any" readonly></td>'
                    : ""
            }
            ${
                satuan === "m2"
                    ? '<td class="px-3 py-2 border dark:border-gray-700 text-sm"><input type="number" id="vc_luas" class="w-full border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded p-1 text-sm" step="any" readonly></td>'
                    : ""
            }
            ${
                satuan === "kg"
                    ? '<td class="px-3 py-2 border dark:border-gray-700 text-sm"><input type="number" id="vc_berat_jenis" class="w-full border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded p-1 text-sm" step="any"></td>'
                    : ""
            }
            ${
                satuan === "ltr"
                    ? '<td class="px-3 py-2 border dark:border-gray-700 text-sm"><input type="number" id="vc_liter" class="w-full border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded p-1 text-sm" step="any"></td>'
                    : ""
            }
          </tr>
        </tbody>
      </table>
      <div class="mt-4">
        <label class="block px-3 py-2 bg-blue-100 dark:bg-dark-accent/20 text-sm font-medium text-center rounded-t">Hasil</label>
        <input type="number" id="vc_hasil" class="w-full border border-t-0 rounded-b p-3 text-lg font-bold text-center dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600" step="any" min="0.001" required oninvalid="this.setCustomValidity('Hasil tidak boleh kosong atau 0')" oninput="this.setCustomValidity('')">
      </div>
    `;
    return commonStyles;
}
