@if ($paginator->hasPages())
    <nav role="navigation" aria-label="{{ __('Pagination Navigation') }}" class="pagination-container fixed-pagination" id="pagination-container">
        <div class="pagination-content">
            <div class="pagination-info">
                <p class="text-sm leading-5">
                    <span class="hidden sm:inline">{{ __('Menampilkan') }}</span>
                    @if ($paginator->firstItem())
                        <span class="font-medium">{{ $paginator->firstItem() }}</span>
                        <span class="hidden xs:inline">{{ __('sampai') }}</span>
                        <span class="font-medium">{{ $paginator->lastItem() }}</span>
                    @else
                        {{ $paginator->count() }}
                    @endif
                    <span class="hidden xs:inline">{{ __('dari') }}</span>
                    <span class="font-medium">{{ $paginator->total() }}</span>
                </p>
            </div>

            <div class="pagination-links">
                {{-- Previous Page Link --}}
                @if ($paginator->onFirstPage())
                    <span aria-disabled="true" aria-label="{{ __('pagination.previous') }}" class="pagination-arrow-disabled">
                        <i class="fas fa-chevron-left"></i>
                    </span>
                @else
                    <a href="{{ $paginator->previousPageUrl() }}" rel="prev" class="pagination-arrow" aria-label="{{ __('pagination.previous') }}" data-page="{{ $paginator->currentPage() - 1 }}">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                @endif

                {{-- First Page Link (Always shown) --}}
                @if($paginator->currentPage() != 1)
                    <a href="{{ $paginator->url(1) }}" class="pagination-link" aria-label="{{ __('Go to page 1') }}" data-page="1">
                        1
                    </a>
                @else
                    <span aria-current="page" class="pagination-current">
                        1
                    </span>
                @endif

                {{-- Ellipsis for pages between first and current (if needed) --}}
                @if($paginator->currentPage() > 2)
                    <span aria-disabled="true" class="pagination-ellipsis">
                        ...
                    </span>
                @endif

                {{-- Current Page (only if not first or last page) --}}
                @if($paginator->currentPage() != 1 && $paginator->currentPage() != $paginator->lastPage())
                    <span aria-current="page" class="pagination-current">
                        {{ $paginator->currentPage() }}
                    </span>
                @endif

                {{-- Ellipsis for pages between current and last (if needed) --}}
                @if($paginator->currentPage() < $paginator->lastPage() - 1)
                    <span aria-disabled="true" class="pagination-ellipsis">
                        ...
                    </span>
                @endif

                {{-- Last Page Link (Always shown) --}}
                @if($paginator->lastPage() > 1)
                    @if($paginator->currentPage() != $paginator->lastPage())
                        <a href="{{ $paginator->url($paginator->lastPage()) }}" class="pagination-link" aria-label="{{ __('Go to page '.$paginator->lastPage()) }}" data-page="{{ $paginator->lastPage() }}">
                            {{ $paginator->lastPage() }}
                        </a>
                    @else
                        <span aria-current="page" class="pagination-current">
                            {{ $paginator->lastPage() }}
                        </span>
                    @endif
                @endif

                {{-- Next Page Link --}}
                @if ($paginator->hasMorePages())
                    <a href="{{ $paginator->nextPageUrl() }}" rel="next" class="pagination-arrow" aria-label="{{ __('pagination.next') }}" data-page="{{ $paginator->currentPage() + 1 }}">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                @else
                    <span aria-disabled="true" aria-label="{{ __('pagination.next') }}" class="pagination-arrow-disabled">
                        <i class="fas fa-chevron-right"></i>
                    </span>
                @endif
            </div>
        </div>
    </nav>
@endif
