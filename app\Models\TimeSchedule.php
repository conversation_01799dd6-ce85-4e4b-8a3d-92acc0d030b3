<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TimeSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'item_pekerjaan_id',
        'nama_kegiatan',
        'tanggal_mulai',
        'tanggal_selesai',
        'bobot',
        'progress',
        'durasi',
        'distribusi_bobot'
    ];

    protected $casts = [
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
        'bobot' => 'decimal:2',
        'progress' => 'decimal:2',
        'durasi' => 'integer',
        'distribusi_bobot' => 'array'
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function itemPekerjaan()
    {
        return $this->belongsTo(ItemPekerjaan::class, 'item_pekerjaan_id');
    }
}
