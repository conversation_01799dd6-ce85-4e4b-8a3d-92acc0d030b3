@extends('layouts.app')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2">Detail Pembayaran</h1>
                <p class="text-gray-600 dark:text-gray-400">Informasi tentang pembayaran Anda</p>
            </div>
            <div>
                <a href="{{ route('payments.index') }}"
                    class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                    <i class="fas fa-arrow-left mr-1"></i> Kembali
                </a>
            </div>
        </div>

        @if (session('error'))
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400"
                role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        @if (session('success'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded dark:bg-green-900/30 dark:text-green-400"
                role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif

        <!-- Detail Pembayaran -->
        <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-blue-600 dark:bg-blue-700 p-4">
                <h2 class="text-white text-lg font-semibold">Detail Pembayaran</h2>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-4">Informasi Pembayaran</h3>

                        <div class="space-y-3">
                            <div>
                                <span class="text-gray-600 dark:text-gray-400">No. Invoice:</span>
                                <span
                                    class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->invoice_number }}</span>
                            </div>

                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Tanggal:</span>
                                <span
                                    class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->created_at->format('d F Y H:i') }}</span>
                            </div>

                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Jumlah:</span>
                                <span
                                    class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->formatted_amount }}</span>
                            </div>

                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Metode Pembayaran:</span>
                                <span class="font-medium text-light-text dark:text-dark-text ml-2">
                                    @if ($payment->payment_method === 'bank_transfer')
                                        Transfer Bank
                                    @elseif($payment->payment_method === 'credit_card')
                                        Kartu Kredit
                                    @elseif($payment->payment_method === 'e-wallet')
                                        E-Wallet
                                    @else
                                        {{ $payment->payment_method }}
                                    @endif
                                </span>
                            </div>

                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Status:</span>
                                @if ($payment->isCompleted())
                                    <span
                                        class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900/30 dark:text-green-400 ml-2">Selesai</span>
                                @elseif($payment->isPending())
                                    @php
                                        $paymentVerificationMode = \App\Models\SystemSetting::getValue(
                                            'payment_verification_mode',
                                            'manual',
                                        );
                                    @endphp

                                    @if ($payment->payment_method === 'xendit' || $payment->payment_method === 'midtrans')
                                        <div class="inline-block ml-2">
                                            <span
                                                class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900/30 dark:text-green-400">Pembayaran
                                                Berhasil</span>

                                            @if ($paymentVerificationMode === 'manual')
                                                <div class="mt-1">
                                                    <span
                                                        class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900/30 dark:text-yellow-400">Menunggu
                                                        Verifikasi Admin</span>
                                                </div>
                                                <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                                                    <i class="fas fa-info-circle mr-1"></i> Pembayaran Anda telah diterima
                                                    dan
                                                    sedang menunggu verifikasi dari admin.
                                                </div>
                                            @else
                                                <div class="mt-1">
                                                    <span
                                                        class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900/30 dark:text-blue-400">Menunggu
                                                        Konfirmasi Sistem</span>
                                                </div>
                                                <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                                                    <i class="fas fa-info-circle mr-1"></i> Pembayaran Anda telah diterima
                                                    dan
                                                    akan diverifikasi secara otomatis oleh sistem.
                                                </div>
                                            @endif
                                        </div>
                                    @else
                                        <span
                                            class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900/30 dark:text-yellow-400 ml-2">Menunggu
                                            Pembayaran</span>
                                    @endif
                                @elseif($payment->hasFailed())
                                    <span
                                        class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-red-900/30 dark:text-red-400 ml-2">Gagal</span>
                                @elseif($payment->wasRefunded())
                                    <span
                                        class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-purple-900/30 dark:text-purple-400 ml-2">Dikembalikan</span>
                                @elseif($payment->status === 'processing')
                                    <span
                                        class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900/30 dark:text-blue-400 ml-2">Diproses</span>
                                @else
                                    <span
                                        class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-gray-300 ml-2">{{ $payment->status }}</span>
                                @endif
                            </div>

                            @if ($payment->paid_at)
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Tanggal Pembayaran:</span>
                                    <span
                                        class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->paid_at->format('d F Y H:i') }}</span>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-4">Detail Langganan</h3>

                        @if ($payment->subscription && $payment->subscription->plan)
                            <div class="space-y-3">
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Paket:</span>
                                    <span
                                        class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->subscription->plan->name }}</span>
                                </div>

                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Durasi:</span>
                                    <span
                                        class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->subscription->plan->duration_in_months }}
                                        bulan</span>
                                </div>

                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Tanggal Mulai:</span>
                                    <span
                                        class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->subscription->start_date->format('d F Y') }}</span>
                                </div>

                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Tanggal Berakhir:</span>
                                    <span
                                        class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->subscription->end_date->format('d F Y') }}</span>
                                </div>

                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Status Langganan:</span>
                                    @if ($payment->subscription->isActive())
                                        <span
                                            class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900/30 dark:text-green-400 ml-2">Aktif</span>
                                    @elseif($payment->subscription->isOnTrial())
                                        <span
                                            class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900/30 dark:text-blue-400 ml-2">Masa
                                            Uji Coba</span>
                                    @elseif($payment->subscription->isCancelled())
                                        <span
                                            class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900/30 dark:text-yellow-400 ml-2">Dibatalkan</span>
                                    @elseif($payment->subscription->isExpired())
                                        <span
                                            class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-red-900/30 dark:text-red-400 ml-2">Kadaluarsa</span>
                                    @else
                                        <span
                                            class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-gray-300 ml-2">{{ $payment->subscription->status }}</span>
                                    @endif
                                </div>
                            </div>
                        @else
                            <p class="text-gray-600 dark:text-gray-400">Tidak ada informasi langganan.</p>
                        @endif
                    </div>
                </div>

                @if ($payment->description)
                    <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-2">Deskripsi</h3>
                        <p class="text-gray-600 dark:text-gray-400">{{ $payment->description }}</p>
                    </div>
                @endif

                @if ($payment->payment_proof)
                    <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-2">Bukti Pembayaran</h3>
                        <div class="mt-2">
                            <img src="{{ asset('storage/' . $payment->payment_proof) }}" alt="Bukti Pembayaran"
                                class="max-w-md rounded-lg shadow-md">
                        </div>
                    </div>
                @endif

                <div class="mt-8 flex flex-wrap gap-4">
                    @php
                        $paymentVerificationMode = \App\Models\SystemSetting::getValue(
                            'payment_verification_mode',
                            'manual',
                        );
                    @endphp
                    
                    @if ($payment->isPending() && $payment->payment_method === 'bank_transfer' && !$payment->payment_proof)
                        <button type="button" 
                            class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150"
                            onclick="openUploadModal()">
                            <i class="fas fa-upload mr-1"></i> Upload Bukti Pembayaran
                        </button>
                    @endif

                    @if ($payment->status === 'processing' && $paymentVerificationMode === 'manual' && !$payment->payment_proof)
                        <button type="button"
                            class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150"
                            onclick="openUploadModal()">
                            <i class="fas fa-upload mr-1"></i> Upload Bukti Pembayaran
                        </button>
                    @endif

                    @if ($payment->isPending())
                        <a href="{{ route('payments.pending.resume', $payment->id) }}"
                            class="bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                            <i class="fas fa-redo mr-1"></i> Buka Kembali Checkout
                        </a>
                    @endif

                    @if ($payment->isCompleted() && $payment->subscription && $payment->subscription->isActive())
                        <a href="{{ route('subscriptions.show') }}"
                            class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                            <i class="fas fa-check-circle mr-1"></i> Lihat Langganan Aktif
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Upload Bukti Pembayaran -->
    <div id="uploadModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden overflow-y-auto py-8 text-sm">
        <div class="bg-white dark:bg-dark-card p-0 rounded-lg shadow-lg max-w-md w-full mx-auto my-auto border-2 border-light-accent dark:border-dark-accent">
            <!-- Header -->
            <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10 rounded-t-lg">
                <div class="flex justify-between items-center">
                    <h2 class="text-white dark:text-dark-text text-lg font-semibold">Upload Bukti Pembayaran</h2>
                    <button type="button" onclick="closeUploadModal()" 
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                        <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Content -->
            <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
                @if($errors->any())
                <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400" role="alert">
                    <ul class="list-disc pl-4">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif
                
                <form action="{{ route('payments.upload', $payment->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="mb-6">
                        <label for="payment_proof" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bukti Pembayaran</label>
                        
                        <div class="mt-2 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-700 border-dashed rounded-lg">
                            <div class="space-y-1 text-center">
                                <div id="preview-container" class="hidden mb-3">
                                    <img id="preview-image" src="#" alt="Preview" class="mx-auto h-32 object-cover rounded">
                                </div>
                                
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                
                                <div class="flex text-sm text-gray-600 dark:text-gray-400">
                                    <label for="payment_proof" class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 focus-within:outline-none">
                                        <span>Upload file</span>
                                        <input id="payment_proof" name="payment_proof" type="file" class="sr-only" accept="image/*" onchange="previewImage(this)">
                                    </label>
                                    <p class="pl-1">atau drag and drop</p>
                                </div>
                                
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    PNG, JPG, JPEG hingga 2MB
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4 flex justify-end space-x-3">
                        <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150" onclick="closeUploadModal()">
                            Batal
                        </button>
                        <button type="submit" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                            <i class="fas fa-upload mr-1"></i> Upload
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    function openUploadModal() {
        document.getElementById('uploadModal').classList.remove('hidden');
    }
    
    function closeUploadModal() {
        document.getElementById('uploadModal').classList.add('hidden');
        document.getElementById('preview-container').classList.add('hidden');
        document.getElementById('payment_proof').value = '';
    }
    
    function previewImage(input) {
        const previewContainer = document.getElementById('preview-container');
        const previewImage = document.getElementById('preview-image');
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                previewContainer.classList.remove('hidden');
            }
            
            reader.readAsDataURL(input.files[0]);
        }
    }
</script>
@endsection
