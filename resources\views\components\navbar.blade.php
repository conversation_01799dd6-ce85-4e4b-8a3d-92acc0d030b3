<nav
    class="fixed top-0 z-50 w-full bg-light-navbar dark:bg-dark-navbar border-r border-blue-400/20 dark:border-dark-accent/20 shadow-xl sm:translate-x-0 transition-colors duration-200">
    <div class="px-4 py-3 lg:px-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
                <!-- Sidebar Toggle Button untuk halaman admin/customer (hanya di halaman dashboard) -->
                @auth
                    @if (!Request::routeIs('visitor.*'))
                        <button data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar"
                            aria-controls="logo-sidebar" type="button"
                            class="p-2 text-white rounded-lg transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 sm:hidden">
                            <span class="sr-only">Open sidebar</span>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="1.5"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                            </svg>
                        </button>
                    @endif
                @endauth

                <!-- Mobile menu toggle untuk halaman visitor -->
                @if (Request::routeIs('visitor.*') || !Auth::check())
                    <button data-collapse-toggle="navbar-mobile" type="button"
                        class="p-2 text-white rounded-lg transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 sm:hidden"
                        aria-controls="navbar-mobile" aria-expanded="false">
                        <span class="sr-only">Open main menu</span>
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16m-7 6h7" />
                        </svg>
                    </button>
                @endif

                <!-- Logo -->
                <a href="{{ Auth::check() && Auth::user()->role === 'admin' ? route('admin.dashboard') : (Auth::check() ? route('proyek') : route('visitor.welcome')) }}"
                    class="flex items-center gap-2">
                    <span
                        class="text-2xl font-bold bg-gradient-to-r from-yellow-500 to-yellow-500 bg-clip-text text-transparent">
                        ESTIMATEENG
                    </span>
                </a>
            </div>

            <!-- Page Title - Only for user pages -->
            @auth
                @if (!Request::routeIs('visitor.*'))
                    <div class="hidden md:block text-center flex-grow">
                        <h1 class="text-xl font-bold text-white">
                            @if (request()->routeIs('upah.index'))
                                Daftar Harga Satuan Upah
                            @elseif(request()->routeIs('bahan.index'))
                                Daftar Harga Satuan Bahan
                            @elseif(request()->routeIs('alat.index'))
                                Daftar Harga Satuan Alat
                            @elseif(request()->routeIs('ahs.index'))
                                Daftar Analisa Harga Satuan Pekerjaan
                            @elseif(request()->routeIs('rab.index'))
                                {{ $project->name ?? 'Rencana Anggaran Biaya' }}
                            @elseif(request()->routeIs('time-schedule.index'))
                                {{ $project->name ?? 'Time Schedule' }}
                            @elseif(request()->routeIs('proyek'))
                                Daftar Proyek
                            @elseif(request()->routeIs('admin.dashboard'))
                                Dashboard Admin
                            @endif
                        </h1>
                    </div>
                @endif
            @endauth

            <!-- Desktop Navigation untuk semua pengguna - diposisikan ke kanan -->
            <div class="hidden sm:flex items-center justify-end space-x-2 ml-auto mr-6">
                <!-- Menu untuk halaman visitor atau hanya menu Tutorial untuk halaman admin/customer -->
                @if (Request::routeIs('visitor.*') || !Auth::check())
                    <a href="{{ route('visitor.welcome') }}"
                        class="text-white/90 font-medium hover:text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 px-3 py-2 rounded-lg transition-all {{ Request::routeIs('visitor.welcome') ? 'text-white bg-white/20 dark:bg-dark-accent/30' : '' }}">
                        Home
                    </a>
                @endif

                <!-- Tutorial hanya ditampilkan di halaman visitor -->
                @if (Request::routeIs('visitor.*') || !Auth::check())
                    <a href="{{ route('visitor.tutorial') }}"
                        class="text-white/90 font-medium hover:text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 px-3 py-2 rounded-lg transition-all {{ Request::routeIs('visitor.tutorial') ? 'text-white bg-white/20 dark:bg-dark-accent/30' : '' }}">
                        Tutorial
                    </a>
                @endif

                @if (Request::routeIs('visitor.*') || !Auth::check())
                    <a href="{{ route('visitor.packages') }}"
                        class="text-white/90 font-medium hover:text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 px-3 py-2 rounded-lg transition-all {{ Request::routeIs('visitor.packages') ? 'text-white bg-white/20 dark:bg-dark-accent/30' : '' }}">
                        Paket
                    </a>

                    <a href="{{ route('visitor.koleksi') }}"
                        class="text-white/90 font-medium hover:text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 px-3 py-2 rounded-lg transition-all {{ Request::routeIs('visitor.koleksi*') ? 'text-white bg-white/20 dark:bg-dark-accent/30' : '' }}">
                        Koleksi
                    </a>

                    @auth
                        <!-- Proyek saya link untuk pengguna yang sudah login di halaman visitor -->
                        <a href="{{ route('proyek') }}"
                            class="text-white/90 font-medium hover:text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 px-3 py-2 rounded-lg transition-all {{ Request::routeIs('proyek') ? 'text-white bg-white/20 dark:bg-dark-accent/30' : '' }}">
                            Proyek Saya
                        </a>
                    @endauth
                @endif

                @guest
                    <!-- Login dan Register untuk tamu -->
                    <a href="{{ route('login') }}"
                        class="text-white/90 font-medium hover:text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 px-3 py-2 rounded-lg transition-all">
                        Login
                    </a>
                    <a href="{{ route('register') }}"
                        class="bg-white text-light-accent dark:bg-dark-accent dark:text-white px-4 py-2 rounded-lg font-medium hover:bg-light-hover dark:hover:bg-dark-accent/80 transition-colors">
                        Register
                    </a>
                @endguest
            </div>

            <!-- Theme Toggle Button untuk halaman visitor -->
            @if (Request::routeIs('visitor.*') || !Auth::check())
                <div class="flex items-center ml-4">
                    <x-theme-toggle :toggleId="'theme-toggle-visitor'" :toggleIconId="'theme-toggle-visitor-icon'" :label="''" />
                </div>
            @endif

            <!-- User menu untuk pengguna yang sudah login -->
            @auth
                <div class="flex items-center gap-4 ml-4">
                    <div class="relative group">
                        <button type="button"
                            class="flex items-center gap-2 p-1.5 rounded-full text-white transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20"
                            aria-expanded="false" data-dropdown-toggle="dropdown-user">
                            <div class="relative w-9 h-9 overflow-hidden rounded-full bg-white/10 dark:bg-dark-accent/20">
                                @if (Auth::user()->photo)
                                    <img src="{{ asset('storage/' . Auth::user()->photo) }}" alt="Profile Photo"
                                        class="w-full h-full object-cover navbar-profile-photo">
                                @else
                                    <img src="@gravatar(Auth::user()->email, 200, 'identicon')" alt="Profile Photo"
                                        class="w-full h-full object-cover navbar-profile-photo">
                                    <svg class="w-5 h-5 text-white absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 navbar-profile-icon hidden"
                                        fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                            clip-rule="evenodd" />
                                    </svg>
                                @endif
                            </div>
                            <!-- Username hidden as requested -->
                        </button>

                        <!-- Dropdown Menu -->
                        <div class="absolute right-0 hidden pt-2 w-48 group-hover:block" id="dropdown-user">
                            <div
                                class="bg-light-card dark:bg-dark-bg-secondary rounded-xl shadow-lg ring-1 ring-gray-100 dark:ring-dark-accent/10 py-2 border-2 border-light-accent dark:border-dark-accent">
                                <div class="px-4 py-3 border-b border-gray-100 dark:border-dark-accent/10">
                                    <p class="text-sm font-medium text-light-text dark:text-dark-text truncate">
                                        {{ Auth::user()->name }}</p>
                                    <p class="text-xs text-light-text/70 dark:text-dark-text/70 mt-1 truncate">
                                        {{ Auth::user()->email }}</p>
                                </div>
                                <ul class="py-1">
                                    <li>
                                        <button type="button"
                                            class="profile-button w-full px-4 py-2 text-sm text-light-text dark:text-dark-text hover:bg-light-hover dark:hover:bg-dark-accent/10 text-left transition-colors">
                                            <i class="fas fa-user mr-2"></i> Profil
                                        </button>
                                    </li>
                                    @if (Auth::user()->role === 'customer')
                                        <li>
                                            <a href="{{ route('subscriptions.index') }}"
                                                class="block w-full px-4 py-2 text-sm text-light-text dark:text-dark-text hover:bg-light-hover dark:hover:bg-dark-accent/10 text-left transition-colors">
                                                <i class="fas fa-crown mr-2"></i> Langganan
                                            </a>
                                        </li>
                                        <li>
                                            <a href="{{ route('payments.pending') }}"
                                                class="block w-full px-4 py-2 text-sm text-light-text dark:text-dark-text hover:bg-light-hover dark:hover:bg-dark-accent/10 text-left transition-colors">
                                                <i class="fas fa-shopping-cart mr-2"></i> Keranjang Checkout
                                            </a>
                                        </li>
                                    @endif
                                    <li>
                                        <form method="POST" action="{{ route('logout') }}">
                                            @csrf
                                            <button type="submit"
                                                class="w-full px-4 py-2 text-sm text-light-text dark:text-dark-text hover:bg-light-hover dark:hover:bg-dark-accent/10 text-left transition-colors">
                                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @endauth
        </div>
    </div>

    <!-- Mobile menu untuk semua pengguna -->
    <div class="hidden sm:hidden" id="navbar-mobile">
        <div class="px-4 pt-2 pb-4 space-y-2 bg-light-navbar dark:bg-dark-navbar">
            <!-- Menu untuk halaman visitor atau hanya menu Tutorial untuk halaman admin/customer -->
            @if (Request::routeIs('visitor.*') || !Auth::check())
                <a href="{{ route('visitor.welcome') }}"
                    class="block py-2 px-3 rounded-lg text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 transition-colors {{ Request::routeIs('visitor.welcome') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                    Home
                </a>
            @endif

            <!-- Tutorial hanya ditampilkan di halaman visitor -->
            @if (Request::routeIs('visitor.*') || !Auth::check())
                <a href="{{ route('visitor.tutorial') }}"
                    class="block py-2 px-3 rounded-lg text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 transition-colors {{ Request::routeIs('visitor.tutorial') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                    Tutorial
                </a>
            @endif

            @if (Request::routeIs('visitor.*') || !Auth::check())
                <a href="{{ route('visitor.packages') }}"
                    class="block py-2 px-3 rounded-lg text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 transition-colors {{ Request::routeIs('visitor.packages') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                    Paket
                </a>

                <a href="{{ route('visitor.koleksi') }}"
                    class="block py-2 px-3 rounded-lg text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 transition-colors {{ Request::routeIs('visitor.koleksi*') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                    Koleksi
                </a>

                @auth
                    <!-- Proyek saya di mobile menu di halaman visitor -->
                    <a href="{{ route('proyek') }}"
                        class="block py-2 px-3 rounded-lg text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 transition-colors {{ Request::routeIs('proyek') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                        Proyek Saya
                    </a>
                @endauth
            @endif

            @guest
                <div class="grid grid-cols-2 gap-2 mt-2">
                    <a href="{{ route('login') }}"
                        class="block py-2 px-3 text-center rounded-lg text-white hover:bg-white/10 dark:hover:bg-dark-accent/20 border border-white/30 dark:border-dark-accent/30 transition-colors">
                        Login
                    </a>
                    <a href="{{ route('register') }}"
                        class="block py-2 px-3 text-center rounded-lg bg-white dark:bg-dark-accent text-blue-600 dark:text-white font-medium hover:bg-gray-100 dark:hover:bg-dark-accent/80 transition-colors">
                        Register
                    </a>
                </div>
            @endguest

            <!-- Theme Toggle Button untuk mobile menu - hanya ditampilkan pada halaman visitor dan saat menu utama tidak terbuka -->
            @if (Request::routeIs('visitor.*') || !Auth::check())
                <div id="mobile-theme-toggle-container" class="flex items-center justify-center mt-4">
                    <x-theme-toggle :toggleId="'mobile-theme-toggle'" :toggleIconId="'mobile-theme-toggle-icon'" :label="'Mode'" />
                </div>
            @endif
        </div>
    </div>
</nav>

@auth
    @if (!Request::routeIs('visitor.*'))
        <aside id="logo-sidebar"
            class="fixed top-0 left-0 z-40 w-64 h-screen pt-20 transition-transform -translate-x-full bg-light-sidebar dark:bg-dark-sidebar border-r border-blue-400/20 dark:border-dark-accent/20 shadow-xl sm:translate-x-0"
            aria-label="Sidebar">
            <div class="h-full px-3 pb-4 overflow-y-auto flex flex-col">
                <ul class="space-y-2 font-medium flex-grow">
                    @if (Auth::user()->role === 'customer')
                        <li>
                            <a href="{{ route('proyek') }}"
                                class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('proyek') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                    <svg class="w-5 h-5 text-white" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                    </svg>
                                </div>
                                <span class="font-medium">Proyek</span>
                            </a>
                        </li>
                        @php
                            $showPreviewDownload = request()->has('showPreviewDownload');
                            $onRabPage = request()->is('rab') || request()->is('rab/*');
                            $onTimeSchedulePage = request()->is('time-schedule') || request()->is('time-schedule/*');
                            $isProjectPageContext = $onRabPage || $onTimeSchedulePage || $showPreviewDownload;
                        @endphp

                        @if ($isProjectPageContext)
                            <li>
                                <a href="{{ route('rab.index', ['project' => request()->route('project') ?? session('current_project_id')]) }}"
                                    class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ ($onRabPage && !$showPreviewDownload) ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                    <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                        <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                            viewBox="0 0 18 18">
                                            <path
                                                d="M6.143 0H1.857A1.857 1.857 0 0 0 0 1.857v4.286C0 7.169.831 8 1.857 8h4.286A1.857 1.857 0 0 0 8 6.143V1.857A1.857 1.857 0 0 0 6.143 0Zm10 0h-4.286A1.857 1.857 0 0 0 10 1.857v4.286C10 7.169 10.831 8 11.857 8h4.286A1.857 1.857 0 0 0 18 6.143V1.857A1.857 1.857 0 0 0 16.143 0Zm-10 10H1.857A1.857 1.857 0 0 0 0 11.857v4.286C0 17.169.831 18 1.857 18h4.286A1.857 1.857 0 0 0 8 16.143v-4.286A1.857 1.857 0 0 0 6.143 10Zm10 0h-4.286A1.857 1.857 0 0 0 10 11.857v4.286c0 1.026.831 1.857 1.857 1.857h4.286A1.857 1.857 0 0 0 18 16.143v-4.286A1.857 1.857 0 0 0 16.143 10Z" />
                                        </svg>
                                    </div>
                                    <span class="font-medium">Rab</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('time-schedule.index', ['project' => request()->route('project') ?? session('current_project_id')]) }}"
                                    class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ $onTimeSchedulePage ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                    <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                        <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                            viewBox="0 0 20 20">
                                            <path
                                                d="M0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm14-7.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm0 4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm-5-4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm0 4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm-5-4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm0 4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1ZM20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4Z" />
                                        </svg>
                                    </div>
                                    <span class="font-medium">Time Schedule</span>
                                </a>
                            </li>
                        @else
                            <li>
                                <a href="{{ route('ahs.index') }}"
                                    class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('ahs.index') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                    <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                        <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                            viewBox="0 0 20 20">
                                            <path
                                                d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.96 2.96 0 0 0 .13 5H5Z" />
                                            <path
                                                d="M6.737 11.061a2.961 2.961 0 0 1 .81-1.515l6.117-6.116A4.839 4.839 0 0 1 16 2.141V2a1.97 1.97 0 0 0-1.933-2H7v5a2 2 0 0 1-2 2H0v11a1.969 1.969 0 0 0 1.933 2h12.134A1.97 1.97 0 0 0 16 18v-3.093l-1.546 1.546c-.413.413-.94.695-1.513.81l-3.4.679a2.947 2.947 0 0 1-1.85-.227 2.96 2.96 0 0 1-1.635-3.257l.681-3.397Z" />
                                            <path
                                                d="M8.961 16a.93.93 0 0 0 .189-.019l3.4-.679a.961.961 0 0 0 .49-.263l6.118-6.117a2.884 2.884 0 0 0-4.079-4.078l-6.117 6.117a.96.96 0 0 0-.263.491l-.679 3.4A.961.961 0 0 0 8.961 16Zm7.477-9.8a.958.958 0 0 1 .68-.281.961.961 0 0 1 .682 1.644l-.315.315-1.36-1.36.313-.318Zm-5.911 5.911 4.236-4.236 1.359 1.359-4.236 4.237-1.7.339.341-1.699Z" />
                                        </svg>
                                    </div>
                                    <span class="font-medium">Daftar Ahsp</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('visitor.tutorial') }}"
                                    class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('visitor.tutorial') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                    <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                        <svg class="w-5 h-5 text-white" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                        </svg>
                                    </div>
                                    <span class="font-medium">Tutorial</span>
                                </a>
                            </li>
                        @endif
                    @endif

                    @if (Auth::user()->role === 'admin')
                        <li>
                            <a href="{{ route('admin.dashboard') }}"
                                class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('admin.dashboard') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                    <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                        viewBox="0 0 20 20">
                                        <path
                                            d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                                        </path>
                                    </svg>
                                </div>
                                <span class="font-medium">Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('customers.index') }}"
                                class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('customers.index') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                    <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                        viewBox="0 0 20 18">
                                        <path
                                            d="M14 2a3.963 3.963 0 0 0-1.4.267 6.439 6.439 0 0 1-1.331 6.638A4 4 0 1 0 14 2Zm1 9h-1.264A6.957 6.957 0 0 1 15 15v2a2.97 2.97 0 0 1-.184 1H19a1 1 0 0 0 1-1v-1a5.006 5.006 0 0 0-5-5ZM6.5 9a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9ZM8 10H5a5.006 5.006 0 0 0-5 5v2a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-2a5.006 5.006 0 0 0-5-5Z" />
                                    </svg>
                                </div>
                                <span class="font-medium">Customers</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('ahs.index') }}"
                                class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('ahs.index') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                    <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                        viewBox="0 0 20 20">
                                        <path d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.96 2.96 0 0 0 .13 5H5Z" />
                                        <path
                                            d="M6.737 11.061a2.961 2.961 0 0 1 .81-1.515l6.117-6.116A4.839 4.839 0 0 1 16 2.141V2a1.97 1.97 0 0 0-1.933-2H7v5a2 2 0 0 1-2 2H0v11a1.969 1.969 0 0 0 1.933 2h12.134A1.97 1.97 0 0 0 16 18v-3.093l-1.546 1.546c-.413.413-.94.695-1.513.81l-3.4.679a2.947 2.947 0 0 1-1.85-.227 2.96 2.96 0 0 1-1.635-3.257l.681-3.397Z" />
                                        <path
                                            d="M8.961 16a.93.93 0 0 0 .189-.019l3.4-.679a.961.961 0 0 0 .49-.263l6.118-6.117a2.884 2.884 0 0 0-4.079-4.078l-6.117 6.117a.96.96 0 0 0-.263.491l-.679 3.4A.961.961 0 0 0 8.961 16Zm7.477-9.8a.958.958 0 0 1 .68-.281.961.961 0 0 1 .682 1.644l-.315.315-1.36-1.36.313-.318Zm-5.911 5.911 4.236-4.236 1.359 1.359-4.236 4.237-1.7.339.341-1.699Z" />
                                    </svg>
                                </div>
                                <span class="font-medium">Ahsp</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('upah.index') }}"
                                class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('upah.index') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                    <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                        viewBox="0 0 20 18">
                                        <path
                                            d="M14 2a3.963 3.963 0 0 0-1.4.267 6.439 6.439 0 0 1-1.331 6.638A4 4 0 1 0 14 2Zm1 9h-1.264A6.957 6.957 0 0 1 15 15v2a2.97 2.97 0 0 1-.184 1H19a1 1 0 0 0 1-1v-1a5.006 5.006 0 0 0-5-5ZM6.5 9a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9ZM8 10H5a5.006 5.006 0 0 0-5 5v2a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-2a5.006 5.006 0 0 0-5-5Z" />
                                    </svg>
                                </div>
                                <span class="font-medium">Upah</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('bahan.index') }}"
                                class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('bahan.index') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                    <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                        viewBox="0 0 18 20">
                                        <path
                                            d="M17 5.923A1 1 0 0 0 16 5h-3V4a4 4 0 1 0-8 0v1H2a1 1 0 0 0-1 .923L.086 17.846A2 2 0 0 0 2.08 20h13.84a2 2 0 0 0 1.994-2.153L17 5.923ZM7 9a1 1 0 0 1-2 0V7h2v2Zm0-5a2 2 0 1 1 4 0v1H7V4Zm6 5a1 1 0 1 1-2 0V7h2v2Z" />
                                    </svg>
                                </div>
                                <span class="font-medium">Bahan</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('alat.index') }}"
                                class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('alat.index') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                    <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                        viewBox="0 0 20 20">
                                        <path
                                            d="M18 7.5h-.423l-.452-1.09.3-.3a1.5 1.5 0 0 0-2.121-2.121l-.3.3-1.089-.452V2A1.5 1.5 0 0 0 11 .5H9A1.5 1.5 0 0 0 7.5 2v.423l-1.09.452-.3-.3a1.5 1.5 0 0 0-2.121 2.121l.3.3L2.423 7.5H2A1.5 1.5 0 0 0 .5 9v2A1.5 1.5 0 0 0 2 12.5h.423l.452 1.09-.3.3a1.5 1.5 0 0 0 2.121 2.121l.3-.3 1.09.452V18A1.5 1.5 0 0 0 9 19.5h2a1.5 1.5 0 0 0 1.5-1.5v-.423l1.09-.452.3.3a1.5 1.5 0 0 0 2.121-2.121l-.3-.3.452-1.09H18a1.5 1.5 0 0 0 1.5-1.5V9A1.5 1.5 0 0 0 18 7.5Zm-8 6a3.5 3.5 0 1 1 0-7 3.5 3.5 0 0 1 0 7Z" />
                                    </svg>
                                </div>
                                <span class="font-medium">Alat</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.subscription-plans.index') }}"
                                class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('admin.subscription-plans.*') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                    <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                        viewBox="0 0 20 20">
                                        <path
                                            d="M10 0a8.8 8.8 0 0 1 6.6 2.9 9.2 9.2 0 0 1 2.9 6.6 8.8 8.8 0 0 1-2.9 6.6 9.2 9.2 0 0 1-6.6 2.9 8.8 8.8 0 0 1-6.6-2.9 9.2 9.2 0 0 1-2.9-6.6 8.8 8.8 0 0 1 2.9-6.6A9.2 9.2 0 0 1 10 0zm0 4.5a.8.8 0 0 0-.8.8v4.7c0 .*******.8h4.7a.8.8 0 0 0 0-1.6h-3.9V5.3a.8.8 0 0 0-.8-.8z" />
                                    </svg>
                                </div>
                                <span class="font-medium">Paket Langganan</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.payments.index') }}"
                                class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('admin.payments.*') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                    <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                        viewBox="0 0 20 20">
                                        <path
                                            d="M10.75 16.82A7.5 7.5 0 0 1 4.9 6.4l1.13 1.12a6 6 0 1 0 8.5-1.2L13.4 8.4h3.8V4.6L15.5 6.3a7.5 7.5 0 0 1-4.75 10.53v-.01z" />
                                    </svg>
                                </div>
                                <span class="font-medium">Pembayaran</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.settings.index') }}"
                                class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('admin.settings.*') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                    <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                        viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <span class="font-medium">Pengaturan</span>
                            </a>
                        </li>
                    @endif
                </ul>

                <!-- Download Menu (di bagian bawah sidebar) -->
                <div class="pt-4 border-t border-white/10">
                    @if (Auth::user()->role === 'customer')
                        @if ($isProjectPageContext)
                            <a href="{{ route('rab.index', ['showPreviewDownload' => true, 'project' => request()->route('project') ?? session('current_project_id')]) }}"
                                class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ $showPreviewDownload ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                                <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                    <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                        viewBox="0 0 20 20">
                                        <path
                                            d="M17.8 13.4l-.6.6c-.2.2-.5.2-.7 0-.2-.2-.2-.5 0-.7l.6-.6c.2-.2.2-.5 0-.7l-.5-.5c-.2-.2-.5-.2-.7 0l-.6.6c-.2.2-.5.2-.7 0-.2-.2-.2-.5 0-.7l.6-.6c.2-.2.2-.5 0-.7l-.5-.5c-.2-.2-.5-.2-.7 0l-.6.6c-.2.2-.5.2-.7 0-.2-.2-.2-.5 0-.7l.6-.6c.2-.2.5-.2.7 0l.5.5c.******* 0 .7l-.6.6c-.2.2-.2.5 0 .*******.2.7 0l.6-.6c.2-.2.5-.2.7 0l.5.5c.******* 0 .7l-.6.6c-.2.2-.2.5 0 .*******.2.7 0l.6-.6c.2-.2.5-.2.7 0l.5.5c.*******-.1.7z" />
                                    </svg>
                                </div>
                                <span class="font-medium">Laporan</span>
                            </a>
                        @endif

                        <a href="{{ route('subscriptions.index') }}"
                            class="flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20 {{ request()->routeIs('subscriptions.*') ? 'bg-white/20 dark:bg-dark-accent/30' : '' }}">
                            <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                                <svg class="w-5 h-5 text-white" aria-hidden="true" fill="currentColor"
                                    viewBox="0 0 20 20">
                                    <path
                                        d="M10 0a8.8 8.8 0 0 1 6.6 2.9 9.2 9.2 0 0 1 2.9 6.6 8.8 8.8 0 0 1-2.9 6.6 9.2 9.2 0 0 1-6.6 2.9 8.8 8.8 0 0 1-6.6-2.9 9.2 9.2 0 0 1-2.9-6.6 8.8 8.8 0 0 1 2.9-6.6A9.2 9.2 0 0 1 10 0zm0 4.5a.8.8 0 0 0-.8.8v4.7c0 .*******.8h4.7a.8.8 0 0 0 0-1.6h-3.9V5.3a.8.8 0 0 0-.8-.8z" />
                                </svg>
                            </div>
                            <span class="font-medium">Langganan</span>
                        </a>
                    @endif
                </div>

                <!-- Theme Toggle Button (Fixed at bottom of sidebar) -->
                <div class="mt-auto pt-4 border-t border-white/10">
                    <x-theme-toggle :toggleId="'theme-toggle-sidebar'" :toggleIconId="'theme-toggle-sidebar-icon'" :label="'Mode'" />
                </div>
            </div>
        </aside>
    @endif
@endauth
