// — Data model awal —
let tasks = [];
let scale = "week", // Default scale set to week
    zoom = 1,
    dayW = scale === "week" ? 60 : 30, // Base width: 60px for week, 30px for day
    lastGridWidth = 420, // Default grid width set to 420px
    projectId; // Make projectId accessible globally

// Helper function to get the start of the week (Monday)
function getStartOfWeek(date) {
    const d = new Date(date);
    const day = d.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when Sunday
    return new Date(d.setDate(diff));
}

// Helper function to get week number of the year was removed (unused)

// Helper function to get number of weeks between two dates
function getWeeksBetween(startDate, endDate) {
    const startWeek = getStartOfWeek(startDate).getTime();
    const endWeek = getStartOfWeek(endDate).getTime();
    return Math.ceil((endWeek - startWeek) / (7 * 864e5)) + 1; // +1 to include the start week
}

document.addEventListener("DOMContentLoaded", function () {
    // Window variable to track if user is in view-only mode
    window.viewOnly = document.getElementById("viewOnly")
        ? document.getElementById("viewOnly").value === "1"
        : false;
    console.log("View Only Mode:", window.viewOnly);

    // Hanya deklarasikan variabel yang benar-benar digunakan dalam scope ini
    const leftContainer = document.getElementById("left-container"),
        gridResizer = document.getElementById("grid-resizer"),
        scrollableCon = document.getElementById("scrollable-container"),
        resizeHandle = document.getElementById("resize-handle"),
        app = document.getElementById("app");

    // Check for success message from previous action
    const timeScheduleMessage = localStorage.getItem("timeScheduleMessage");
    if (timeScheduleMessage) {
        // Show success message using the application's toast system
        if (window.showSuccessToast) {
            window.showSuccessToast(timeScheduleMessage);
        } else {
            // Fallback if toast function is not available
            console.log(`SUCCESS: ${timeScheduleMessage}`);
        }
        // Clear the message from localStorage
        localStorage.removeItem("timeScheduleMessage");
    }

    // Set projectId as a global variable
    projectId = document.getElementById("projectId")?.value;

    // Ensure dayW is properly set based on scale
    dayW = scale === "week" ? 60 : 30;

    // Set default width for left-container
    if (leftContainer) {
        leftContainer.style.flexBasis = `${lastGridWidth}px`;
    }

    // Render grid lines immediately to ensure they're visible on page load
    renderGridLines();

    // Update grid-lines height to match grid-body
    updateGridLinesHeight();

    // Update grid-resizer height to match grid-body
    updateGridResizerHeight();

    // Update resize-handle max height to match chart-row terakhir
    updateResizeHandleHeight();

    // Setup locked buttons in view-only mode
    if (window.viewOnly) {
        setupLockedButtons();
    }

    // Ambil data dari database
    fetchTimeScheduleData();

    // Inisialisasi
    initResizer();
    initVerticalResizer();
    initControls();
    initDisplayToggles();

    // Fungsi untuk mengatur tombol yang terkunci dalam mode view-only
    function setupLockedButtons() {
        // Gembok tombol add-task
        const addTaskBtn = document.getElementById("add-task");
        if (addTaskBtn) {
            addTaskBtn.classList.add("locked-btn");
            // Hapus data-modal-target untuk mencegah modal terbuka otomatis
            addTaskBtn.removeAttribute("data-modal-target");

            // Fungsi untuk mencegah event click normal dan menampilkan toast
            const preventDefaultHandler = function (e) {
                e.preventDefault();
                e.stopPropagation();
                window.showLockedToast();
                return false;
            };

            // Hapus semua event listener lama
            const newBtn = addTaskBtn.cloneNode(true);
            addTaskBtn.parentNode.replaceChild(newBtn, addTaskBtn);

            // Tambahkan event listener baru untuk menampilkan toast saat diklik
            newBtn.addEventListener("click", preventDefaultHandler);

            // Tambahkan style untuk menunjukkan tombol tidak bisa diklik
            newBtn.style.pointerEvents = "all"; // Pastikan click event masih bisa ditangkap
        }

        // Observer untuk menggembok tombol action-btn pada kolom aksi (chart-cell) saat halaman dirender
        const observer = new MutationObserver(function (mutations) {
            // Setelah halaman dirender, temukan semua tombol action-btn di dalam chart-cell
            const actionButtons = document.querySelectorAll(
                ".chart-cell .action-btn"
            );
            actionButtons.forEach((btn) => {
                btn.classList.add("locked-btn");
                const originalOnClick = btn.getAttribute("onclick");
                btn.removeAttribute("onclick");
                btn.addEventListener("click", function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    showLockedToast();
                });
            });
        });

        // Observe perubahan pada grid-body
        const gridBody = document.getElementById("grid-body");
        if (gridBody) {
            observer.observe(gridBody, { childList: true, subtree: true });
        }

        // Panggil juga saat pertama kali untuk tombol yang sudah ada
        const actionButtons = document.querySelectorAll(
            ".chart-cell .action-btn"
        );
        actionButtons.forEach((btn) => {
            btn.classList.add("locked-btn");
            const originalOnClick = btn.getAttribute("onclick");
            btn.removeAttribute("onclick");
            btn.addEventListener("click", function (e) {
                e.preventDefault();
                e.stopPropagation();
                showLockedToast();
            });
        });
    }

    // Fungsi untuk menampilkan toast saat tombol terkunci diklik - tersedia secara global
    window.showLockedToast = function () {
        if (window.showInfoToast) {
            window.showInfoToast(
                "Anda hanya memiliki akses untuk melihat. Tidak dapat melakukan perubahan pada proyek ini. Silahkan hubungi pemilik proyek untuk mendapatkan akses sebagai Editor.",
                null,
                null,
                3000
            );
        } else {
            console.warn(
                "Anda hanya memiliki akses untuk melihat. Tidak dapat melakukan perubahan pada proyek ini. Silahkan hubungi pemilik proyek untuk mendapatkan akses sebagai Editor."
            );
        }
    };

    // Sync scrolling between the containers
    leftContainer.onscroll = () => {
        // Sync vertical scrolling with chart container
        scrollableCon.scrollTop = leftContainer.scrollTop;
    };

    scrollableCon.onscroll = () => {
        // Sync vertical scrolling with left container
        if (leftContainer.scrollTop !== scrollableCon.scrollTop) {
            leftContainer.scrollTop = scrollableCon.scrollTop;
        }
    };

    // Fungsi untuk mengambil data time schedule dari database
    function fetchTimeScheduleData() {
        if (!projectId) {
            console.warn("Project ID tidak ditemukan");
            return;
        }

        // Tambahkan loading state
        app.classList.add("opacity-50");

        fetch(`/time-schedule/project-data?project_id=${projectId}`)
            .then((response) => response.json())
            .then((data) => {
                if (data.success && data.data) {
                    tasks = data.data.map((item) => ({
                        id: item.id,
                        name: item.title,
                        start: new Date(item.start),
                        end: new Date(item.end),
                        duration: item.durasi,
                        price: parseItemPrice(item.item_id), // Dapat dari data RAB
                        weight: item.bobot,
                        progress: item.progress,
                        itemId: item.item_id,
                    }));

                    // Render dengan data yang sudah diambil
                    renderGrid();

                    // Pastikan seluruh proyek terlihat saat pertama kali dimuat
                    const { start, end } = calcRange();
                    const scrollableCon = document.getElementById(
                        "scrollable-container"
                    );
                    const containerWidth = scrollableCon.clientWidth;

                    let totalProjectUnits;
                    if (scale === "day") {
                        totalProjectUnits = Math.ceil((end - start) / 864e5);
                    } else {
                        // week
                        totalProjectUnits = getWeeksBetween(start, end);
                    }

                    // Hitung zoom yang tepat agar seluruh proyek terlihat
                    const requiredWidth = totalProjectUnits * dayW * zoom;

                    if (requiredWidth > containerWidth) {
                        // Jika proyek lebih lebar dari container, hitung zoom yang tepat
                        const exactZoom =
                            containerWidth / (totalProjectUnits * dayW);
                        zoom = exactZoom;
                    }

                    // Hilangkan scrollbar pada skala hari saat layout full
                    updateScrollbarVisibility();

                    renderTimeline();
                    renderTasks();
                    renderGridLines(); // Pastikan grid lines selalu tampil setelah data diambil
                    drawSCurve();
                } else {
                    console.error("Gagal mengambil data time schedule");
                    // Tetap render grid lines meskipun data gagal diambil
                    renderGridLines();
                }
            })
            .catch((error) => {
                console.error("Error fetching time schedule data:", error);
            })
            .finally(() => {
                // Hapus loading state
                app.classList.remove("opacity-50");
            });
    }

    // Helper untuk mendapatkan harga item dari elemen select
    function parseItemPrice(itemId) {
        if (!itemId) return 0;

        const itemSelect = document.getElementById("itemSelect");
        if (!itemSelect) return 0;

        const option = itemSelect.querySelector(`option[value="${itemId}"]`);
        return option ? parseFloat(option.getAttribute("data-price") || 0) : 0;
    }

    // Inisialisasi resizer untuk grid
    function initResizer() {
        if (!gridResizer) return;

        let isResizing = false;
        let startX, startWidth;

        gridResizer.addEventListener("mousedown", function (e) {
            isResizing = true;
            startX = e.clientX;
            startWidth = parseInt(
                document.defaultView.getComputedStyle(leftContainer).width,
                10
            );

            gridResizer.classList.add("active");

            document.addEventListener("mousemove", resizeGrid);
            document.addEventListener("mouseup", stopResize);

            e.preventDefault();
        });

        function resizeGrid(e) {
            if (!isResizing) return;

            const width = startWidth + e.clientX - startX;
            // Batasi ukuran minimum dan maksimum
            const minWidth = 400;
            const maxWidth = window.innerWidth * 0.7;

            if (width >= minWidth && width <= maxWidth) {
                leftContainer.style.flexBasis = `${width}px`;
                lastGridWidth = width;

                // Perbarui lebar header cells
                updateHeaderCellWidths();
            }
        }

        function stopResize() {
            isResizing = false;
            gridResizer.classList.remove("active");

            document.removeEventListener("mousemove", resizeGrid);
            document.removeEventListener("mouseup", stopResize);

            // Re-render setelah resize untuk memastikan semua konten tersaji dengan benar
            renderTimeline();
            renderTasks();
        }

        function updateHeaderCellWidths() {
            // Menyesuaikan lebar header dengan lebar grid
            const headerCells = document.querySelectorAll(".header-cell");
            const gridWidth = lastGridWidth;

            if (headerCells.length >= 5) {
                // Calculate first column width dynamically based on remaining space
                // First column width should adjust when resizing the grid
                const firstColWidth = Math.max(
                    200,
                    Math.floor(gridWidth * 0.5)
                ); // At least 200px, up to 50% of grid

                // Update first column width (Item Pekerjaan)
                headerCells[0].style.width = `${firstColWidth}px`;

                // Compute remaining width for other columns
                const remainingWidth = gridWidth - firstColWidth;

                // Reserve 50px for action column
                const availableWidth = remainingWidth - 50;

                // Allocate equal width to other columns (Harga, Durasi, Bobot)
                const columnWidth = Math.floor(availableWidth / 3);

                // Sesuaikan lebar cell
                headerCells[1].style.width = `${columnWidth}px`; // Harga
                headerCells[2].style.width = `${columnWidth}px`; // Durasi
                headerCells[3].style.width = `${columnWidth}px`; // Bobot
                headerCells[4].style.width = "50px"; // Aksi

                // Update CSS variables for other components to use
                document.documentElement.style.setProperty(
                    "--cell-width-1",
                    `${firstColWidth}px`
                );
                document.documentElement.style.setProperty(
                    "--cell-width-2",
                    `${columnWidth}px`
                );
                document.documentElement.style.setProperty(
                    "--cell-width-3",
                    `${columnWidth}px`
                );
                document.documentElement.style.setProperty(
                    "--cell-width-4",
                    `${columnWidth}px`
                );
                document.documentElement.style.setProperty(
                    "--cell-width-5",
                    "50px"
                );
            }

            // Update chart-cell widths to match header widths - this is needed for table-layout fixed
            const chartCells = document.querySelectorAll("chart-cell");
            for (let i = 0; i < chartCells.length; i++) {
                const cellIndex = i % 5;
                if (cellIndex < 5 && headerCells[cellIndex]) {
                    chartCells[i].style.width =
                        headerCells[cellIndex].style.width;
                }
            }
        }

        // Inisialisasi lebar awal
        updateHeaderCellWidths();
    }

    // Fungsi untuk menginisialisasi toggle tampilan
    function initDisplayToggles() {
        const showGanttCheckbox = document.getElementById("show-gantt");
        const showScurveCheckbox = document.getElementById("show-scurve");
        const scrollableCon = document.getElementById("scrollable-container");
        const leftContainer = document.getElementById("left-container");
        const scurveCanvas = document.getElementById("scurve");
        const tasksContainer = document.getElementById("tasks");
        const headerRow = document.querySelector(".header-row");

        // Fungsi untuk mengupdate tampilan berdasarkan checkbox
        function updateDisplay() {
            const showGantt = showGanttCheckbox.checked;
            const showScurve = showScurveCheckbox.checked;

            // Tampilkan/sembunyikan Gantt Chart
            tasksContainer.style.display = showGantt ? "block" : "none";

            // Tampilkan/sembunyikan Kurva S
            scurveCanvas.style.display = showScurve ? "block" : "none";

            // Jika keduanya tidak dicentang, left-container melebar maksimal
            if (!showGantt && !showScurve) {
                scrollableCon.style.display = "none";
                leftContainer.style.flexBasis = "100%";

                // Update header row width untuk mengisi seluruh container
                if (headerRow) {
                    headerRow.style.width = "100%";
                }

                // Pastikan grid-resizer juga menyesuaikan
                const gridResizer = document.getElementById("grid-resizer");
                if (gridResizer) {
                    gridResizer.style.right = "0";
                    gridResizer.style.display = "none"; // Sembunyikan resizer
                }

                // Menyesuaikan lebar header-cell dan chart-cell saat melebar
                const headerCells = document.querySelectorAll(".header-cell");

                if (headerCells.length >= 5) {
                    // Hapus semua width yang diatur sebelumnya
                    headerCells.forEach((cell) => {
                        cell.style.width = "";
                        // Tambahkan class untuk mode expanded
                        cell.classList.add("expanded");
                    });

                    // Khusus untuk kolom aksi, tetapkan lebar tetap
                    headerCells[4].style.width = "60px";
                    headerCells[4].style.flex = "0 0 60px";

                    // Update CSS variables untuk chart-cell
                    document.documentElement.style.setProperty(
                        "--cell-width-5",
                        "60px"
                    );

                    // Update chart-cell untuk mengikuti header-cell
                    const chartRows = document.querySelectorAll("chart-row");
                    chartRows.forEach((row) => {
                        // Tambahkan class expanded ke chart-row untuk mengubahnya menjadi flex container
                        row.classList.add("expanded");

                        const cells = row.querySelectorAll("chart-cell");
                        if (cells.length >= 5) {
                            // Hapus semua width yang diatur sebelumnya
                            for (let i = 0; i < 4; i++) {
                                cells[i].style.width = "";
                                cells[i].classList.add("expanded");

                                // Dapatkan lebar header-cell yang sesuai dan terapkan ke chart-cell
                                const headerWidth = headerCells[i].offsetWidth;
                                cells[i].style.width = `${headerWidth}px`;
                            }

                            // Khusus untuk kolom aksi, tetapkan lebar tetap
                            cells[4].style.width = "60px";
                            cells[4].style.flex = "0 0 60px";
                        }
                    });
                }
            } else {
                scrollableCon.style.display = "block";
                leftContainer.style.flexBasis = `${lastGridWidth}px`;

                // Kembalikan header row width ke default
                if (headerRow) {
                    headerRow.style.width = "";
                }

                // Kembalikan posisi grid-resizer
                const gridResizer = document.getElementById("grid-resizer");
                if (gridResizer) {
                    gridResizer.style.right = "";
                    gridResizer.style.display = "block"; // Tampilkan kembali resizer
                }

                // Kembalikan lebar header-cell dan chart-cell ke ukuran default
                // Hapus class expanded dari semua cell
                const headerCells = document.querySelectorAll(".header-cell");
                headerCells.forEach((cell) => {
                    cell.classList.remove("expanded");
                    cell.style.flex = "";
                });

                // Hapus class expanded dari semua chart-row dan chart-cell
                const chartRows = document.querySelectorAll("chart-row");
                chartRows.forEach((row) => {
                    // Hapus class expanded dari chart-row
                    row.classList.remove("expanded");

                    const cells = row.querySelectorAll("chart-cell");
                    cells.forEach((cell) => {
                        cell.classList.remove("expanded");
                        cell.style.flex = "";
                        // Reset display ke table-cell
                        cell.style.display = "";
                    });
                });

                updateHeaderCellWidths();
            }

            // Re-render untuk memastikan tampilan yang benar
            renderTimeline();
            renderTasks();
            renderGridLines();
            drawSCurve();

            // Update tinggi grid-lines agar hanya sampai pada grid line horizontal terakhir
            updateGridLinesHeight();
        }

        // Fungsi untuk memperbarui lebar header cell
        function updateHeaderCellWidths() {
            // Menyesuaikan lebar header dengan lebar grid
            const headerCells = document.querySelectorAll(".header-cell");
            const gridWidth = lastGridWidth;

            if (headerCells.length >= 5) {
                // Calculate first column width dynamically based on remaining space
                // First column width should adjust when resizing the grid
                const firstColWidth = Math.max(
                    200,
                    Math.floor(gridWidth * 0.5)
                ); // At least 200px, up to 50% of grid

                // Update first column width (Item Pekerjaan)
                headerCells[0].style.width = `${firstColWidth}px`;

                // Compute remaining width for other columns
                const remainingWidth = gridWidth - firstColWidth;

                // Reserve 60px for action column
                const availableWidth = remainingWidth - 60;

                // Allocate equal width to other columns (Harga, Durasi, Bobot)
                const columnWidth = Math.floor(availableWidth / 3);

                // Sesuaikan lebar cell
                headerCells[1].style.width = `${columnWidth}px`; // Harga
                headerCells[2].style.width = `${columnWidth}px`; // Durasi
                headerCells[3].style.width = `${columnWidth}px`; // Bobot
                headerCells[4].style.width = "60px"; // Aksi

                // Update CSS variables for other components to use
                document.documentElement.style.setProperty(
                    "--cell-width-1",
                    `${firstColWidth}px`
                );
                document.documentElement.style.setProperty(
                    "--cell-width-2",
                    `${columnWidth}px`
                );
                document.documentElement.style.setProperty(
                    "--cell-width-3",
                    `${columnWidth}px`
                );
                document.documentElement.style.setProperty(
                    "--cell-width-4",
                    `${columnWidth}px`
                );
                document.documentElement.style.setProperty(
                    "--cell-width-5",
                    "60px"
                );

                // Update chart-cell widths to match header widths
                const chartCells = document.querySelectorAll("chart-cell");
                for (let i = 0; i < chartCells.length; i++) {
                    const cellIndex = i % 5;
                    if (cellIndex < 5 && headerCells[cellIndex]) {
                        chartCells[i].style.width =
                            headerCells[cellIndex].style.width;
                    }
                }
            }
        }

        // Tambahkan event listener ke checkbox
        if (showGanttCheckbox) {
            showGanttCheckbox.addEventListener("change", updateDisplay);
        }

        if (showScurveCheckbox) {
            showScurveCheckbox.addEventListener("change", updateDisplay);
        }

        // Inisialisasi tampilan awal
        updateDisplay();
    }

    // Fungsi inisialisasi kontrol
    function initControls() {
        const scaleSelect = document.getElementById("scale");
        const zoomInBtn = document.getElementById("zoom-in");
        const zoomOutBtn = document.getElementById("zoom-out");
        const scrollableCon = document.getElementById("scrollable-container");
        const maxZoomInWidth = 240; // Maximum width for one grid unit (day/week)

        // Function to check and update zoom button states
        function updateZoomButtons() {
            // Zoom In Limit Check
            const nextZoomInWidth = dayW * (zoom * 2);
            zoomInBtn.disabled = nextZoomInWidth > maxZoomInWidth;

            // Zoom Out Limit Check
            const { start, end } = calcRange();
            let totalProjectUnits; // Days or Weeks
            if (scale === "day") {
                totalProjectUnits = Math.ceil((end - start) / 864e5);
            } else {
                // week
                totalProjectUnits = getWeeksBetween(start, end);
            }
            // Hitung lebar container untuk perbandingan
            const containerWidth = scrollableCon.clientWidth;

            // Cek apakah scrollbar aktif
            const chartCon = document.getElementById("chart-container");
            const isScrollbarActive = chartCon.scrollWidth > containerWidth;

            // Jika scrollbar aktif, tombol zoom out selalu aktif
            // Jika tidak, disable hanya jika proyek sudah pas dengan container
            const exactZoom = containerWidth / (totalProjectUnits * dayW);
            zoomOutBtn.disabled = !isScrollbarActive && zoom <= exactZoom;

            // Apply visual styling for disabled state
            [zoomInBtn, zoomOutBtn].forEach((btn) => {
                if (btn.disabled) {
                    btn.classList.add("opacity-50", "cursor-not-allowed");
                } else {
                    btn.classList.remove("opacity-50", "cursor-not-allowed");
                }
            });
        }

        // Set initial scale from the variable
        if (scaleSelect) {
            scaleSelect.value = scale;
        }

        zoomInBtn.onclick = () => {
            const nextZoom = zoom * 2;
            const nextWidth = dayW * nextZoom;
            if (nextWidth <= maxZoomInWidth) {
                zoom = nextZoom;
                renderTimeline();
                renderTasks();
                renderGridLines();
                drawSCurve();
                updateZoomButtons(); // Update button states after zoom
                updateScrollbarVisibility(); // Update scrollbar visibility
            }
        };

        zoomOutBtn.onclick = () => {
            const nextZoom = zoom / 2;
            const { start, end } = calcRange();
            let totalProjectUnits; // Days or Weeks
            if (scale === "day") {
                totalProjectUnits = Math.ceil((end - start) / 864e5);
            } else {
                // week
                totalProjectUnits = getWeeksBetween(start, end);
            }
            const requiredWidth = totalProjectUnits * dayW * nextZoom;
            const containerWidth = scrollableCon.clientWidth;

            // Hapus batas jarak minimal grid line
            // Hanya batasi zoom out agar seluruh proyek terlihat dalam container
            // Jika nextZoom membuat lebar proyek lebih kecil dari container, hitung zoom yang tepat
            if (requiredWidth < containerWidth) {
                // Hitung zoom yang tepat agar proyek pas dengan lebar container
                const exactZoom = containerWidth / (totalProjectUnits * dayW);
                zoom = exactZoom;
            } else {
                zoom = nextZoom;
            }

            renderTimeline();
            renderTasks();
            renderGridLines(); // Pastikan grid lines selalu tampil setelah zoom
            drawSCurve();
            updateZoomButtons(); // Update button states after zoom
            updateScrollbarVisibility(); // Update scrollbar visibility
        };

        scaleSelect.onchange = (e) => {
            const oldScale = scale;
            scale = e.target.value;

            // Update dayW based on scale
            const oldDayW = dayW;
            dayW = scale === "week" ? 60 : 30;

            // Adjust zoom to maintain appropriate width when switching scales
            if (oldScale !== scale) {
                if (scale === "week" && oldScale === "day") {
                    // Day to week: ensure week width is at least 60px
                    zoom = Math.max(1, zoom * (oldDayW / dayW));
                } else if (scale === "day" && oldScale === "week") {
                    // Week to day: ensure day width is at least 30px
                    zoom = Math.max(1, zoom * (oldDayW / dayW));
                }

                // Setelah pergantian skala, pastikan seluruh proyek terlihat
                const { start, end } = calcRange();
                const scrollableCon = document.getElementById(
                    "scrollable-container"
                );
                const containerWidth = scrollableCon.clientWidth;

                let totalProjectUnits;
                if (scale === "day") {
                    totalProjectUnits = Math.ceil((end - start) / 864e5);
                } else {
                    // week
                    totalProjectUnits = getWeeksBetween(start, end);
                }

                // Hitung zoom yang tepat agar seluruh proyek terlihat
                const requiredWidth = totalProjectUnits * dayW * zoom;

                if (requiredWidth > containerWidth) {
                    // Jika proyek lebih lebar dari container, hitung zoom yang tepat
                    const exactZoom =
                        containerWidth / (totalProjectUnits * dayW);
                    zoom = exactZoom;
                }
            }

            renderTimeline();
            renderTasks();
            renderGridLines(); // Pastikan grid lines selalu tampil setelah perubahan skala
            drawSCurve();
            updateZoomButtons(); // Update button states after scale change
            updateScrollbarVisibility(); // Update scrollbar visibility
        };

        // Reset modal saat tombol add-task diklik
        const addTaskBtn = document.getElementById("add-task");
        if (addTaskBtn && !window.viewOnly) {
            // Hanya tambahkan event listener jika BUKAN dalam mode view-only
            addTaskBtn.addEventListener("click", function () {
                // Reset form
                const addItemForm = document.getElementById("addItemForm");
                if (addItemForm) {
                    addItemForm.reset();
                }

                // Reset submit button ke tampilan awal
                const submitBtn = document.querySelector(
                    "#addItemForm button[type='submit']"
                );
                if (submitBtn) {
                    submitBtn.innerHTML =
                        '<i class="fas fa-plus-circle mr-1"></i>Tambah Item';
                    submitBtn.classList.remove("loading-btn");
                    submitBtn.disabled = false;
                }

                // Tampilkan modal tambahItemModal dengan cara yang aman
                const tambahItemModalEl =
                    document.getElementById("tambahItemModal");
                if (tambahItemModalEl) {
                    tambahItemModalEl.classList.remove("hidden");
                    tambahItemModalEl.style.display = "flex";
                }
            });
        }

        // Inisialisasi modal tambahItemModal agar tidak close saat klik di luar
        const tambahItemModalEl = document.getElementById("tambahItemModal");
        if (tambahItemModalEl) {
            // Mencegah modal close saat klik di luar
            tambahItemModalEl.addEventListener("click", function (e) {
                // Jika klik tepat pada modal background (bukan pada konten modal)
                if (e.target === tambahItemModalEl) {
                    // Mencegah modal close
                    e.stopPropagation();
                }
            });
        }

        // Inisialisasi modal editItemModal agar tidak close saat klik di luar
        const editItemModalEl = document.getElementById("editItemModal");
        if (editItemModalEl) {
            // Mencegah modal close saat klik di luar
            editItemModalEl.addEventListener("click", function (e) {
                // Jika klik tepat pada modal background (bukan pada konten modal)
                if (e.target === editItemModalEl) {
                    // Mencegah modal close
                    e.stopPropagation();
                }
            });
        }

        // Form tambah item dari RAB
        const addItemForm = document.getElementById("addItemForm");
        if (addItemForm) {
            // Saat item dipilih, isi otomatis harga dan bobot
            const itemSelect = document.getElementById("itemSelect");
            const itemPriceInput = document.getElementById("itemPrice");
            const itemWeightInput = document.getElementById("itemWeight");

            // Form edit item
            const editItemForm = document.getElementById("editItemForm");
            if (editItemForm) {
                // Saat item dipilih, isi otomatis harga dan bobot
                const editItemSelect =
                    document.getElementById("editItemSelect");
                const editItemPriceInput =
                    document.getElementById("editItemPrice");
                const editItemWeightInput =
                    document.getElementById("editItemWeight");

                if (editItemSelect) {
                    editItemSelect.addEventListener("change", function () {
                        const selectedOption = this.options[this.selectedIndex];
                        if (selectedOption.value) {
                            const price =
                                selectedOption.getAttribute("data-price");
                            const bobot =
                                selectedOption.getAttribute("data-bobot");

                            // Format harga sebagai mata uang Indonesia
                            editItemPriceInput.value = new Intl.NumberFormat(
                                "id-ID"
                            ).format(price);
                            editItemPriceInput.dataset.rawValue = price;

                            // Isi bobot
                            editItemWeightInput.value = bobot;
                        } else {
                            editItemPriceInput.value = "";
                            editItemPriceInput.dataset.rawValue = "0";
                            editItemWeightInput.value = "";
                        }
                    });
                }

                // Handle form submit
                editItemForm.addEventListener("submit", function (e) {
                    e.preventDefault();

                    const editItemSelect =
                        document.getElementById("editItemSelect");
                    const editStartDate =
                        document.getElementById("editStartDate").value;
                    const editDuration = parseInt(
                        document.getElementById("editDuration").value
                    );
                    const editTaskId =
                        document.getElementById("editTaskId").value;

                    if (
                        !editItemSelect.value ||
                        !editStartDate ||
                        !editDuration ||
                        !editTaskId
                    ) {
                        if (window.showWarningToast) {
                            window.showWarningToast(
                                "Mohon lengkapi semua field"
                            );
                        } else {
                            alert("Mohon lengkapi semua field");
                        }
                        return;
                    }

                    // Get the submit button and set loading state
                    const submitBtn = this.querySelector(
                        'button[type="submit"]'
                    );
                    if (submitBtn) {
                        submitBtn.innerHTML =
                            '<i class="fas fa-spinner fa-spin spinner mr-2"></i> <span>Menyimpan...</span>';
                        submitBtn.disabled = true;
                        submitBtn.classList.add("loading-btn");
                    }

                    const selectedOption =
                        editItemSelect.options[editItemSelect.selectedIndex];
                    const itemName = selectedOption.textContent.split(" (")[0];
                    const itemId = parseInt(editItemSelect.value);
                    const itemWeight = parseFloat(
                        editItemWeightInput.value || 0
                    );

                    // Hitung tanggal selesai
                    const endDate = new Date(editStartDate);
                    endDate.setDate(endDate.getDate() + editDuration - 1); // Kurangi 1 karena hari pertama dihitung

                    // Distribusi bobot sesuai dengan durasi
                    const distribusiBobot = window.generateBobotDistribution(
                        editStartDate,
                        editDuration,
                        itemWeight
                    );

                    // Update ke database
                    updateTimeSchedule({
                        project_id: projectId,
                        id: editTaskId,
                        item_pekerjaan_id: itemId,
                        nama_kegiatan: itemName,
                        tanggal_mulai: editStartDate,
                        tanggal_selesai: endDate.toISOString().split("T")[0],
                        bobot: itemWeight,
                        durasi: editDuration,
                        distribusi_bobot: distribusiBobot,
                    });
                });
            }

            if (itemSelect) {
                itemSelect.addEventListener("change", function () {
                    const selectedOption = this.options[this.selectedIndex];
                    if (selectedOption.value) {
                        const price = selectedOption.getAttribute("data-price");
                        const bobot = selectedOption.getAttribute("data-bobot");

                        // Format harga sebagai mata uang Indonesia
                        itemPriceInput.value = new Intl.NumberFormat(
                            "id-ID"
                        ).format(price);
                        itemPriceInput.dataset.rawValue = price;

                        // Isi bobot
                        itemWeightInput.value = bobot;
                    } else {
                        itemPriceInput.value = "";
                        itemPriceInput.dataset.rawValue = "0";
                        itemWeightInput.value = "";
                    }
                });
            }

            addItemForm.addEventListener("submit", function (e) {
                e.preventDefault();

                const itemSelect = document.getElementById("itemSelect");
                const startDate = document.getElementById("startDate").value;
                const duration = parseInt(
                    document.getElementById("duration").value
                );

                if (!itemSelect.value || !startDate || !duration) {
                    if (window.showWarningToast) {
                        window.showWarningToast("Mohon lengkapi semua field");
                    } else {
                        alert("Mohon lengkapi semua field");
                    }
                    return;
                }

                // Get the submit button and set loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML =
                        '<i class="fas fa-spinner fa-spin spinner mr-2"></i> <span>Menyimpan...</span>';
                    submitBtn.disabled = true;
                    submitBtn.classList.add("loading-btn");
                }

                const selectedOption =
                    itemSelect.options[itemSelect.selectedIndex];
                const itemName = selectedOption.textContent.split(" (")[0];
                const itemId = parseInt(itemSelect.value);
                const itemWeight = parseFloat(itemWeightInput.value || 0);

                // Hitung tanggal selesai
                const endDate = new Date(startDate);
                endDate.setDate(endDate.getDate() + duration - 1); // Kurangi 1 karena hari pertama dihitung

                // Distribusi bobot sesuai dengan durasi
                const distribusiBobot = generateBobotDistribution(
                    startDate,
                    duration,
                    itemWeight
                );

                // Simpan ke database
                saveTimeSchedule({
                    project_id: projectId,
                    item_pekerjaan_id: itemId,
                    nama_kegiatan: itemName,
                    tanggal_mulai: startDate,
                    tanggal_selesai: endDate.toISOString().split("T")[0],
                    bobot: itemWeight,
                    durasi: duration,
                    distribusi_bobot: distribusiBobot,
                });
            });
        }

        // Initial check for zoom button states
        updateZoomButtons();
    }

    // Fungsi untuk membuat distribusi bobot untuk setiap hari (dipindahkan ke scope global)
    window.generateBobotDistribution = function (
        startDateStr,
        duration,
        totalWeight
    ) {
        if (duration <= 0 || !totalWeight) {
            return [];
        }

        // Buat array untuk menyimpan distribusi bobot
        const distribution = [];

        // Distribusi bobot harian - distribusi merata
        const dailyWeight = parseFloat((totalWeight / duration).toFixed(4));

        // Buat date object dari string tanggal mulai
        const currentDate = new Date(startDateStr);

        // Generate distribusi bobot untuk setiap hari
        for (let i = 0; i < duration; i++) {
            distribution.push({
                tanggal: currentDate.toISOString().split("T")[0],
                bobot: dailyWeight,
            });

            // Tambah satu hari
            currentDate.setDate(currentDate.getDate() + 1);
        }

        // Pastikan total bobot tepat sama dengan bobot asli (mengatasi masalah pembulatan)
        let sumWeight = distribution.reduce((sum, item) => sum + item.bobot, 0);

        // Jika ada perbedaan karena pembulatan, tambahkan selisih ke hari terakhir
        if (Math.abs(sumWeight - totalWeight) > 0.0001) {
            const lastIndex = distribution.length - 1;
            distribution[lastIndex].bobot = parseFloat(
                (
                    distribution[lastIndex].bobot +
                    (totalWeight - sumWeight)
                ).toFixed(4)
            );
        }

        // Pastikan semua nilai bobot adalah angka, bukan string
        for (let i = 0; i < distribution.length; i++) {
            distribution[i].bobot = parseFloat(
                distribution[i].bobot.toFixed(4)
            );
        }

        return distribution;
    };

    // Simpan time schedule ke database
    function saveTimeSchedule(scheduleData) {
        // Tampilkan loading
        app.classList.add("opacity-50");

        // Mode tambah
        const url = "/time-schedule";
        const method = "POST";

        // Store success message for after reload
        const successMessage = "Item berhasil ditambahkan";

        // Kirim data ke server
        sendTimeScheduleData(url, method, scheduleData, successMessage);
    }

    // Update time schedule ke database
    function updateTimeSchedule(scheduleData) {
        // Tampilkan loading
        app.classList.add("opacity-50");

        // Mode edit
        const url = `/time-schedule/${scheduleData.id}`;
        const method = "PUT";

        // Store success message for after reload
        const successMessage = "Item berhasil diperbarui";

        // Kirim data ke server
        sendTimeScheduleData(url, method, scheduleData, successMessage);
    }

    // Fungsi untuk mengirim data time schedule ke server
    function sendTimeScheduleData(url, method, scheduleData, successMessage) {
        fetch(url, {
            method: method,
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: JSON.stringify(scheduleData),
        })
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    // Store success message in localStorage to show after page reload
                    localStorage.setItem("timeScheduleMessage", successMessage);

                    // Reset form berdasarkan modal yang aktif
                    if (method === "POST") {
                        // Reset form tambah
                        document.getElementById("addItemForm").reset();

                        // Reset submit button
                        const submitBtn = document.querySelector(
                            "#addItemForm button[type='submit']"
                        );
                        if (submitBtn) {
                            submitBtn.innerHTML =
                                '<i class="fas fa-plus-circle mr-1"></i>Tambah Item';
                            submitBtn.classList.remove("loading-btn");
                            submitBtn.disabled = false;
                        }

                        // Tutup modal tambah
                        const modalEl =
                            document.getElementById("tambahItemModal");
                        if (modalEl) {
                            closeModal(modalEl);
                        }
                    } else {
                        // Reset form edit
                        document.getElementById("editItemForm").reset();

                        // Reset submit button
                        const submitBtn = document.querySelector(
                            "#editItemForm button[type='submit']"
                        );
                        if (submitBtn) {
                            submitBtn.innerHTML =
                                '<i class="fas fa-save mr-1"></i>Update Item';
                            submitBtn.classList.remove("loading-btn");
                            submitBtn.disabled = false;
                        }

                        // Tutup modal edit
                        const modalEl =
                            document.getElementById("editItemModal");
                        if (modalEl) {
                            closeModal(modalEl);
                        }
                    }

                    // Reload the page
                    window.location.reload();
                } else {
                    // Reset button state if there's an error
                    if (method === "POST") {
                        const submitBtn = document.querySelector(
                            "#addItemForm button[type='submit']"
                        );
                        if (submitBtn) {
                            submitBtn.innerHTML =
                                '<i class="fas fa-plus-circle mr-1"></i>Tambah Item';
                            submitBtn.classList.remove("loading-btn");
                            submitBtn.disabled = false;
                        }
                    } else {
                        const submitBtn = document.querySelector(
                            "#editItemForm button[type='submit']"
                        );
                        if (submitBtn) {
                            submitBtn.innerHTML =
                                '<i class="fas fa-save mr-1"></i>Update Item';
                            submitBtn.classList.remove("loading-btn");
                            submitBtn.disabled = false;
                        }
                    }

                    if (window.showErrorToast) {
                        window.showErrorToast(
                            data.message || "Gagal menyimpan time schedule"
                        );
                    } else {
                        alert(data.message || "Gagal menyimpan time schedule");
                    }
                }
            })
            .catch((error) => {
                console.error("Error saving time schedule:", error);
                if (window.showErrorToast) {
                    window.showErrorToast(
                        "Terjadi kesalahan saat menyimpan time schedule"
                    );
                } else {
                    alert("Terjadi kesalahan saat menyimpan time schedule");
                }
            })
            .finally(() => {
                // Hapus loading
                app.classList.remove("opacity-50");
            });
    }

    // Fungsi untuk menutup modal dengan aman
    function closeModal(modalEl) {
        // Cek apakah flowbite tersedia
        if (typeof flowbite !== "undefined" && flowbite.Modal) {
            // Gunakan Flowbite modal jika tersedia
            const modal = flowbite.Modal.getInstance(modalEl);
            if (modal) {
                modal.hide();
            } else {
                modalEl.classList.add("hidden");
                modalEl.style.display = "none";
            }
        } else {
            // Fallback ke DOM API jika flowbite tidak tersedia
            modalEl.classList.add("hidden");
            modalEl.style.display = "none";
        }
    }

    // Fungsi global untuk menutup modal tambahItemModal
    window.closeTambahItemModal = function () {
        const modalEl = document.getElementById("tambahItemModal");
        if (modalEl) {
            closeModal(modalEl);
        }
    };

    // Fungsi global untuk menutup modal editItemModal
    window.closeEditItemModal = function () {
        const modalEl = document.getElementById("editItemModal");
        if (modalEl) {
            closeModal(modalEl);
        }
    };

    // Inisialisasi resizer untuk resize vertikal
    function initVerticalResizer() {
        if (!resizeHandle) return;

        // Inisialisasi tinggi maksimum resize-handle saat pertama kali
        updateResizeHandleHeight();

        let isResizing = false;
        let startY, startHeight;

        resizeHandle.addEventListener("mousedown", function (e) {
            isResizing = true;
            startY = e.clientY;
            startHeight = app.offsetHeight;

            // Pastikan window.maxAppHeight sudah diperbarui
            updateResizeHandleHeight();

            resizeHandle.classList.add("active");

            document.addEventListener("mousemove", resizeVertical);
            document.addEventListener("mouseup", stopVerticalResize);

            e.preventDefault();
        });

        function resizeVertical(e) {
            if (!isResizing) return;

            const newHeight = startHeight + (e.clientY - startY);
            // Batasi ukuran minimum dan maksimum
            const minHeight = 500;
            // Gunakan window.maxAppHeight yang sudah dihitung di updateResizeHandleHeight()
            const maxHeight = window.maxAppHeight || 1200;

            if (newHeight >= minHeight && newHeight <= maxHeight) {
                app.style.height = `${newHeight}px`;

                // Re-render setelah resize untuk memastikan semua konten tersaji dengan benar
                renderTimeline();
                renderTasks();
            }
        }

        function stopVerticalResize() {
            isResizing = false;
            resizeHandle.classList.remove("active");

            document.removeEventListener("mousemove", resizeVertical);
            document.removeEventListener("mouseup", stopVerticalResize);

            // Re-render untuk memastikan semua konten tersaji dengan benar
            renderTimeline();
            renderTasks();
        }
    }
});

// Hitung rentang waktu
function calcRange() {
    if (tasks.length === 0) {
        // Default rentang waktu jika tidak ada tasks
        const today = new Date();
        const start = getStartOfWeek(today);
        const end = new Date(start);
        end.setDate(end.getDate() + 7 * 4); // Default to 4 weeks view
        return { start: start, end: end };
    }

    let min = Infinity,
        max = -Infinity;
    tasks.forEach((t) => {
        min = Math.min(min, t.start.getTime());
        const taskEnd = new Date(t.start);
        taskEnd.setDate(taskEnd.getDate() + t.duration);
        max = Math.max(max, taskEnd.getTime());
    });

    let startDate = new Date(min);
    let endDate = new Date(max);

    // Snap start date to the beginning of the week if scale is week
    if (scale === "week") {
        startDate = getStartOfWeek(startDate);
    } else {
        // Untuk skala hari, kita tetap mulai dari awal bulan untuk konsistensi
        startDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
    }

    // Add padding at the end to ensure we see the full project
    // Untuk skala minggu, tambahkan 1 minggu, untuk skala hari tambahkan hingga akhir bulan
    if (scale === "week") {
        // Tambahkan 1 minggu dan snap ke akhir minggu
        endDate.setDate(endDate.getDate() + 7);
        const endDay = endDate.getDay(); // 0=Sun, 6=Sat
        const diffToEnd = endDay === 0 ? 0 : 7 - endDay; // Days to add to reach Sunday
        endDate.setDate(endDate.getDate() + diffToEnd);
    } else {
        // Untuk skala hari, tampilkan hingga akhir bulan
        endDate = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0);
    }

    return { start: startDate, end: endDate };
}

// Render grid kiri: bobot=(price/Σprice)*100%
function renderGrid() {
    const gridBody = document.getElementById("grid-body");

    let sum = tasks.reduce((s, t) => s + (parseFloat(t.price) || 0), 0);
    let sumWeight = 0;

    gridBody.innerHTML = "";

    tasks.forEach((t) => {
        // Pastikan weight adalah number
        if (
            t.weight === undefined ||
            t.weight === null ||
            isNaN(parseFloat(t.weight))
        ) {
            t.weight = sum > 0 ? (parseFloat(t.price || 0) / sum) * 100 : 0;
        } else {
            t.weight = parseFloat(t.weight);
        }

        sumWeight += t.weight;

        const row = document.createElement("chart-row"),
            c1 = document.createElement("chart-cell"),
            c2 = document.createElement("chart-cell"),
            c3 = document.createElement("chart-cell"),
            c4 = document.createElement("chart-cell"),
            c5 = document.createElement("chart-cell");

        // Set text content for each cell
        c1.innerText = t.name;

        const formattedPrice = new Intl.NumberFormat("id-ID").format(
            t.price || 0
        );
        c2.innerText = formattedPrice;

        const durationText = t.duration + " Hari";
        c3.innerText = durationText;

        const weightText = t.weight.toFixed(2) + "%";
        c4.innerText = weightText;

        // Add action buttons (edit, delete)
        // Tidak perlu menambahkan class action-btn-item ke chart-cell
        // karena ini menyebabkan background tidak konsisten
        const actionBtn = document.createElement("button");
        actionBtn.className =
            "action-btn" + (window.viewOnly ? " locked-btn" : "");
        actionBtn.innerHTML = '<i class="fas fa-ellipsis-v"></i>';

        if (window.viewOnly) {
            // Dalam mode view-only, tambahkan handler click yang menampilkan pesan toast
            actionBtn.onclick = function (e) {
                e.stopPropagation();
                if (typeof showLockedToast === "function") {
                    showLockedToast();
                } else if (window.showInfoToast) {
                    window.showInfoToast(
                        "Anda hanya memiliki akses untuk melihat. Tidak dapat melakukan perubahan pada proyek ini.",
                        null,
                        null,
                        3000
                    );
                }
            };
        } else {
            // Dalam mode normal, tambahkan handler click normal
        actionBtn.onclick = function (e) {
            e.stopPropagation();
            showActionMenu(e, t);
        };
        }

        c5.appendChild(actionBtn);

        row.append(c1, c2, c3, c4, c5);
        gridBody.appendChild(row);
    });

    // Add total row at the bottom of the grid-body
    const totalRow = document.createElement("chart-row"),
        tc1 = document.createElement("chart-cell"),
        tc2 = document.createElement("chart-cell"),
        tc3 = document.createElement("chart-cell"),
        tc4 = document.createElement("chart-cell"),
        tc5 = document.createElement("chart-cell");

    // Update grid-lines height to match grid-body
    updateGridLinesHeight();

    // Update grid-resizer height to match grid-body
    updateGridResizerHeight();

    // Update resize-handle max height to match chart-row terakhir
    updateResizeHandleHeight();

    // Add footer class to all cells
    [tc1, tc2, tc3, tc4, tc5].forEach((cell) =>
        cell.setAttribute("footer", "")
    );

    // Hitung total durasi (jumlah hari dari tanggal awal mulai proyek sampai tanggal berakhir proyek)
    let totalDuration = 0;

    if (tasks.length > 0) {
        // Dapatkan rentang waktu proyek
        const { start, end } = calcRange();

        // Hitung selisih hari antara tanggal awal dan akhir proyek
        const diffTime = Math.abs(end - start);
        totalDuration = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // Konversi dari milidetik ke hari
    }

    // Set text content for total row
    tc1.innerText = "Total";

    const formattedSum = new Intl.NumberFormat("id-ID").format(sum);
    tc2.innerText = formattedSum;
    tc2.id = "grid-total";

    const totalDurationText = totalDuration + " Hari";
    tc3.innerText = totalDurationText;
    tc3.id = "total-duration";

    const totalWeightText = Math.round(sumWeight) + "%"; // Bulatkan total bobot tanpa angka di belakang koma
    tc4.innerText = totalWeightText;
    tc4.id = "total-weight";

    totalRow.append(tc1, tc2, tc3, tc4, tc5);
    gridBody.appendChild(totalRow);
}

// Render header Gantt: bulan & hari/minggu
function renderTimeline() {
    const tmContainer = document.querySelector(".timeline-container"),
        tm = document.querySelector(".timeline-month"),
        td = document.querySelector(".timeline-day"),
        chartCon = document.getElementById("chart-container");

    const { start, end } = calcRange();
    tm.innerHTML = "";
    td.innerHTML = "";

    let cellWidth = (scale === "week" ? 7 * dayW : dayW) * zoom;
    if (scale === "week") cellWidth = dayW * zoom;
    // For week scale, dayW now represents week width
    else cellWidth = dayW * zoom; // For day scale

    let totalWidth = 0;
    let currentMonth = -1;
    let monthCell = null;
    let monthWidth = 0;
    let weekCounter = 1;

    // Fungsi untuk menentukan apakah teks harus tersembunyi berdasarkan lebar sel
    function shouldHideText(width, text) {
        // Perkiraan lebar teks (sekitar 8px per karakter)
        const estimatedTextWidth = text.length * 8;
        return width < estimatedTextWidth;
    }

    if (scale === "day") {
        let cur = new Date(start);
        while (cur < end) {
            const month = cur.getMonth();
            if (month !== currentMonth) {
                if (monthCell) {
                    monthCell.style.width = monthWidth + "px";
                    totalWidth += monthWidth;

                    // Tambahkan class untuk menyembunyikan teks jika sel terlalu kecil
                    const monthText = monthCell.innerText;
                    if (shouldHideText(monthWidth, monthText)) {
                        monthCell.classList.add("text-hidden");
                        monthCell.title = monthText; // Tambahkan tooltip
                    }
                }
                currentMonth = month;
                monthCell = document.createElement("div");
                monthCell.className = "cell";
                monthCell.innerText = cur.toLocaleDateString("id", {
                    month: "long",
                    year: "numeric",
                });
                tm.appendChild(monthCell);
                monthWidth = 0;
            }

            const dcell = document.createElement("div");
            dcell.className = "cell";
            dcell.style.width = cellWidth + "px";
            dcell.innerText = cur.getDate();

            // Tambahkan class untuk menyembunyikan teks jika sel terlalu kecil
            if (shouldHideText(cellWidth, dcell.innerText)) {
                dcell.classList.add("text-hidden");
                dcell.title = cur.getDate(); // Tambahkan tooltip
            }

            td.appendChild(dcell);
            monthWidth += cellWidth;

            cur.setDate(cur.getDate() + 1);
        }
        // Set width for the last month cell
        if (monthCell) {
            monthCell.style.width = monthWidth + "px";
            totalWidth += monthWidth;

            // Tambahkan class untuk menyembunyikan teks jika sel terlalu kecil
            const monthText = monthCell.innerText;
            if (shouldHideText(monthWidth, monthText)) {
                monthCell.classList.add("text-hidden");
                monthCell.title = monthText; // Tambahkan tooltip
            }
        }
    } else if (scale === "week") {
        let cur = new Date(start); // Already snapped to start of week in calcRange
        while (cur < end) {
            const month = cur.getMonth();
            if (month !== currentMonth) {
                if (monthCell) {
                    monthCell.style.width = monthWidth + "px";
                    totalWidth += monthWidth;

                    // Tambahkan class untuk menyembunyikan teks jika sel terlalu kecil
                    const monthText = monthCell.innerText;
                    if (shouldHideText(monthWidth, monthText)) {
                        monthCell.classList.add("text-hidden");
                        monthCell.title = monthText; // Tambahkan tooltip
                    }
                }
                currentMonth = month;
                monthCell = document.createElement("div");
                monthCell.className = "cell";
                monthCell.innerText = cur.toLocaleDateString("id", {
                    month: "long",
                    year: "numeric",
                });
                tm.appendChild(monthCell);
                monthWidth = 0;
            }

            const weekText = `M${weekCounter}`;
            const wcell = document.createElement("div");
            wcell.className = "cell";
            wcell.style.width = cellWidth + "px"; // Width of one week
            wcell.innerText = weekText;

            // Tambahkan class untuk menyembunyikan teks jika sel terlalu kecil
            if (shouldHideText(cellWidth, weekText)) {
                wcell.classList.add("text-hidden");
                wcell.title = weekText; // Tambahkan tooltip
            }

            td.appendChild(wcell);
            monthWidth += cellWidth;
            weekCounter++;

            cur.setDate(cur.getDate() + 7); // Move to the next week
        }
        // Set width for the last month cell
        if (monthCell) {
            monthCell.style.width = monthWidth + "px";
            totalWidth += monthWidth;

            // Tambahkan class untuk menyembunyikan teks jika sel terlalu kecil
            const monthText = monthCell.innerText;
            if (shouldHideText(monthWidth, monthText)) {
                monthCell.classList.add("text-hidden");
                monthCell.title = monthText; // Tambahkan tooltip
            }
        }
    }

    // Set width for chart-container and timeline-container
    tmContainer.style.width = totalWidth + "px";
    chartCon.style.width = totalWidth + "px";

    // Update scrollbar visibility after setting width
    updateScrollbarVisibility();
}

// Helper function untuk mendapatkan jumlah hari dalam bulan
function getDaysInMonth(month, year) {
    return new Date(year, month + 1, 0).getDate();
}

// Fungsi untuk menyesuaikan tinggi grid-lines dengan grid-body
function updateGridLinesHeight() {
    const gridLines = document.getElementById("grid-lines");
    const gridBody = document.getElementById("grid-body");

    if (gridLines && gridBody) {
        // Dapatkan tinggi dari grid-body
        const gridBodyHeight = gridBody.offsetHeight;

        // Sesuaikan tinggi grid-lines dengan grid-body
        gridLines.style.height = `${gridBodyHeight}px`;

        console.log(
            `Grid lines height updated to match grid-body: ${gridBodyHeight}px`
        );
    }
}

// Fungsi untuk menyesuaikan tinggi grid-resizer dengan grid-body
function updateGridResizerHeight() {
    const gridResizer = document.getElementById("grid-resizer");
    const gridBody = document.getElementById("grid-body");

    if (gridResizer && gridBody) {
        // Dapatkan tinggi dari grid-body
        const gridBodyHeight = gridBody.offsetHeight;

        // Sesuaikan tinggi grid-resizer dengan grid-body
        gridResizer.style.height = `${gridBodyHeight}px`;

        console.log(
            `Grid resizer height updated to match grid-body: ${gridBodyHeight}px`
        );
    }
}

// Fungsi untuk menyesuaikan tinggi resize-handle agar hanya sampai chart-row terakhir + 45px
function updateResizeHandleHeight() {
    const resizeHandle = document.getElementById("resize-handle");
    const gridBody = document.getElementById("grid-body");
    const app = document.getElementById("app");

    if (resizeHandle && gridBody && app) {
        // Dapatkan posisi dan tinggi dari grid-body
        const gridBodyRect = gridBody.getBoundingClientRect();
        const appRect = app.getBoundingClientRect();

        // Hitung tinggi maksimum untuk resize-handle (hanya sampai chart-row terakhir + 45px)
        const maxHeight = gridBodyRect.bottom - appRect.top + 45;

        // Sesuaikan tinggi maksimum resize-handle
        resizeHandle.style.maxHeight = `${maxHeight}px`;

        // Simpan nilai maxHeight untuk digunakan di fungsi lain
        window.maxAppHeight = maxHeight;

        console.log(`Resize handle max height updated to: ${maxHeight}px`);
    }
}

// Render garis grid vertikal
function renderGridLines() {
    const gridLines = document.getElementById("grid-lines");
    const chartCon = document.getElementById("chart-container");
    const scrollableCon = document.getElementById("scrollable-container");
    const tasksContainer = document.getElementById("tasks");
    const showGanttCheckbox = document.getElementById("show-gantt");
    const showScurveCheckbox = document.getElementById("show-scurve");
    const headerRow = document.querySelector(".header-row");

    gridLines.innerHTML = "";

    const { start, end } = calcRange();
    let currentPos = 0;
    let cur = new Date(start);

    // Hitung lebar total berdasarkan rentang waktu (minggu akhir proyek)
    let calculatedWidth;
    if (scale === "day") {
        const totalDays = Math.ceil((end - start) / 864e5);
        calculatedWidth = totalDays * dayW * zoom;
    } else {
        // scale === "week"
        const totalWeeks = getWeeksBetween(start, end);
        calculatedWidth = totalWeeks * dayW * zoom;
    }

    // Dapatkan lebar container
    const containerWidth = scrollableCon ? scrollableCon.clientWidth : 0;

    // Pastikan lebar grid-lines tidak lebih kecil dari lebar chart-container
    const finalWidth = Math.max(calculatedWidth, containerWidth);

    // Dapatkan tinggi grid-body untuk menyesuaikan tinggi grid-lines
    const gridBody = document.getElementById("grid-body");
    const gridBodyHeight = gridBody ? gridBody.offsetHeight : 0;

    // Set lebar dan tinggi untuk grid-lines
    gridLines.style.width = finalWidth + "px";
    gridLines.style.height = gridBodyHeight + "px"; // Sesuaikan dengan tinggi grid-body

    // Set lebar untuk chart-container dan tasks-container
    chartCon.style.width = finalWidth + "px";
    tasksContainer.style.width = finalWidth + "px";

    // Add horizontal grid lines first
    const chartRows = document.querySelectorAll("chart-row");
    const existingLines = gridLines.querySelectorAll(".horizontal-grid-line");
    existingLines.forEach((line) => line.remove());
    const gridTop = document.getElementById("grid").getBoundingClientRect().top;

    // Simpan posisi horizontal line terakhir
    let lastHorizontalLineTop = 0;

    for (let i = 1; i < chartRows.length; i++) {
        const chartRow = chartRows[i];
        const chartRowTop = chartRow.getBoundingClientRect().top;
        const relativeTop = chartRowTop - gridTop;
        const hLine = document.createElement("div");
        hLine.className = "horizontal-grid-line";
        hLine.style.top = relativeTop + "px";
        gridLines.appendChild(hLine);

        // Simpan posisi horizontal line terakhir
        if (i === chartRows.length - 1) {
            lastHorizontalLineTop = relativeTop;
        }
    }

    // Add vertical lines that only extend to the last horizontal line
    if (scale === "day") {
        while (cur < end) {
            const line = document.createElement("div");
            line.className = "line";
            line.style.left = currentPos + "px";
            line.style.height = lastHorizontalLineTop + "px"; // Set tinggi hanya sampai horizontal line terakhir
            gridLines.appendChild(line);
            currentPos += dayW * zoom;
            cur.setDate(cur.getDate() + 1);
        }
    } else if (scale === "week") {
        let weekNum = 0;
        while (cur < end) {
            const line = document.createElement("div");
            line.className = "line week-line"; // Add week-line class
            line.style.left = currentPos + "px";
            line.style.height = lastHorizontalLineTop + "px"; // Set tinggi hanya sampai horizontal line terakhir
            gridLines.appendChild(line);
            currentPos += dayW * zoom; // dayW represents week width here
            cur.setDate(cur.getDate() + 7);
            weekNum++;
        }
    }

    // Jika keduanya tidak dicentang, left-container melebar maksimal
    const showGantt = showGanttCheckbox ? showGanttCheckbox.checked : true;
    const showScurve = showScurveCheckbox ? showScurveCheckbox.checked : true;

    if (!showGantt && !showScurve) {
        // Sembunyikan grid-lines jika keduanya tidak dicentang
        gridLines.style.display = "none";

        // Update header row width untuk mengisi seluruh container
        if (headerRow) {
            headerRow.style.width = "100%";
        }
    } else {
        gridLines.style.display = "block";

        // Kembalikan header row width ke default
        if (headerRow) {
            headerRow.style.width = "";
        }
    }

    // Update grid-lines height to match grid-body
    updateGridLinesHeight();

    // Update grid-resizer height to match grid-body
    updateGridResizerHeight();

    // Update resize-handle max height to match chart-row terakhir
    updateResizeHandleHeight();
}

// Render tasks & attach drag/resize
function renderTasks() {
    const tasksEl = document.getElementById("tasks");
    const gridBody = document.getElementById("grid-body"); // Reference to the grid body
    tasksEl.innerHTML = "";
    // renderGridLines(); // Grid lines are rendered separately now or after timeline

    const { start } = calcRange();
    const chartCon = document.getElementById("chart-container");
    tasksEl.style.width = chartCon.style.width;
    tasksEl.style.height = "100%"; // Ensure task container takes full height

    const unitWidth = dayW * zoom; // Width of one unit (day or week)

    // Get all chart rows for precise measurement
    const chartRows = gridBody.querySelectorAll("chart-row");
    // Get the reference top position for relative calculation
    const gridBodyRect = gridBody.getBoundingClientRect();

    // Create task rows that perfectly match chart rows (excluding the last total row)
    for (let i = 0; i < chartRows.length - 1; i++) {
        const chartRow = chartRows[i];
        const chartRowRect = chartRow.getBoundingClientRect();

        // Calculate top position relative to the start of the grid body content
        const topRelativeToGridBody = chartRowRect.top - gridBodyRect.top;

        const row = document.createElement("div");
        row.className = "task-row";
        // Ensure height is explicitly set using the CSS variable
        row.style.height = "var(--row-height)";
        // Set the top position relative to the start of the #tasks container
        row.style.top = topRelativeToGridBody + "px";

        tasksEl.appendChild(row);
    }

    // Now add task bars to the appropriate rows
    tasks.forEach((t, i) => {
        if (
            t.weight === undefined ||
            t.weight === null ||
            isNaN(parseFloat(t.weight))
        ) {
            t.weight = 0;
        } else {
            t.weight = parseFloat(t.weight);
        }

        const row = tasksEl.children[i];
        if (!row) return;

        const bar = document.createElement("div");
        bar.className = "task-bar" + (window.viewOnly ? " task-readonly" : "");
        bar.setAttribute("data-task-id", t.id);
        // Tambahkan atribut untuk mode view-only
        if (window.viewOnly) {
            bar.dataset.disabled = "true";
        }
        bar.innerText = `${t.name} (${t.weight.toFixed(1)}%)`;

        let taskStart = new Date(t.start);
        let taskEnd = new Date(taskStart);
        taskEnd.setDate(taskStart.getDate() + t.duration);

        let offsetPixels, widthPixels;

        if (scale === "day") {
            offsetPixels = ((taskStart - start) / 864e5) * unitWidth;
            widthPixels = t.duration * unitWidth;
        } else {
            // scale === "week"
            const startOfWeek = getStartOfWeek(start);
            const taskStartWeek = getStartOfWeek(taskStart);
            const weeksFromStart = Math.round(
                (taskStartWeek - startOfWeek) / (7 * 864e5)
            );

            offsetPixels = weeksFromStart * unitWidth;

            const taskEndWeek = getStartOfWeek(taskEnd);
            const endsAtStartOfWeek =
                taskEnd.getTime() === taskEndWeek.getTime() && t.duration > 0;
            const endWeekIndex = Math.round(
                (taskEndWeek - startOfWeek) / (7 * 864e5)
            );
            let numWeeks =
                endWeekIndex - weeksFromStart + (endsAtStartOfWeek ? 0 : 1);
            if (t.duration > 0 && numWeeks <= 0) numWeeks = 1;

            widthPixels = Math.max(unitWidth, numWeeks * unitWidth);
        }

        bar.style.left = offsetPixels + "px";
        bar.style.width = widthPixels + "px";

        // Menghilangkan tombol action-btn pada task-bar
        // Hanya menyisakan resizer

        ["left", "right"].forEach((side) => {
            const r = document.createElement("div");
            r.className = `resizer ${side}`;
            bar.appendChild(r);
        });

        row.appendChild(bar);

        // Hanya tambahkan fungsi drag dan resize jika bukan mode view-only
        if (!window.viewOnly) {
        makeDrag(bar, t);
        makeResize(bar, t);
        }
    });

    // Update grid-lines height to match grid-body after rendering tasks
    updateGridLinesHeight();

    // Update grid-resizer height to match grid-body
    updateGridResizerHeight();

    // Update resize-handle max height to match chart-row terakhir
    updateResizeHandleHeight();
}

// Drag tugas
function makeDrag(bar, task) {
    let dx;
    const unitWidth = dayW * zoom;
    const scrollableCon = document.getElementById("scrollable-container");
    let isDragging = false;
    let originalStart = null;

    bar.addEventListener("mousedown", (e) => {
        if (e.target.classList.contains("resizer")) return;

        // Prevent default to avoid text selection during drag
        e.preventDefault();

        // Store original start date for comparison
        originalStart = new Date(task.start);

        dx = e.clientX - bar.getBoundingClientRect().left;

        // Add visual feedback that we're dragging
        bar.classList.add("dragging");
        isDragging = true;

        // Add ghost effect to show original position
        const ghost = document.createElement("div");
        ghost.className = "task-bar-ghost";
        ghost.style.left = bar.style.left;
        ghost.style.width = bar.style.width;
        ghost.style.height = bar.offsetHeight + "px";
        ghost.style.top = "0";
        bar.parentNode.appendChild(ghost);

        function mv(e2) {
            if (!isDragging) return;

            // Update position
            bar.style.left =
                e2.clientX -
                scrollableCon.getBoundingClientRect().left -
                dx +
                scrollableCon.scrollLeft +
                "px"; // Account for scrollLeft

            // Update task data locally for visual feedback
            updateTaskPositionLocal();
        }

        function updateTaskPositionLocal() {
            const { start } = calcRange();
            const leftPosition = parseFloat(bar.style.left);

            // Calculate new start date based on position
            let newStart;
            if (scale === "day") {
                const daysFromStart = Math.round(leftPosition / unitWidth);
                newStart = new Date(start.getTime() + daysFromStart * 864e5);
            } else {
                // scale === "week"
                const weeksFromStart = Math.round(leftPosition / unitWidth);
                newStart = new Date(
                    start.getTime() + weeksFromStart * 7 * 864e5
                );
            }

            // Update task object locally
            task.start = newStart;

            // Adjust end date based on new start and duration
            const taskEnd = new Date(task.start);
            taskEnd.setDate(taskEnd.getDate() + task.duration - 1); // -1 as duration includes start day
            task.end = taskEnd;
        }

        function up() {
            document.removeEventListener("mousemove", mv);
            document.removeEventListener("mouseup", up);

            // Remove visual feedback
            bar.classList.remove("dragging");
            isDragging = false;

            // Remove ghost
            const ghost = bar.parentNode.querySelector(".task-bar-ghost");
            if (ghost) ghost.remove();

            // Get final position and update task data
            const { start } = calcRange();
            const leftPosition = parseFloat(bar.style.left);

            // Calculate final start date
            if (scale === "day") {
                const daysFromStart = Math.round(leftPosition / unitWidth);
                task.start = new Date(start.getTime() + daysFromStart * 864e5);
            } else {
                // scale === "week"
                const weeksFromStart = Math.round(leftPosition / unitWidth);
                task.start = new Date(
                    start.getTime() + weeksFromStart * 7 * 864e5
                );
            }

            // Adjust end date based on new start and duration
            const taskEnd = new Date(task.start);
            taskEnd.setDate(taskEnd.getDate() + task.duration - 1);
            task.end = taskEnd;

            // Only update server if position actually changed
            if (originalStart.getTime() !== task.start.getTime()) {
                // Add updating indicator
                const indicator = document.createElement("div");
                indicator.className = "updating-indicator";
                indicator.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i>';
                bar.appendChild(indicator);

                // Update server with final position
                updateTimeScheduleRealtime(task, true);
            }

            // Recalculate layout to ensure it fits all tasks
            adjustLayoutToFitAllTasks();

            // Render changes
            renderTimeline();
            renderTasks();
            renderGridLines(); // Pastikan grid lines selalu tampil setelah drag
            drawSCurve();
        }

        document.addEventListener("mousemove", mv);
        document.addEventListener("mouseup", up);
    });
}

// Resize tugas
function makeResize(bar, task) {
    const l = bar.querySelector(".resizer.left"),
        r = bar.querySelector(".resizer.right");
    let ox, ow, side;
    const unitWidth = dayW * zoom;
    let isResizing = false;
    let originalStart = null;
    let originalDuration = null;

    function startResize(e, s) {
        // Prevent default to avoid text selection during resize
        e.preventDefault();

        // Store original values for comparison
        originalStart = new Date(task.start);
        originalDuration = task.duration;

        side = s;
        ox = e.clientX;
        ow = bar.offsetWidth;
        isResizing = true;

        // Add visual feedback that we're resizing
        bar.classList.add("resizing");
        bar.classList.add(`resizing-${side}`);

        // Add ghost effect to show original position and size
        const ghost = document.createElement("div");
        ghost.className = "task-bar-ghost";
        ghost.style.left = bar.style.left;
        ghost.style.width = bar.style.width;
        ghost.style.height = bar.offsetHeight + "px";
        ghost.style.top = "0";
        bar.parentNode.appendChild(ghost);

        document.addEventListener("mousemove", rsz);
        document.addEventListener("mouseup", stp);
    }

    function rsz(e) {
        if (!isResizing) return;

        let d = e.clientX - ox;
        if (side === "right") {
            bar.style.width = Math.max(unitWidth, ow + d) + "px";
        } else {
            const newWidth = ow - d;
            if (newWidth >= unitWidth) {
                bar.style.width = newWidth + "px";
                bar.style.left = bar.offsetLeft + d + "px";
            }
        }

        // Update task dimensions locally for visual feedback
        updateTaskDimensionsLocal();
    }

    function updateTaskDimensionsLocal() {
        const { start } = calcRange();
        const lp = parseFloat(bar.style.left);
        const wp = parseFloat(bar.style.width);
        let newDurationDays;
        let newStartDate;

        if (scale === "day") {
            const daysFromStart = Math.round(lp / unitWidth);
            newStartDate = new Date(start.getTime() + daysFromStart * 864e5);
            newDurationDays = Math.round(wp / unitWidth);
        } else {
            // scale === "week"
            const weeksFromStart = Math.round(lp / unitWidth);
            newStartDate = new Date(
                start.getTime() + weeksFromStart * 7 * 864e5
            );
            const numWeeks = Math.round(wp / unitWidth);
            newDurationDays = numWeeks * 7;
        }

        // Ensure duration is at least 1 day
        newDurationDays = Math.max(1, newDurationDays);

        // Update task object locally
        task.start = newStartDate;
        task.duration = newDurationDays;

        // Adjust end date based on new start and duration
        const taskEnd = new Date(task.start);
        taskEnd.setDate(taskEnd.getDate() + task.duration - 1);
        task.end = taskEnd;
    }

    function stp() {
        document.removeEventListener("mousemove", rsz);
        document.removeEventListener("mouseup", stp);

        // Remove visual feedback
        bar.classList.remove("resizing");
        bar.classList.remove(`resizing-left`);
        bar.classList.remove(`resizing-right`);
        isResizing = false;

        // Remove ghost
        const ghost = bar.parentNode.querySelector(".task-bar-ghost");
        if (ghost) ghost.remove();

        // Get final dimensions and update task data
        const { start } = calcRange();
        const lp = parseFloat(bar.style.left);
        const wp = parseFloat(bar.style.width);
        let newDurationDays;
        let newStartDate;

        if (scale === "day") {
            const daysFromStart = Math.round(lp / unitWidth);
            newStartDate = new Date(start.getTime() + daysFromStart * 864e5);
            newDurationDays = Math.round(wp / unitWidth);
        } else {
            // scale === "week"
            const weeksFromStart = Math.round(lp / unitWidth);
            newStartDate = new Date(
                start.getTime() + weeksFromStart * 7 * 864e5
            );
            const numWeeks = Math.round(wp / unitWidth);
            newDurationDays = numWeeks * 7;
        }

        // Ensure duration is at least 1 day
        newDurationDays = Math.max(1, newDurationDays);

        // Update task with final values
        task.start = newStartDate;
        task.duration = newDurationDays;

        // Adjust end date based on new start and duration
        const taskEnd = new Date(task.start);
        taskEnd.setDate(taskEnd.getDate() + task.duration - 1);
        task.end = taskEnd;

        // Only update server if something actually changed
        if (
            originalDuration !== task.duration ||
            originalStart.getTime() !== task.start.getTime()
        ) {
            // Add updating indicator
            const indicator = document.createElement("div");
            indicator.className = "updating-indicator";
            indicator.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i>';
            bar.appendChild(indicator);

            // Update server with final dimensions
            updateTimeScheduleRealtime(task, true);
        }

        // Recalculate layout to ensure it fits all tasks
        adjustLayoutToFitAllTasks();

        // Render changes
        renderGrid();
        renderTimeline();
        renderTasks();
        renderGridLines(); // Pastikan grid lines selalu tampil setelah resize
        drawSCurve();
    }

    l.addEventListener("mousedown", (e) => startResize(e, "left"));
    r.addEventListener("mousedown", (e) => startResize(e, "right"));
}

// S-curve terbalik via Canvas transform
function drawSCurve() {
    const chartCon = document.getElementById("chart-container"),
        canvas = document.getElementById("scurve"),
        tasksContainer = document.getElementById("tasks");

    if (!canvas || !tasksContainer || !chartCon) return;

    const ctx = canvas.getContext("2d");

    // Clear previous drawing
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (tasks.length === 0) return; // No tasks, nothing to draw

    const sumW = tasks.reduce((s, t) => s + (t.weight || 0), 0);
    // If total weight is zero or negative, cannot draw percentage curve
    if (sumW <= 0) return;

    // --- Setup Canvas and Coordinates ---
    const rowHeight =
        parseInt(
            getComputedStyle(document.documentElement).getPropertyValue(
                "--row-height"
            )
        ) || 30;

    // Mulai dari task-row paling bawah - (tinggi row/2) dan berakhir di task-row paling atas + (tinggi row/2)
    const halfRowHeight = rowHeight / 2;
    const totalTaskAreaHeight = tasks.length * rowHeight + rowHeight; // Height based on number of tasks + extra space

    // Set canvas dimensions to match grid-lines exactly
    const gridLines = document.getElementById("grid-lines");
    canvas.width = gridLines
        ? gridLines.offsetWidth
        : tasksContainer.scrollWidth;
    canvas.height = totalTaskAreaHeight;
    // Position canvas absolutely at the bottom of the task area
    canvas.style.position = "absolute";
    canvas.style.top = "0";
    canvas.style.left = "0";
    canvas.style.pointerEvents = "none"; // Make sure it doesn't block interactions
    canvas.style.zIndex = "25"; // Ensure it's above tasks but potentially below other overlays if needed

    // Invert Y axis: 0,0 becomes bottom-left
    ctx.setTransform(1, 0, 0, -1, 0, canvas.height);

    // --- Calculate Periods and Cumulative Weights ---
    const { start, end } = calcRange();
    let periods = [];
    let periodLabels = []; // Keep labels if needed for future tooltips/etc.
    let unitWidth = dayW * zoom; // Width of one period (day or week)

    // Array untuk menyimpan titik tengah bulan untuk menampilkan persentase
    let monthMidPoints = [];

    let currentDate = new Date(start);
    let counter = 1;

    // Fungsi untuk mendapatkan tanggal tengah bulan
    function getMidMonthDate(date) {
        const year = date.getFullYear();
        const month = date.getMonth();
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        return new Date(year, month, Math.floor(daysInMonth / 2));
    }

    // Simpan bulan saat ini untuk melacak perubahan bulan
    let currentMonth = currentDate.getMonth();
    let monthStartDate = new Date(currentDate);

    if (scale === "day") {
        while (currentDate < end) {
            periods.push(new Date(currentDate));
            periodLabels.push(
                currentDate.toLocaleDateString("id", {
                    day: "numeric",
                    month: "short",
                })
            );

            // Cek apakah bulan berubah atau sudah mencapai akhir
            const nextDate = new Date(currentDate);
            nextDate.setDate(nextDate.getDate() + 1);

            if (nextDate.getMonth() !== currentMonth || nextDate >= end) {
                // Tambahkan titik tengah bulan
                const midMonthDate = getMidMonthDate(monthStartDate);
                if (midMonthDate >= start && midMonthDate < end) {
                    monthMidPoints.push({
                        date: midMonthDate,
                        label: midMonthDate.toLocaleDateString("id", {
                            month: "short",
                            year: "numeric",
                        }),
                    });
                }

                // Perbarui bulan saat ini dan tanggal mulai bulan
                if (nextDate < end) {
                    currentMonth = nextDate.getMonth();
                    monthStartDate = new Date(nextDate);
                }
            }

            currentDate.setDate(currentDate.getDate() + 1);
        }
    } else {
        // scale === "week"
        while (currentDate < end) {
            periods.push(new Date(currentDate));
            periodLabels.push(`M${counter++}`);

            // Cek apakah bulan berubah atau sudah mencapai akhir
            const nextDate = new Date(currentDate);
            nextDate.setDate(nextDate.getDate() + 7);

            if (nextDate.getMonth() !== currentMonth || nextDate >= end) {
                // Tambahkan titik tengah bulan
                const midMonthDate = getMidMonthDate(monthStartDate);
                if (midMonthDate >= start && midMonthDate < end) {
                    monthMidPoints.push({
                        date: midMonthDate,
                        label: midMonthDate.toLocaleDateString("id", {
                            month: "short",
                            year: "numeric",
                        }),
                    });
                }

                // Perbarui bulan saat ini dan tanggal mulai bulan
                if (nextDate < end) {
                    currentMonth = nextDate.getMonth();
                    monthStartDate = new Date(nextDate);
                }
            }

            currentDate.setDate(currentDate.getDate() + 7);
        }
    }

    if (periods.length === 0) {
        ctx.setTransform(1, 0, 0, 1, 0, 0); // Reset transform
        return;
    }

    let cumulativeWeights = [];
    let cumulativeWeight = 0;

    periods.forEach((periodDate) => {
        let periodBobot = 0;
        let periodEndDate = new Date(periodDate);
        if (scale === "day") periodEndDate.setDate(periodDate.getDate() + 1);
        else periodEndDate.setDate(periodDate.getDate() + 7);

        tasks.forEach((task) => {
            const taskStart = new Date(task.start);
            const taskEnd = new Date(task.start);
            taskEnd.setDate(taskStart.getDate() + task.duration);
            const taskWeight = task.weight || 0;
            const taskDuration = Math.max(1, task.duration);

            const overlapStart = Math.max(
                taskStart.getTime(),
                periodDate.getTime()
            );
            const overlapEnd = Math.min(
                taskEnd.getTime(),
                periodEndDate.getTime()
            );
            const overlapDuration = Math.max(0, overlapEnd - overlapStart);
            const overlapDays = overlapDuration / 864e5;

            if (overlapDays > 0) {
                periodBobot += (taskWeight / taskDuration) * overlapDays;
            }
        });

        cumulativeWeight += periodBobot;
        // Ensure cumulative weight doesn't exceed the total sum due to rounding
        cumulativeWeights.push({
            cumulative: Math.min(cumulativeWeight, sumW),
        });
    });

    // --- Calculate Endpoint ---
    let lastBarEndPixels = 0;
    if (tasks.length > 0) {
        tasks.forEach((t) => {
            let taskStart = new Date(t.start);
            let taskEnd = new Date(taskStart);
            taskEnd.setDate(taskStart.getDate() + t.duration);
            let endPixel;
            if (scale === "day") {
                endPixel = ((taskEnd - start) / 864e5) * unitWidth;
            } else {
                const startOfWeek = getStartOfWeek(start);
                const taskEndWeek = getStartOfWeek(taskEnd);
                const endsAtStartOfWeek =
                    taskEnd.getTime() === taskEndWeek.getTime() &&
                    t.duration > 0;
                let endWeekIndex = Math.round(
                    (taskEndWeek - startOfWeek) / (7 * 864e5)
                );
                if (endsAtStartOfWeek) endWeekIndex--;
                endPixel = (endWeekIndex + 1) * unitWidth;
            }
            lastBarEndPixels = Math.max(lastBarEndPixels, endPixel);
        });
    }
    // Ensure the end pixel is at least one unit width, covering the first period if needed
    lastBarEndPixels = Math.max(unitWidth * periods.length, lastBarEndPixels);

    // --- Draw the S-Curve ---
    ctx.beginPath();
    // Start at transformed bottom-left with offset of halfRowHeight
    ctx.moveTo(0, halfRowHeight);
    let lastX = 0;
    let lastY = halfRowHeight; // Start with halfRowHeight offset

    cumulativeWeights.forEach((point, index) => {
        const x = (index + 1) * unitWidth;
        // Scale Y based on percentage of total weight mapped to total task area height
        // Add halfRowHeight at the bottom and subtract at the top to create the offset
        const y =
            halfRowHeight +
            (point.cumulative / sumW) *
                (totalTaskAreaHeight - halfRowHeight * 2);
        ctx.lineTo(x, y);
        lastX = x;
        lastY = y;
    });

    // Extend line horizontally if the last calculated point doesn't reach the end pixel
    if (lastX < lastBarEndPixels) {
        ctx.lineTo(lastBarEndPixels, lastY);
        lastX = lastBarEndPixels;
    }

    // Ensure the curve reaches 100% at the end by adding a final point
    // Subtract halfRowHeight to end at task-row paling atas + (tinggi row/2)
    const finalY = totalTaskAreaHeight - halfRowHeight; // 100% of the height with offset
    ctx.lineTo(lastX, finalY);
    lastY = finalY;

    // Style and draw the line
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, "rgba(59, 130, 246, 0.7)");
    gradient.addColorStop(1, "rgba(16, 185, 129, 0.7)");
    ctx.strokeStyle = gradient;
    ctx.lineWidth = 3;
    ctx.lineCap = "round"; // Smoother line ends
    ctx.lineJoin = "round"; // Smoother line joints
    ctx.stroke();

    // Fill area under the curve
    ctx.lineTo(lastX, halfRowHeight); // Line down to X-axis + halfRowHeight at the end
    ctx.lineTo(0, halfRowHeight); // Line back to origin along X-axis + halfRowHeight
    ctx.closePath();
    ctx.fillStyle = "rgba(59, 130, 246, 0.1)";
    ctx.fill();

    // Tambahkan persentase pada titik tengah bulan
    // Cek apakah dark mode aktif
    const isDarkMode = document.documentElement.classList.contains("dark");
    ctx.fillStyle = isDarkMode ? "#e5e7eb" : "#1e293b"; // Warna teks sesuai mode
    ctx.font = "bold 11px Arial";
    ctx.save();
    ctx.scale(1, -1); // Flip text back for readability
    ctx.textAlign = "center";

    // Fungsi untuk mendapatkan persentase pada tanggal tertentu
    function getPercentageAtDate(date) {
        // Cari periode terdekat sebelum tanggal
        let closestPeriodIndex = -1;
        let minDiff = Infinity;

        for (let i = 0; i < periods.length; i++) {
            const diff = date - periods[i];
            if (diff >= 0 && diff < minDiff) {
                minDiff = diff;
                closestPeriodIndex = i;
            }
        }

        // Jika tidak ada periode sebelum tanggal, gunakan persentase 0
        if (closestPeriodIndex === -1) return 0;

        // Jika ada periode setelah tanggal, interpolasi persentase
        if (closestPeriodIndex < cumulativeWeights.length - 1) {
            const period1 = periods[closestPeriodIndex];
            const period2 = periods[closestPeriodIndex + 1];
            const weight1 = cumulativeWeights[closestPeriodIndex].cumulative;
            const weight2 =
                cumulativeWeights[closestPeriodIndex + 1].cumulative;

            const totalTime = period2 - period1;
            const elapsedTime = date - period1;
            const ratio = elapsedTime / totalTime;

            return weight1 + ratio * (weight2 - weight1);
        }

        // Jika tidak ada periode setelah tanggal, gunakan persentase terakhir
        return cumulativeWeights[closestPeriodIndex].cumulative;
    }

    // Tampilkan persentase pada titik tengah bulan
    monthMidPoints.forEach((midPoint) => {
        // Hitung posisi X berdasarkan tanggal
        let xPos;
        if (scale === "day") {
            xPos = ((midPoint.date - start) / 864e5) * unitWidth;
        } else {
            const startOfWeek = getStartOfWeek(start);
            const midPointWeek = getStartOfWeek(midPoint.date);
            const weeksFromStart = Math.round(
                (midPointWeek - startOfWeek) / (7 * 864e5)
            );
            xPos = weeksFromStart * unitWidth;
        }

        // Hitung persentase pada titik tengah bulan
        const percentage = getPercentageAtDate(midPoint.date);
        const percentageOfTotal = (percentage / sumW) * 100;

        // Hitung posisi Y berdasarkan persentase
        const yPos =
            halfRowHeight +
            (percentage / sumW) * (totalTaskAreaHeight - halfRowHeight * 2);

        // Tambahkan label bulan dan persentase
        ctx.fillText(
            `${midPoint.label}: ${percentageOfTotal.toFixed(1)}%`,
            xPos,
            -yPos - 15
        );

        // Tambahkan titik pada kurva
        ctx.save();
        ctx.setTransform(1, 0, 0, 1, 0, 0);
        ctx.beginPath();
        ctx.arc(xPos, totalTaskAreaHeight - yPos, 4, 0, Math.PI * 2);
        ctx.fillStyle = "rgba(59, 130, 246, 0.9)";
        ctx.fill();
        ctx.restore();
    });

    // Cek apakah sudah ada persentase 100% dari titik tengah bulan
    let has100Percent = false;
    monthMidPoints.forEach((midPoint) => {
        const percentage = getPercentageAtDate(midPoint.date);
        const percentageOfTotal = (percentage / sumW) * 100;
        if (Math.abs(percentageOfTotal - 100) < 0.1) {
            // Toleransi 0.1% untuk pembulatan
            has100Percent = true;
        }
    });

    // Tambahkan persentase pada akhir proyek jika belum ada persentase 100%
    if (!has100Percent) {
        const finalPercentage = 100; // Always show 100% at the end

        // Dapatkan posisi akhir proyek (ujung dari ganttchart dikurangi 2 grid lines)
        let endPosition;
        const gridLineWidth = dayW * zoom; // Lebar satu grid line

        if (scale === "day") {
            // Untuk skala hari, gunakan ujung dari ganttchart dikurangi 2 grid lines
            endPosition = lastBarEndPixels - 2 * gridLineWidth;
        } else {
            // Untuk skala minggu, gunakan ujung dari ganttchart dikurangi 2 grid lines
            endPosition = lastBarEndPixels - 2 * gridLineWidth;
        }

        // Pastikan endPosition tidak negatif
        endPosition = Math.max(endPosition, lastX);

        ctx.textAlign = "right"; // Align text to the right
        // Position label at the end of the project
        ctx.fillText(finalPercentage.toFixed(1) + "%", endPosition, -lastY - 8);
    }

    ctx.restore();

    // Reset transform before exiting function
    ctx.setTransform(1, 0, 0, 1, 0, 0);
}

// Fungsi untuk update ke server database
function updateTimeScheduleRealtime(task, isFinalUpdate = false) {
    if (!task.id || !projectId) {
        console.warn("Task ID atau Project ID tidak ditemukan");
        return;
    }

    // Hitung tanggal selesai (jika belum ada)
    if (!task.end) {
        const endDate = new Date(task.start);
        endDate.setDate(endDate.getDate() + task.duration - 1);
        task.end = endDate;
    }

    // Pastikan tanggal format YYYY-MM-DD
    const startDateStr = task.start.toISOString().split("T")[0];
    const endDateStr = task.end.toISOString().split("T")[0];

    // Distribusi bobot harian masih diperlukan untuk backend
    // Gunakan window.generateBobotDistribution yang sudah dipindahkan ke scope global
    const distribusiBobot = window.generateBobotDistribution(
        startDateStr,
        task.duration,
        task.weight
    );

    // Data untuk update
    const updateData = {
        project_id: projectId,
        id: task.id,
        tanggal_mulai: startDateStr,
        tanggal_selesai: endDateStr,
        durasi: task.duration,
        bobot: task.weight,
        distribusi_bobot: distribusiBobot,
        is_realtime_update: false, // Selalu false karena kita hanya update saat drop
    };

    // Show loading indicator
    const app = document.getElementById("app");
    if (isFinalUpdate) {
        app.classList.add("opacity-50");
    }

    // Find the task element for visual feedback
    const taskElement = document.querySelector(
        `.task-bar[data-task-id="${task.id}"]`
    );

    // Cancel any existing update requests for this task
    if (
        window._updateRequestPromise &&
        window._updateRequestPromise.taskId === task.id
    ) {
        window._updateRequestPromise.cancelled = true;
    }

    // Make API request
    const currentPromise = fetch(`/time-schedule/${task.id}`, {
        method: "PUT",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
        body: JSON.stringify(updateData),
    })
        .then((response) => {
            if (currentPromise.cancelled) {
                return { success: false, message: "Request cancelled" };
            }
            return response.json();
        })
        .then((data) => {
            if (currentPromise.cancelled) {
                return;
            }

            if (data.success) {
                // Update the task in the tasks array with the server data
                if (data.data) {
                    // Find the task in the array
                    const taskIndex = tasks.findIndex((t) => t.id === task.id);
                    if (taskIndex !== -1) {
                        // Update with server values to ensure consistency
                        tasks[taskIndex].start = new Date(
                            data.data.tanggal_mulai
                        );
                        tasks[taskIndex].end = new Date(
                            data.data.tanggal_selesai
                        );
                        tasks[taskIndex].duration = data.data.durasi;

                        // Only update these if they were changed on the server
                        if (data.data.bobot !== undefined) {
                            tasks[taskIndex].weight = data.data.bobot;
                        }
                        if (data.data.progress !== undefined) {
                            tasks[taskIndex].progress = data.data.progress;
                        }
                    }
                }

                // Show success notification
                if (window.showSuccessToast) {
                    window.showSuccessToast("Jadwal berhasil diperbarui");
                } else {
                    showNotification("Jadwal berhasil diperbarui", "success");
                }

                // Add success indicator to the task element
                if (taskElement) {
                    taskElement.classList.add("update-success");
                    setTimeout(() => {
                        taskElement.classList.remove("update-success");
                    }, 1000);
                }

                // Re-render to ensure everything is in sync
                renderGrid();
                renderTimeline();
                renderTasks();
                drawSCurve();
            } else {
                console.error("Gagal mengupdate time schedule:", data.message);

                // Add error indicator on the task element
                if (taskElement) {
                    taskElement.classList.add("update-error");
                    setTimeout(() => {
                        taskElement.classList.remove("update-error");
                    }, 2000);
                }

                if (window.showErrorToast) {
                    window.showErrorToast("Gagal menyimpan perubahan");
                } else {
                    showNotification("Gagal menyimpan perubahan", "error");
                }
            }
        })
        .catch((error) => {
            if (currentPromise.cancelled) {
                return;
            }
            console.error("Error updating time schedule:", error);

            // Add error indicator on the task element
            if (taskElement) {
                taskElement.classList.add("update-error");
                setTimeout(() => {
                    taskElement.classList.remove("update-error");
                }, 2000);
            }

            if (window.showErrorToast) {
                window.showErrorToast("Gagal terhubung ke server");
            } else {
                showNotification("Gagal terhubung ke server", "error");
            }
        })
        .finally(() => {
            // Remove loading indicator
            app.classList.remove("opacity-50");

            // Remove updating indicator from task element
            const indicator = taskElement?.querySelector(".updating-indicator");
            if (indicator) {
                indicator.remove();
            }
        });

    // Store the task ID with the promise for better tracking
    currentPromise.taskId = task.id;
    window._updateRequestPromise = currentPromise;
}

// Notification function for real-time updates using the application's toast system
function showNotification(message, type = "info") {
    // Use the application's toast notification system
    if (type === "success" && window.showSuccessToast) {
        window.showSuccessToast(message);
    } else if (type === "error" && window.showErrorToast) {
        window.showErrorToast(message);
    } else if (type === "warning" && window.showWarningToast) {
        window.showWarningToast(message);
    } else if (window.showInfoToast) {
        window.showInfoToast(message);
    } else {
        // Fallback if toast functions are not available
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// This function has been replaced by updateTimeScheduleRealtime with isFinalUpdate=true parameter
// Keeping this as a stub for backward compatibility
function updateTimeSchedule(task) {
    // Forward to the new function with isFinalUpdate=true
    updateTimeScheduleRealtime(task, true);
}

// Fungsi untuk memastikan layout selalu mengikuti saat melakukan drag drop dan resize
function adjustLayoutToFitAllTasks() {
    const scrollableCon = document.getElementById("scrollable-container");
    const chartCon = document.getElementById("chart-container");

    if (!scrollableCon || !chartCon) return;

    // Recalculate range based on all tasks
    const { start, end } = calcRange();

    // Calculate total width needed
    let totalWidth;
    if (scale === "day") {
        const totalDays = Math.ceil((end - start) / 864e5);
        totalWidth = totalDays * dayW * zoom;
    } else {
        // scale === "week"
        const totalWeeks = getWeeksBetween(start, end);
        totalWidth = totalWeeks * dayW * zoom;
    }

    // Set chart container width
    chartCon.style.width = totalWidth + "px";

    // Update scrollbar visibility
    updateScrollbarVisibility();
}

// Fungsi untuk menghilangkan scrollbar pada skala hari saat layout full
// dan memperbarui status tombol zoom out
function updateScrollbarVisibility() {
    const scrollableCon = document.getElementById("scrollable-container");
    const chartCon = document.getElementById("chart-container");
    const zoomOutBtn = document.getElementById("zoom-out");
    const gridBody = document.getElementById("grid-body");

    if (!scrollableCon || !chartCon) return;

    // Dapatkan lebar konten dan container
    const containerWidth = scrollableCon.clientWidth;
    const contentWidth = chartCon.scrollWidth;

    // Cek apakah scrollbar horizontal aktif (konten lebih lebar dari container)
    const isHorizontalScrollbarActive = contentWidth > containerWidth;

    // Jika skala hari dan konten pas dengan container (tidak perlu scroll horizontal)
    if (scale === "day" && Math.abs(contentWidth - containerWidth) < 5) {
        // Hilangkan scrollbar horizontal dengan mengatur overflow ke hidden
        scrollableCon.style.overflowX = "hidden";
    } else {
        // Tampilkan scrollbar horizontal dengan mengatur overflow ke auto
        scrollableCon.style.overflowX = "auto";
    }

    // Cek apakah perlu scroll vertical
    if (gridBody) {
        const gridBodyHeight = gridBody.offsetHeight;
        const scrollableContainerHeight = scrollableCon.clientHeight;

        // Jika tinggi grid-body kurang dari atau sama dengan tinggi container (semua data sudah tampil)
        if (gridBodyHeight <= scrollableContainerHeight) {
            // Nonaktifkan scroll vertical
            scrollableCon.style.overflowY = "hidden";
        } else {
            // Aktifkan scroll vertical jika data tidak semuanya tampil
            scrollableCon.style.overflowY = "auto";
        }
    }

    // Jika tombol zoom out ada, perbarui statusnya
    if (zoomOutBtn) {
        // Jika scrollbar horizontal aktif, tombol zoom out selalu aktif
        if (isHorizontalScrollbarActive) {
            zoomOutBtn.disabled = false;
            zoomOutBtn.classList.remove("opacity-50", "cursor-not-allowed");
        }
        // Jika tidak, status tombol akan diatur oleh updateZoomButtons
    }
}

// Function to show action menu
function showActionMenu(event, task) {
    // Remove any existing action menus
    const existingMenu = document.getElementById("task-action-menu");
    if (existingMenu) {
        existingMenu.remove();
    }

    // Get the button that was clicked
    const button = event.currentTarget;
    // Get the position of the button
    const buttonRect = button.getBoundingClientRect();

    // Create action menu
    const menu = document.createElement("div");
    menu.id = "task-action-menu";
    menu.className =
        "absolute bg-white dark:bg-dark-bg-secondary shadow-lg rounded-md py-2 w-40 border border-gray-200 dark:border-dark-accent/20 z-50 text-sm";

    // Position the menu relative to the button
    // Center the menu horizontally with the button
    const menuWidth = 160; // w-40 = 10rem = 160px
    const left = buttonRect.left + buttonRect.width / 2 - menuWidth / 2;
    // Position the menu below the button
    const top = buttonRect.bottom + 5; // 5px gap

    // Ensure menu stays within viewport
    const viewportWidth = window.innerWidth;

    // Adjust horizontal position if needed
    let adjustedLeft = left;
    if (left < 10) {
        adjustedLeft = 10; // 10px from left edge
    } else if (left + menuWidth > viewportWidth - 10) {
        adjustedLeft = viewportWidth - menuWidth - 10; // 10px from right edge
    }

    // Set the position
    menu.style.left = adjustedLeft + "px";
    menu.style.top = top + "px";

    // Edit option
    const editOption = document.createElement("div");
    editOption.className =
        "w-full px-4 py-2 text-left text-blue-500 hover:bg-blue-100 hover:text-blue-700 text-sm flex items-center dark:text-dark-accent dark:hover:bg-dark-accent/20 dark:hover:text-white";
    editOption.innerHTML = '<i class="fas fa-edit mr-2"></i> Edit';
    editOption.onclick = function () {
        // Hapus menu
        menu.remove();

        // Panggil fungsi editTask dengan task yang dipilih
        editTask(task);
    };

    // Delete option
    const deleteOption = document.createElement("div");
    deleteOption.className =
        "w-full px-4 py-2 text-left hover:bg-blue-100 text-sm flex items-center hover:text-red-700 text-red-500 dark:hover:bg-dark-accent/20 dark:text-red-400 dark:hover:text-red-300";
    deleteOption.innerHTML = '<i class="fas fa-trash-alt mr-2"></i> Hapus';
    deleteOption.onclick = function () {
        // Hapus menu terlebih dahulu
        menu.remove();

        // Gunakan showCustomConfirm jika tersedia
        if (window.showCustomConfirm) {
            window.showCustomConfirm(
                "Apakah Anda yakin ingin menghapus item ini?",
                () => {
                    // Hanya hapus jika pengguna mengkonfirmasi
                    deleteTask(task);
                },
                () => {
                    // Fungsi yang dijalankan jika pengguna menekan tombol Tidak
                    console.log("Hapus dibatalkan.");
                }
            );
        } else if (window.showConfirm) {
            window.showConfirm(
                "Apakah Anda yakin ingin menghapus item ini?",
                () => {
                    // Hanya hapus jika pengguna mengkonfirmasi
                    deleteTask(task);
                }
            );
        } else {
            // Gunakan confirm bawaan browser sebagai fallback
            if (confirm("Apakah Anda yakin ingin menghapus item ini?")) {
                // Hanya hapus jika pengguna mengkonfirmasi
                deleteTask(task);
            }
        }
    };

    // Append options to menu
    menu.appendChild(editOption);
    menu.appendChild(deleteOption);

    // Add to body
    document.body.appendChild(menu);

    // Close menu when clicking elsewhere
    document.addEventListener("click", function closeMenu(e) {
        if (!menu.contains(e.target) && e.target !== event.target) {
            menu.remove();
            document.removeEventListener("click", closeMenu);
        }
    });
}

// Edit task function
function editTask(task) {
    // Set form values for edit
    const itemSelect = document.getElementById("editItemSelect");
    const itemPriceInput = document.getElementById("editItemPrice");
    const itemWeightInput = document.getElementById("editItemWeight");
    const startDateInput = document.getElementById("editStartDate");
    const durationInput = document.getElementById("editDuration");
    const editTaskIdInput = document.getElementById("editTaskId");

    // Set task ID untuk mode edit
    if (editTaskIdInput) {
        editTaskIdInput.value = task.id;
    }

    // Format date for input
    const formattedDate = task.start.toISOString().split("T")[0];

    // Set values
    if (itemSelect) {
        // Try to select the original item if possible
        const option = itemSelect.querySelector(
            `option[value="${task.itemId}"]`
        );
        if (option) {
            itemSelect.value = task.itemId;
            // Trigger change event to update dependent fields
            const event = new Event("change");
            itemSelect.dispatchEvent(event);
        }
    }

    // Set other fields directly
    if (itemPriceInput) {
        itemPriceInput.value = new Intl.NumberFormat("id-ID").format(
            task.price || 0
        );
        itemPriceInput.dataset.rawValue = task.price || 0;
    }

    if (itemWeightInput) itemWeightInput.value = task.weight;
    if (startDateInput) startDateInput.value = formattedDate;
    if (durationInput) durationInput.value = task.duration;

    // Show modal using data attributes untuk konsistensi posisi
    const modalEl = document.getElementById("editItemModal");
    if (modalEl) {
        // Cek apakah flowbite tersedia
        if (typeof flowbite !== "undefined" && flowbite.Modal) {
            // Gunakan Flowbite modal jika tersedia
            try {
                const modal = new flowbite.Modal(modalEl);
                modal.show();
            } catch (e) {
                // Fallback jika ada error
                modalEl.classList.remove("hidden");
                modalEl.style.display = "flex";
                modalEl.style.alignItems = "center";
                modalEl.style.justifyContent = "center";
            }
        } else {
            // Fallback ke DOM API jika flowbite tidak tersedia
            modalEl.classList.remove("hidden");
            modalEl.style.display = "flex";
            modalEl.style.alignItems = "center";
            modalEl.style.justifyContent = "center";
        }
    }
}

// Delete task function
function deleteTask(task) {
    if (!task.id || !projectId) {
        console.warn("Task ID atau Project ID tidak ditemukan");
        return;
    }

    // Tampilkan loading
    app.classList.add("opacity-50");

    fetch(`/time-schedule/${task.id}`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
    })
        .then((response) => response.json())
        .then((data) => {
            if (data.success) {
                tasks = tasks.filter((t) => t.id !== task.id);
                // Re-render
                renderGrid();
                renderTimeline();
                renderTasks();
                drawSCurve();
            } else {
                if (window.showErrorToast) {
                    window.showErrorToast(
                        data.message || "Gagal menghapus item"
                    );
                } else {
                    alert(data.message || "Gagal menghapus item");
                }
            }
        })
        .catch((error) => {
            console.error("Error deleting task:", error);
            if (window.showErrorToast) {
                window.showErrorToast("Terjadi kesalahan saat menghapus item");
            } else {
                alert("Terjadi kesalahan saat menghapus item");
            }
        })
        .finally(() => {
            // Hapus loading
            app.classList.remove("opacity-50");
        });
}
