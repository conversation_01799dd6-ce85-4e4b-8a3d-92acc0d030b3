<?php

namespace App\Traits;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

trait AjaxPagination
{
    /**
     * Check if the request is an AJAX request
     *
     * @param Request $request
     * @return bool
     */
    protected function isAjaxRequest(Request $request)
    {
        return $request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest';
    }

    /**
     * Handle pagination for AJAX requests
     *
     * @param Request $request
     * @param \Illuminate\Pagination\LengthAwarePaginator $paginator
     * @param string $viewName
     * @param array $data
     * @return \Illuminate\Http\Response
     */
    protected function handleAjaxPagination(Request $request, $paginator, $viewName, array $data = [])
    {
        // Add the paginator to the data
        $data = array_merge($data, [$this->getPaginatorName($paginator) => $paginator]);

        // If it's an AJAX request, return the view without the layout
        if ($this->isAjaxRequest($request)) {
            return view($viewName, $data);
        }

        // Otherwise, return the view with the layout
        return view($viewName, $data);
    }

    /**
     * Get the paginator variable name based on the paginator class
     *
     * @param \Illuminate\Pagination\LengthAwarePaginator $paginator
     * @return string
     */
    protected function getPaginatorName($paginator)
    {
        // Get the current route name
        $routeName = Route::currentRouteName();
        
        // Extract the resource name from the route name
        $resourceName = explode('.', $routeName)[0] ?? '';
        
        // Return the resource name or a default name
        return $resourceName ? $resourceName . 's' : 'items';
    }
}
