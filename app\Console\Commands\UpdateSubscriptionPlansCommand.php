<?php

namespace App\Console\Commands;

use App\Models\SubscriptionPlan;
use Illuminate\Console\Command;

class UpdateSubscriptionPlansCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-subscription-plans';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Memperbarui paket langganan dengan field akses fitur baru';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Memperbarui paket langganan dengan field akses fitur baru...');

        // Update Basic plan
        $basic = SubscriptionPlan::where('slug', 'basic')->first();
        if ($basic) {
            $basic->update([
                'can_export_excel' => true,
                'can_export_excel_formula' => false,
                'can_export_pdf' => true,
                'can_use_time_schedule' => false,
                'can_use_empirical_ahsp' => false,
            ]);
            $this->info('Paket Basic berhasil diperbarui.');
        } else {
            $this->warn('Paket Basic tidak ditemukan.');
        }

        // Update Pro plan
        $pro = SubscriptionPlan::where('slug', 'pro')->first();
        if ($pro) {
            $pro->update([
                'can_export_excel' => true,
                'can_export_excel_formula' => true,
                'can_export_pdf' => true,
                'can_use_time_schedule' => true,
                'can_use_empirical_ahsp' => false,
            ]);
            $this->info('Paket Pro berhasil diperbarui.');
        } else {
            $this->warn('Paket Pro tidak ditemukan.');
        }

        // Update Enterprise plan
        $enterprise = SubscriptionPlan::where('slug', 'enterprise')->first();
        if ($enterprise) {
            $enterprise->update([
                'can_export_excel' => true,
                'can_export_excel_formula' => true,
                'can_export_pdf' => true,
                'can_use_time_schedule' => true,
                'can_use_empirical_ahsp' => true,
            ]);
            $this->info('Paket Enterprise berhasil diperbarui.');
        } else {
            $this->warn('Paket Enterprise tidak ditemukan.');
        }

        // Update Yearly plan
        $yearly = SubscriptionPlan::where('slug', 'yearly')->first();
        if ($yearly) {
            $yearly->update([
                'can_export_excel' => true,
                'can_export_excel_formula' => true,
                'can_export_pdf' => true,
                'can_use_time_schedule' => true,
                'can_use_empirical_ahsp' => true,
            ]);
            $this->info('Paket Yearly berhasil diperbarui.');
        } else {
            $this->warn('Paket Yearly tidak ditemukan.');
        }

        $this->info('Pembaruan paket langganan selesai.');

        return Command::SUCCESS;
    }
}
