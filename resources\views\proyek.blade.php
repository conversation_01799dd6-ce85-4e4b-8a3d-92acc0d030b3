@extends('layouts.app')

@section('content')
    <!-- Button to open modal -->
    <button onclick="openModal('addProjectModal')"
        class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
        <i class="fas fa-plus-circle"></i> Tambah Proyek Baru
    </button>

    <!-- Filter Project Buttons -->
    <div class="flex flex-wrap gap-2 mt-4 mb-6">
        <button id="my-projects-btn" onclick="filterProjects('my')"
            class="bg-blue-500 text-white px-4 py-2 rounded-md transition-all duration-200 shadow-sm hover:shadow hover:bg-green-600 active">
            <i class="fas fa-user"></i> Proyek Saya
        </button>
        <button id="shared-projects-btn" onclick="filterProjects('shared')"
            class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md transition-all duration-200 shadow-sm hover:shadow hover:bg-purple-400">
            <i class="fas fa-share-alt"></i> Proyek Share
        </button>
    </div>

    <!-- Modal -->
    <div id="addProjectModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
        <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-3xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
            <!-- Header -->
            <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
                <div class="flex justify-between items-center">
                    <h2 class="text-white dark:text-dark-text text-lg font-semibold">Tambah Proyek</h2>
            <button type="button" onclick="closeModal('addProjectModal')"
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                        <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
                </div>
            </div>
            
            <!-- Content -->
            <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
            <form id="addProjectForm" method="POST" action="{{ route('projects.store') }}" enctype="multipart/form-data">
                @csrf
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Left: Upload Cover -->
                    <div class="col-span-1">
                        <div class="mb-2">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Foto Sampul</label>
                        </div>
                        <div id="imagePreview" class="group relative aspect-square duration-300 transform hover:scale-105">
                            <label class="cursor-pointer group block h-full">
                                <img id="preview"
                                        class="w-full h-full object-cover rounded-md group-hover:opacity-75 transition-opacity duration-300 border border-gray-200 dark:border-gray-700"
                                    src="https://www.dysconstructions.com/wp-content/uploads/2020/09/estimation.jpg"
                                    alt="Preview">
                                <div
                                    class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <span class="bg-black bg-opacity-50 text-white px-4 py-2 rounded-md">Klik untuk memilih
                                        foto</span>
                                </div>
                                <input type="file" id="cover_photo" name="cover_photo" accept="image/*" class="hidden"
                                    onchange="previewImage(this)">
                            </label>
                            <div
                                class="absolute bottom-2 right-2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <button type="button" onclick="deletePhoto()"
                                    class="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>
                                <button type="button" onclick="openCropModal('add')"
                                    class="p-2 bg-yellow-500 text-white rounded-full hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 flex items-center">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path
                                            d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                    </svg>
                                    <span class="ml-1 hidden sm:inline">Crop Foto</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Right: Project Details -->
                    <div class="col-span-1">
                        <div class="mb-4">
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Nama Proyek</label>
                            <input type="text" id="name" name="name"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                        </div>

                        <div class="mb-4">
                                <label for="province" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Provinsi</label>
                            <select id="province" name="province"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                                <option value="">Pilih Provinsi</option>
                            </select>
                        </div>

                        <div class="mb-4">
                                <label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Kota/Kabupaten</label>
                            <select id="city" name="city"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                                <option value="">Pilih Kota/Kabupaten</option>
                            </select>
                        </div>

                        <div class="mb-4">
                                <label for="district" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Alamat Lengkap</label>
                            <input type="text" id="district" name="district"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                placeholder="Contoh: jl./dusun/desa(kelurahan)/kecamatan" required>
                        </div>

                        <div class="mb-4">
                                <label for="project_owner" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Pemilik
                                Proyek</label>
                            <input type="text" id="project_owner" name="project_owner"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                        </div>

                        <div class="mb-4">
                                <label for="year" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tahun</label>
                            <input type="number" id="year" name="year" min="2000" max="2100"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                        </div>

                        <div class="mb-4">
                                <label for="ppn" class="block text-sm font-medium text-gray-700 dark:text-gray-300">PPN (%)</label>
                            <input type="number" id="ppn" name="ppn" min="0" max="100"
                                step="0.01"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                        </div>
                    </div>
                </div>

                    <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4 flex justify-end space-x-3">
                    <button type="button" onclick="closeModal('addProjectModal')"
                            class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white dark:text-dark-text rounded-lg transition-colors duration-150">
                        <i class="fas fa-times-circle"></i> Batal
                    </button>
                    <button type="submit" id="btnSaveProject"
                            class="px-4 py-2 bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                </div>
            </form>
            </div>
        </div>
    </div>

    <!-- Edit Project Modal -->
    <div id="editProjectModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
        <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-3xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
            <!-- Header -->
            <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
                <div class="flex justify-between items-center">
                    <h2 class="text-white dark:text-dark-text text-lg font-semibold">Edit Proyek</h2>
            <button type="button" onclick="closeModal('editProjectModal')"
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                        <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
                </div>
            </div>
            
            <!-- Content -->
            <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
            <form id="editProjectForm" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Left: Upload Cover -->
                    <div class="col-span-1">
                        <div class="mb-2">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Foto Sampul</label>
                        </div>
                        <div id="editImagePreview"
                            class="group relative aspect-square duration-300 transform hover:scale-105">
                            <label class="cursor-pointer group block h-full">
                                <img id="editPreview"
                                        class="w-full h-full object-cover rounded-md group-hover:opacity-75 transition-opacity duration-300 border border-gray-200 dark:border-gray-700"
                                    src="https://www.dysconstructions.com/wp-content/uploads/2020/09/estimation.jpg"
                                    alt="Preview">
                                <div
                                    class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <span class="bg-black bg-opacity-50 text-white px-4 py-2 rounded-md">Klik untuk memilih
                                        foto</span>
                                </div>
                                <input type="file" id="edit_cover_photo" name="cover_photo" accept="image/*"
                                    class="hidden" onchange="previewEditImage(this)">
                            </label>
                            <div
                                class="absolute bottom-2 right-2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <button type="button" onclick="deleteEditPhoto()"
                                    class="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>
                                <button type="button" onclick="openCropModal('edit')"
                                    class="p-2 bg-yellow-500 text-white rounded-full hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 flex items-center">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path
                                            d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                    </svg>
                                    <span class="ml-1 hidden sm:inline">Crop Foto</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Right: Project Details -->
                    <div class="col-span-1">
                        <div class="mb-4">
                                <label for="edit_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Nama Proyek</label>
                            <input type="text" id="edit_name" name="name"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                        </div>

                        <div class="mb-4">
                                <label for="edit_province" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Provinsi</label>
                            <select id="edit_province" name="province"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                                <option value="">Pilih Provinsi</option>
                            </select>
                        </div>

                        <div class="mb-4">
                                <label for="edit_city" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Kota/Kabupaten</label>
                            <select id="edit_city" name="city"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                                <option value="">Pilih Kota/Kabupaten</option>
                            </select>
                        </div>

                        <div class="mb-4">
                                <label for="edit_district" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Alamat
                                Lengkap</label>
                            <input type="text" id="edit_district" name="district"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                placeholder="Contoh: jl./dusun/desa(kelurahan)/kecamatan" required>
                        </div>

                        <div class="mb-4">
                                <label for="edit_project_owner" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Pemilik
                                Proyek</label>
                            <input type="text" id="edit_project_owner" name="project_owner"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                        </div>

                        <div class="mb-4">
                                <label for="edit_year" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tahun</label>
                            <input type="number" id="edit_year" name="year" min="2000" max="2100"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                        </div>

                        <div class="mb-4">
                                <label for="edit_ppn" class="block text-sm font-medium text-gray-700 dark:text-gray-300">PPN (%)</label>
                            <input type="number" id="edit_ppn" name="ppn" min="0" max="100"
                                step="0.01"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                        </div>
                    </div>
                </div>

                    <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4 flex justify-end space-x-3">
                    <button type="button" onclick="closeModal('editProjectModal')"
                            class="bg-gray-500 hover:bg-gray-600 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150">
                        <i class="fas fa-times-circle"></i> Batal</button>
                    <button type="submit" id="btnSaveEditProject"
                            class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                </div>
            </form>
            </div>
        </div>
    </div>

    <!-- Project List -->
    <div class="mt-6">
        <h2 id="my-projects-heading" class="text-xl font-bold mb-4 text-light-text dark:text-dark-text">Proyek Saya</h2>
        <div id="my-projects-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            @forelse ($ownProjects as $project)
                <div class="group relative" data-project-type="own" data-project-id="{{ $project->id }}">
                    <div
                        class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                    <!-- Form untuk membuka RAB -->
                    <form action="{{ route('rab.index') }}" method="POST" class="block h-full"
                        id="form-rab-{{ $project->id }}">
                        @csrf
                        <input type="hidden" name="project_id" value="{{ $project->id }}">

                        <!-- Area gambar yang bisa diklik -->
                        <div class="relative aspect-square cursor-pointer group"
                            onclick="document.getElementById('form-rab-{{ $project->id }}').submit()">
                            <img src="{{ $project->cover_photo ? asset('storage/' . $project->cover_photo) : 'https://www.dysconstructions.com/wp-content/uploads/2020/09/estimation.jpg' }}"
                                alt="Cover Photo"
                                class="w-full h-full object-cover rounded-md group-hover:opacity-75 transition-opacity duration-300">

                            <!-- Overlay saat hover -->
                            <div
                                class="absolute inset-0 flex flex-col items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 bg-black bg-opacity-50">
                                @if ($project->rab)
                                    <div class="text-white text-center p-4 space-y-2">
                                        <p class="text-lg font-semibold">Total RAB:</p>
                                        <p class="text-xl font-bold">Rp.
                                            {{ number_format($project->rab->dibulatkan, 2, ',', '.') }}</p>
                                        <div class="text-sm space-y-1 mt-2">
                                            <p>Jumlah Harga: Rp.
                                                {{ number_format($project->rab->jumlah_harga, 2, ',', '.') }}</p>
                                                <p>Total + PPN: Rp.
                                                    {{ number_format($project->rab->total_harga, 2, ',', '.') }}
                                            </p>
                                        </div>
                                    </div>
                                @endif
                                <span class="mt-2 bg-light-accent text-white px-4 py-2 rounded-md">Buka Proyek</span>
                            </div>
                        </div>
                    </form>

                    <!-- Tombol Aksi (Edit, Duplikat, Bagikan, Hapus) -->
                        <div
                            class="absolute top-2 right-2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <!-- Tombol Edit -->
                            <button onclick="openEditModal({{ $project->id }})"
                                class="p-2 bg-yellow-500 text-white rounded-full hover:bg-yellow-600 transition-colors duration-300">
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path
                                        d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                </svg>
                            </button>

                            <!-- Tombol Duplikat -->
                            <form action="{{ route('projects.duplicate', $project) }}" method="POST" class="inline">
                                @csrf
                                <button type="submit"
                                    class="p-2 bg-light-accent text-white rounded-full hover:bg-light-accent/80 transition-colors duration-300">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z" />
                                        <path d="M5 3a2 2 0 00-2 2v6a2 2 0 002 2V5h8a2 2 0 00-2-2H5z" />
                                    </svg>
                                </button>
                            </form>

                            <!-- Tombol Bagikan -->
                            <button onclick="loadShareModal({{ $project->id }})"
                                class="p-2 bg-purple-500 text-white rounded-full hover:bg-purple-600 transition-colors duration-300 inline-flex items-center justify-center">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path
                                            d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
                                    </svg>
                                </button>

                            <!-- Tombol Hapus -->
                                <button
                                    onclick="confirmDeleteProject('{{ route('projects.destroy', $project) }}', '{{ $project->name }}')"
                                    class="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors duration-300">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>
                        </div>

                    <!-- Informasi Proyek -->
                    <div class="p-4">
                            <h3
                                class="text-lg font-bold group-hover:text-blue-600 transition-colors duration-300 truncate">
                                {{ $project->name }}</h3>
                        <p class="text-sm text-gray-600 truncate">{{ $project->province }}, {{ $project->city }},
                            {{ $project->district }}</p>
                        <p class="text-sm text-gray-600 truncate">Tahun: {{ $project->year }}</p>
                        <p class="text-sm text-gray-600 truncate">PPN: <span
                                class="project-ppn">{{ $project->ppn }}</span>%</p>
                    </div>
                </div>
            </div>
        @empty
            <p class="text-gray-600 col-span-full text-center py-8">Tidak ada proyek yang tersedia.</p>
        @endforelse
    </div>

        @if (count($sharedProjects) > 0)
            <h2 id="shared-projects-heading" class="text-xl font-bold mb-4 text-light-text dark:text-dark-text">Proyek yang Dibagikan dengan Saya</h2>
            <div id="shared-projects-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach ($sharedProjects as $project)
                    <div class="group relative" data-project-type="shared" data-project-id="{{ $project->id }}">
                        <div
                            class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-300 transform hover:scale-105 hover:shadow-lg border-2 border-purple-300 dark:border-purple-700">
                            <!-- Badge untuk menunjukkan proyek yang dibagikan -->
                            <div
                                class="absolute top-2 left-2 bg-purple-500 text-white text-xs px-2 py-1 rounded-full z-10">
                                <i class="fas fa-share-alt mr-1"></i>
                                {{ $project->share_role === 'editor' ? 'Editor' : 'Viewer' }}
                            </div>

                            <!-- Form untuk membuka RAB -->
                            <form action="{{ route('rab.index') }}" method="POST" class="block h-full"
                                id="form-rab-shared-{{ $project->id }}">
                                @csrf
                                <input type="hidden" name="project_id" value="{{ $project->id }}">

                                <!-- Area gambar yang bisa diklik -->
                                <div class="relative aspect-square cursor-pointer group"
                                    onclick="document.getElementById('form-rab-shared-{{ $project->id }}').submit()">
                                    <img src="{{ $project->cover_photo ? asset('storage/' . $project->cover_photo) : 'https://www.dysconstructions.com/wp-content/uploads/2020/09/estimation.jpg' }}"
                                        alt="Cover Photo"
                                        class="w-full h-full object-cover rounded-md group-hover:opacity-75 transition-opacity duration-300">

                                    <!-- Overlay saat hover -->
                                    <div
                                        class="absolute inset-0 flex flex-col items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 bg-black bg-opacity-50">
                                        @if (property_exists($project, 'rab_id') && $project->rab_id)
                                            <div class="text-white text-center p-4 space-y-2">
                                                <p class="text-lg font-semibold">Total RAB:</p>
                                                <p class="text-xl font-bold">Rp.
                                                    {{ isset($project->rab) && $project->rab->dibulatkan ? number_format($project->rab->dibulatkan, 2, ',', '.') : '0,00' }}</p>
                                                <div class="text-sm space-y-1 mt-2">
                                                    <p>Jumlah Harga: Rp.
                                                        {{ isset($project->rab) && $project->rab->jumlah_harga ? number_format($project->rab->jumlah_harga, 2, ',', '.') : '0,00' }}</p>
                                                    <p>Total + PPN: Rp.
                                                        {{ isset($project->rab) && $project->rab->total_harga ? number_format($project->rab->total_harga, 2, ',', '.') : '0,00' }}
                                                    </p>
                                                </div>
                                            </div>
                                        @endif
                                        <span class="mt-2 bg-light-accent text-white px-4 py-2 rounded-md">Buka
                                            Proyek</span>
                                    </div>
                                </div>
                            </form>

                            <!-- Tombol Aksi (Hapus Akses) -->
                            <div class="absolute top-2 right-2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <button 
                                    onclick="confirmRemoveAccess({{ $project->id }}, '{{ $project->name }}')"
                                    class="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors duration-300">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>

                            <!-- Informasi Proyek -->
                            <div class="p-4">
                                <h3
                                    class="text-lg font-bold group-hover:text-blue-600 transition-colors duration-300 truncate">
                                    {{ $project->name }}</h3>
                                <p class="text-sm text-gray-600 truncate">{{ $project->province }}, {{ $project->city }},
                                    {{ $project->district }}</p>
                                <p class="text-sm text-gray-600 truncate">Tahun: {{ $project->year }}</p>
                                <p class="text-sm text-gray-600 truncate">PPN: <span
                                        class="project-ppn">{{ $project->ppn }}</span>%</p>
                                <p class="text-sm text-gray-600 truncate mt-1">Pemilik: {{ $project->user_name }}</p>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @endif

    <!-- Crop Modal -->
    <div id="cropModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
        <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-3xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
            <!-- Header -->
            <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
                <div class="flex justify-between items-center">
                    <h2 class="text-white dark:text-dark-text text-lg font-semibold">Crop Foto</h2>
            <button onclick="closeCropModal()"
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                        <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
                </div>
            </div>
            <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
            <div class="aspect-square w-full">
                <img id="cropImage" class="w-full h-full object-contain" src="" alt="Crop Preview">
            </div>
            <div class="flex justify-end mt-4">
                <button type="button" onclick="closeCropModal()"
                    class="bg-gray-500 text-white px-4 py-2 rounded mr-2 hover:bg-gray-600">Batal</button>
                <button type="button" onclick="applyCrop()"
                    class="bg-light-accent text-white px-4 py-2 rounded hover:bg-light-accent/80 transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">Terapkan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Cropper.js CSS and JS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js"></script>

    <script>
        function openModal(id) {
            document.getElementById(id).classList.remove('hidden');
        }

        function closeModal(id) {
            document.getElementById(id).classList.add('hidden');
        }

        function deletePhoto() {
            const fileInput = document.getElementById('cover_photo');
            const preview = document.getElementById('preview');

            // Reset file input
            fileInput.value = '';

            // Set default image
            preview.src = 'https://www.dysconstructions.com/wp-content/uploads/2020/09/estimation.jpg';
        }

        function deleteEditPhoto() {
            const fileInput = document.getElementById('edit_cover_photo');
            const preview = document.getElementById('editPreview');

            // Reset file input
            fileInput.value = '';

            // Set default image
            preview.src = 'https://www.dysconstructions.com/wp-content/uploads/2020/09/estimation.jpg';

            // Add hidden input to indicate photo deletion
            let deleteInput = document.getElementById('delete_photo');
            if (!deleteInput) {
                deleteInput = document.createElement('input');
                deleteInput.type = 'hidden';
                deleteInput.name = 'delete_photo';
                deleteInput.id = 'delete_photo';
                deleteInput.value = '1';
                document.getElementById('editProjectForm').appendChild(deleteInput);
            } else {
                deleteInput.value = '1';
            }
        }

        function previewImage(input) {
            const preview = document.getElementById('preview');

            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    preview.src = e.target.result;
                }

                reader.readAsDataURL(input.files[0]);
            }
        }

        function previewEditImage(input) {
            const preview = document.getElementById('editPreview');

            // Remove delete photo input if exists
            const deleteInput = document.getElementById('delete_photo');
            if (deleteInput) {
                deleteInput.value = '0';
            }

            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    preview.src = e.target.result;
                }

                reader.readAsDataURL(input.files[0]);
            }
        }

        function openEditModal(projectId) {
            // Fetch project data
            fetch(`/projects/${projectId}`)
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.message || 'Gagal memuat data proyek');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.message || 'Gagal memuat data proyek');
                    }

                    const project = data.data;

                    // Set form action
                    document.getElementById('editProjectForm').action = `/projects/${projectId}`;

                    // Fill form fields
                    document.getElementById('edit_name').value = project.name;
                    document.getElementById('edit_province').value = project.province;
                    document.getElementById('edit_city').value = project.city;
                    document.getElementById('edit_district').value = project.district;
                    document.getElementById('edit_project_owner').value = project.project_owner;
                    document.getElementById('edit_year').value = project.year;
                    document.getElementById('edit_ppn').value = project.ppn;

                    // Set cover photo preview
                    const preview = document.getElementById('editPreview');
                    if (project.cover_photo) {
                        preview.src = project.cover_photo;
                    } else {
                        preview.src = 'https://www.dysconstructions.com/wp-content/uploads/2020/09/estimation.jpg';
                    }

                    // Load cities for the selected province
                    if (project.province) {
                        fetch(`/data/wilayah/cities/${project.province}.json`)
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error('Gagal memuat data kota');
                                }
                                return response.json();
                            })
                            .then(cities => {
                                const citySelect = document.getElementById('edit_city');
                                citySelect.innerHTML = '<option value="">Pilih Kota/Kabupaten</option>';
                                cities.forEach(city => {
                                    const option = document.createElement('option');
                                    option.value = city.name;
                                    option.textContent = city.name;
                                    citySelect.appendChild(option);
                                });
                                citySelect.value = project.city;
                            })
                            .catch(error => {
                                console.error('Error loading cities:', error);
                                alert('Gagal memuat data kota. Silakan coba lagi.');
                            });
                    }

                    // Set alamat lengkap value
                    document.getElementById('edit_district').value = project.district;

                    // Show modal
                    document.getElementById('editProjectModal').classList.remove('hidden');
                    document.getElementById('editProjectForm').setAttribute('data-project-id', projectId);
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert(error.message || 'Gagal memuat data proyek. Silakan coba lagi.');
                });
        }

        // Fungsi untuk konfirmasi hapus proyek
        function confirmDeleteProject(deleteUrl, projectName) {
            showCustomConfirm(
                `Apakah Anda yakin ingin menghapus proyek "${projectName}"?`,
                () => {
                    // Buat form untuk submit
                    const form = document.createElement('form');
                    form.action = deleteUrl;
                    form.method = 'POST';
                    form.innerHTML = `
                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="_method" value="DELETE">
                `;
                    document.body.appendChild(form);
                    form.submit();
                },
                () => {
                    console.log('Hapus proyek dibatalkan.');
                }
            );
        }

        // Load provinces data
        fetch('/data/wilayah/provinces.json')
            .then(response => response.json())
            .then(provinces => {
                const provinceSelect = document.getElementById('province');
                provinces.forEach(province => {
                    const option = document.createElement('option');
                    option.value = province.name;
                    option.textContent = province.name;
                    provinceSelect.appendChild(option);
                });
            });

        // Load cities when province is selected
        document.getElementById('province').addEventListener('change', function() {
            const province = this.value;
            const citySelect = document.getElementById('city');
            citySelect.innerHTML = '<option value="">Pilih Kota/Kabupaten</option>';

            if (province) {
                fetch(`/data/wilayah/cities/${province}.json`)
                    .then(response => response.json())
                    .then(cities => {
                        cities.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city.name;
                            option.textContent = city.name;
                            citySelect.appendChild(option);
                        });
                    });
            }
        });

        // Clear alamat lengkap when city is selected
        document.getElementById('city').addEventListener('change', function() {
            const districtInput = document.getElementById('district');
            districtInput.value = '';
        });

        // Load provinces data for edit modal
        fetch('/data/wilayah/provinces.json')
            .then(response => response.json())
            .then(provinces => {
                const provinceSelect = document.getElementById('edit_province');
                provinces.forEach(province => {
                    const option = document.createElement('option');
                    option.value = province.name;
                    option.textContent = province.name;
                    provinceSelect.appendChild(option);
                });
            });

        // Load cities when province is selected in edit modal
        document.getElementById('edit_province').addEventListener('change', function() {
            const province = this.value;
            const citySelect = document.getElementById('edit_city');
            citySelect.innerHTML = '<option value="">Pilih Kota/Kabupaten</option>';

            if (province) {
                fetch(`/data/wilayah/cities/${province}.json`)
                    .then(response => response.json())
                    .then(cities => {
                        cities.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city.name;
                            option.textContent = city.name;
                            citySelect.appendChild(option);
                        });
                    });
            }
        });

        // Clear alamat lengkap when city is selected in edit modal
        document.getElementById('edit_city').addEventListener('change', function() {
            const districtInput = document.getElementById('edit_district');
            districtInput.value = '';
        });

        function openCropModal(type) {
            const modal = document.getElementById('cropModal');
            const cropImage = document.getElementById('cropImage');
            const preview = type === 'add' ? document.getElementById('preview') : document.getElementById('editPreview');

            cropImage.src = preview.src;

            // Initialize cropper
            if (window.cropper) {
                window.cropper.destroy();
            }

            window.cropper = new Cropper(document.getElementById('cropImage'), {
                aspectRatio: 1,
                viewMode: 1,
                autoCropArea: 1,
                responsive: true,
                restore: false,
                guides: true,
                center: true,
                highlight: true,
                cropBoxMovable: true,
                cropBoxResizable: true,
                toggleDragModeOnDblclick: false,
            });

            modal.classList.remove('hidden');
        }

        function closeCropModal() {
            document.getElementById('cropModal').classList.add('hidden');
            if (window.cropper) {
                window.cropper.destroy();
            }
        }

        function applyCrop() {
            if (window.cropper) {
                const canvas = window.cropper.getCroppedCanvas({
                    width: 800,
                    height: 800,
                });

                // Convert canvas to blob
                canvas.toBlob((blob) => {
                    // Create a new file from the blob
                    const file = new File([blob], 'cropped-image.jpg', {
                        type: 'image/jpeg'
                    });

                    // Create a new DataTransfer object
                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);

                    // Update the file input
                    const fileInput = document.getElementById('cover_photo');
                    const editFileInput = document.getElementById('edit_cover_photo');

                    if (fileInput) {
                        fileInput.files = dataTransfer.files;
                        const preview = document.getElementById('preview');
                        preview.src = URL.createObjectURL(file);
                    }

                    if (editFileInput) {
                        editFileInput.files = dataTransfer.files;
                        const editPreview = document.getElementById('editPreview');
                        editPreview.src = URL.createObjectURL(file);
                    }

                    closeCropModal();
                }, 'image/jpeg', 0.95);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Cek apakah ada pesan error batas proyek
            @if(session('project_limit_error'))
                if (typeof window.showInfoToast === 'function') {
                    window.showInfoToast("{{ session('project_limit_error') }}");
                }
            @endif

            // Jalankan filter default atau simpan pilihan filter
            const savedFilter = localStorage.getItem('projectFilter') || 'my';
            filterProjects(savedFilter);

            // Form tambah proyek
            const addProjectForm = document.getElementById('addProjectForm');
            if (addProjectForm) {
                addProjectForm.addEventListener('submit', function(event) {
                    event.preventDefault();
                    
                    const btnSaveProject = document.getElementById('btnSaveProject');
                    if (btnSaveProject) {
                        if (typeof window.setButtonLoading === 'function') {
                            window.setButtonLoading(btnSaveProject, true, 'Simpan', 'fa-save');
                        } else {
                            btnSaveProject.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
                            btnSaveProject.disabled = true;
                        }
                    }

            const formData = new FormData(this);

            // Sanitize formData to ensure valid JSON
            const sanitizedFormData = new FormData();
            for (const [key, value] of formData.entries()) {
                if (typeof value === 'string') {
                    // Replace any non-printable characters and other problematic characters
                    sanitizedFormData.append(key, value.replace(
                        /[\x00-\x1F\x7F-\x9F]/g, ''));
                } else {
                    sanitizedFormData.append(key, value);
                }
            }

            fetch(this.action, {
                    method: 'POST',
                    body: sanitizedFormData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                                // Jika perlu reload halaman (batas maksimal proyek tercapai)
                                if (data.reload) {
                                    // Hentikan animasi loading button
                                    if (btnSaveProject) {
                                        if (typeof window.setButtonLoading === 'function') {
                                            window.setButtonLoading(btnSaveProject, false, 'Simpan', 'fa-save');
                        } else {
                                            btnSaveProject.innerHTML = '<i class="fas fa-save"></i> Simpan';
                                            btnSaveProject.disabled = false;
                                        }
                                    }
                                    
                                    // Tunggu sebentar hingga animasi selesai, lalu tutup modal dan reload halaman
                                    setTimeout(function() {
                                        document.getElementById('addProjectModal').classList.add('hidden');
                                        
                                        // Tunggu sebentar lagi setelah modal tertutup, lalu reload halaman
                                        setTimeout(function() {
                                            window.location.reload();
                                        }, 200);
                                    }, 300);
                                    
                                    return;
                                }
                                
                        // Handle validation errors
                        if (data.errors) {
                            let errorMessage = 'Validasi gagal:\n';
                            Object.keys(data.errors).forEach(key => {
                                errorMessage += `- ${data.errors[key][0]}\n`;
                            });
                            alert(errorMessage);
                        } else {
                                    alert(data.message || 'Terjadi kesalahan saat menyimpan proyek');
                                }
                                
                                // Reset button state
                                if (btnSaveProject) {
                                    if (typeof window.setButtonLoading === 'function') {
                                        window.setButtonLoading(btnSaveProject, false, 'Simpan', 'fa-save');
                                    } else {
                                        btnSaveProject.innerHTML = '<i class="fas fa-save"></i> Simpan';
                                        btnSaveProject.disabled = false;
                                    }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                            
                            // Reset button state
                            const btnSaveProject = document.getElementById('btnSaveProject');
                            if (btnSaveProject) {
                                if (typeof window.setButtonLoading === 'function') {
                                    window.setButtonLoading(btnSaveProject, false, 'Simpan', 'fa-save');
                                } else {
                                    btnSaveProject.innerHTML = '<i class="fas fa-save"></i> Simpan';
                                    btnSaveProject.disabled = false;
                                }
                            }
                            
                    if (error.errors) {
                        let errorMessage = 'Validasi gagal:\n';
                        Object.keys(error.errors).forEach(key => {
                            errorMessage += `- ${error.errors[key][0]}\n`;
                        });
                        alert(errorMessage);
                    } else {
                                alert(error.message || 'Terjadi kesalahan saat menyimpan proyek');
                            }
                        });
                    });
                }

                // Form edit proyek
                const editProjectForm = document.getElementById('editProjectForm');
                if (editProjectForm) {
                    editProjectForm.addEventListener('submit', function(event) {
                        const submitBtn = document.getElementById('btnSaveEditProject');
                        if (submitBtn) {
                            window.setButtonLoading(submitBtn, true, 'Simpan', 'fa-save');
                        }
                    });
                }
        });

        // Function to update PPN display in project list
        function updateProjectPPN(projectId, newPPN) {
            const projectElement = document.querySelector(`[data-project-id="${projectId}"] .project-ppn`);
            if (projectElement) {
                projectElement.textContent = newPPN;
            }
        }

        // Add event listener for PPN changes in edit modal
        document.getElementById('edit_ppn').addEventListener('change', function(e) {
            const projectId = document.getElementById('editProjectForm').getAttribute('data-project-id');
            const newPPN = e.target.value;

            // Sanitize newPPN to ensure valid JSON
            const sanitizedPPN = typeof newPPN === 'string' ?
                newPPN.replace(/[\x00-\x1F\x7F-\x9F]/g, '') :
                newPPN;

            fetch(`/projects/${projectId}/update-ppn`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        ppn: sanitizedPPN
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateProjectPPN(projectId, data.ppn);
                    } else {
                        alert(data.message || 'Gagal memperbarui PPN');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Terjadi kesalahan saat memperbarui PPN');
                });
        });

            // Fungsi untuk filter proyek
            function filterProjects(type) {
                // Simpan pilihan filter ke localStorage
                localStorage.setItem('projectFilter', type);

                // Update button styles
                document.getElementById('my-projects-btn').classList.remove('bg-blue-500', 'text-white');
                document.getElementById('my-projects-btn').classList.add('bg-gray-200', 'text-gray-700');
                document.getElementById('shared-projects-btn').classList.remove('bg-blue-500', 'text-white');
                document.getElementById('shared-projects-btn').classList.add('bg-gray-200', 'text-gray-700');
                
                // Set active button
                if (type === 'my') {
                    document.getElementById('my-projects-btn').classList.remove('bg-gray-200', 'text-gray-700');
                    document.getElementById('my-projects-btn').classList.add('bg-blue-500', 'text-white');
                } else if (type === 'shared') {
                    document.getElementById('shared-projects-btn').classList.remove('bg-gray-200', 'text-gray-700');
                    document.getElementById('shared-projects-btn').classList.add('bg-blue-500', 'text-white');
                }
                
                // Get all project elements
                const allProjects = document.querySelectorAll('[data-project-type]');
                const myProjects = document.querySelectorAll('[data-project-type="own"]');
                const sharedProjects = document.querySelectorAll('[data-project-type="shared"]');
                
                // Get heading elements
                const myProjectsHeading = document.getElementById('my-projects-heading');
                const sharedProjectsHeading = document.getElementById('shared-projects-heading');
                
                // Get grid containers
                const myProjectsGrid = document.getElementById('my-projects-grid');
                const sharedProjectsGrid = document.getElementById('shared-projects-grid');
                
                // Show/hide projects based on filter
                if (type === 'my') {
                    // Show my projects, hide shared projects
                    myProjects.forEach(project => project.style.display = 'block');
                    sharedProjects.forEach(project => project.style.display = 'none');
                    
                    // Show my projects heading and grid, hide shared projects heading and grid
                    if (myProjectsHeading) myProjectsHeading.style.display = 'block';
                    if (myProjectsGrid) myProjectsGrid.style.display = 'grid';
                    if (sharedProjectsHeading) sharedProjectsHeading.style.display = 'none';
                    if (sharedProjectsGrid) sharedProjectsGrid.style.display = 'none';
                } else if (type === 'shared') {
                    // Show shared projects, hide my projects
                    myProjects.forEach(project => project.style.display = 'none');
                    sharedProjects.forEach(project => project.style.display = 'block');
                    
                    // Hide my projects heading and grid, show shared projects heading and grid
                    if (myProjectsHeading) myProjectsHeading.style.display = 'none';
                    if (myProjectsGrid) myProjectsGrid.style.display = 'none';
                    if (sharedProjectsHeading) sharedProjectsHeading.style.display = 'block';
                    if (sharedProjectsGrid) sharedProjectsGrid.style.display = 'grid';
                }
            }

        // Fungsi untuk membuka modal bagikan proyek
        function loadShareModal(projectId) {
            // Tampilkan modal
            openModal('shareProjectModal');
            
            // Ambil konten modal
            const modalContent = document.getElementById('shareProjectModalContent');
            
            // Tampilkan loading
            modalContent.innerHTML = `
                <div class="text-center py-10">
                    <i class="fas fa-spinner fa-spin text-4xl text-blue-500"></i>
                    <p class="mt-4 text-gray-600">Memuat data...</p>
                </div>
            `;
            
            // Ambil data shares dari server
            fetch(`/projects/${projectId}/share`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
                .then(data => {
                    if (data.success) {
                    // Render modal content dengan data
                    renderShareModalContent(modalContent, data.project, data.shares, projectId, data.canShareMore);
                    } else {
                    modalContent.innerHTML = `
                        <div class="text-center py-10">
                            <i class="fas fa-exclamation-circle text-4xl text-red-500"></i>
                            <p class="mt-4 text-gray-600">${data.message || 'Terjadi kesalahan saat memuat data.'}</p>
                        </div>
                    `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                modalContent.innerHTML = `
                    <div class="text-center py-10">
                        <i class="fas fa-exclamation-circle text-4xl text-red-500"></i>
                        <p class="mt-4 text-gray-600">Terjadi kesalahan saat memuat data.</p>
                    </div>
                `;
            });
        }
        
        // Fungsi untuk merender konten modal bagikan
        function renderShareModalContent(container, project, shares, projectId, canShareMore) {
            const remainingShares = project.remainingShares || 0;
            const hasReachedLimit = !canShareMore || remainingShares <= 0;
            
            container.innerHTML = `
                <div class="border-b pb-4 mb-4">
                    <h3 class="text-lg font-semibold mb-2">Berbagi Proyek: ${project.name}</h3>
                    <p class="text-sm text-gray-600">Bagikan proyek dengan pengguna lain. Mereka dapat melihat atau mengedit proyek sesuai dengan izin yang Anda berikan.</p>
                    ${hasReachedLimit ? 
                    `<div class="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-md">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle text-yellow-500 mr-2"></i>
                            <p class="text-yellow-700 dark:text-yellow-300 text-sm">
                                Anda telah mencapai batas maksimum berbagi untuk proyek ini. Hapus pengguna yang tidak diperlukan atau upgrade paket langganan Anda untuk menambah lebih banyak pengguna.
                            </p>
                            </div>
                    </div>` : 
                    `<p class="mt-2 text-sm text-blue-600 dark:text-blue-400">
                        <i class="fas fa-info-circle mr-1"></i> Anda dapat berbagi dengan <span class="font-semibold">${remainingShares}</span> pengguna lagi.
                    </p>`}
                            </div>
                
                ${!hasReachedLimit ? `
                <div class="mb-6">
                    <form id="shareProjectForm" method="POST" action="/projects/${projectId}/share">
                        @csrf
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="col-span-2">
                                <label for="user_id" class="block text-sm font-medium text-gray-700 mb-1">Pilih Pengguna</label>
                                <select id="user_id" name="user_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" required>
                                    <option value="">-- Pilih Pengguna --</option>
                                    ${project.users ? project.users.map(user => `
                                        <option value="${user.id}" ${user.maxProjectReached ? 'disabled class="text-red-500"' : ''}>${user.name} (${user.email}) ${user.maxProjectReached ? '- Sudah mencapai batas maksimum proyek' : ''}</option>
                                    `).join('') : ''}
                                </select>
                                <div id="maxProjectWarning" class="hidden mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-md">
                                    <p class="text-yellow-700 dark:text-yellow-300 text-xs">
                                        <i class="fas fa-exclamation-circle mr-1"></i> Pengguna ini sudah mencapai batas maksimum proyek. Mereka tidak dapat menerima proyek baru.
                                    </p>
                                </div>
                        </div>
                            <div>
                                <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Peran</label>
                                <select id="role" name="role" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    <option value="viewer">Viewer (hanya lihat)</option>
                                    <option value="editor">Editor (lihat & edit)</option>
                            </select>
                            </div>
                            <div class="md:col-span-3">
                                <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-share-alt mr-1"></i> Bagikan Proyek
                            </button>
                        </div>
                        </div>
                    </form>
                </div>
                ` : ''}
                
                <div>
                    <h4 class="font-semibold mb-3">Daftar Pengguna yang Memiliki Akses</h4>
                    ${renderSharesList(shares, projectId)}
                            </div>
                        `;

            // Tambahkan event listener untuk form berbagi jika form ada
            if (!hasReachedLimit) {
                const shareForm = document.getElementById('shareProjectForm');
                shareForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const userId = document.getElementById('user_id').value;
                    const selectedOption = document.getElementById('user_id').options[document.getElementById('user_id').selectedIndex];
                    
                    // Cek apakah user sudah mencapai batas maksimum proyek
                    if (selectedOption.disabled || selectedOption.classList.contains('text-red-500')) {
                        document.getElementById('maxProjectWarning').classList.remove('hidden');
                        return; // Hentikan pengiriman form
                    }
                    
                    // Kirim form dengan fetch
                    fetch(this.action, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: JSON.stringify({
                            user_id: userId,
                            role: document.getElementById('role').value
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Refresh modal content
                            loadShareModal(projectId);
                        } else {
                            const errorMessage = data.message || 'Terjadi kesalahan saat berbagi proyek.';
                            
                            // Cek apakah error karena batas maksimum proyek
                            if (errorMessage.includes('maksimum proyek')) {
                                document.getElementById('maxProjectWarning').classList.remove('hidden');
                                document.getElementById('maxProjectWarning').querySelector('p').textContent = errorMessage;
                        } else {
                                alert(errorMessage);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                        alert('Terjadi kesalahan saat berbagi proyek.');
                    });
                });
                
                // Tambahkan event listener untuk reset pesan peringatan saat memilih user baru
                document.getElementById('user_id').addEventListener('change', function() {
                    document.getElementById('maxProjectWarning').classList.add('hidden');
                });
            }
            
            // Setup event listeners untuk tombol hapus dan switch role
            setupDeleteShareButtons();
        }
        
        // Fungsi untuk merender daftar shares
        function renderSharesList(shares, projectId) {
            if (shares.length === 0) {
                return `<p class="text-gray-500 italic">Belum ada pengguna yang memiliki akses ke proyek ini.</p>`;
            }
            
            return `
                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-100 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Pengguna</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Peran</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Aksi</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            ${shares.map(share => `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                                <span class="text-gray-700 dark:text-gray-300 font-medium">${share.user.name.charAt(0).toUpperCase()}</span>
                        </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${share.user.name}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${share.user.email}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center space-x-2">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${share.role === 'editor' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'}">
                                                ${share.role === 'editor' ? 'Editor' : 'Viewer'}
                                            </span>
                            <button
                                                class="switch-role-btn text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 ml-2" 
                                                data-share-id="${share.id}" 
                                                data-current-role="${share.role}"
                                                data-project-id="${projectId}">
                                                <i class="fas fa-exchange-alt"></i> Ubah
                            </button>
                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button 
                                            class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 delete-share-btn" 
                                            data-share-id="${share.id}" 
                                            data-user-name="${share.user.name}"
                                            data-project-id="${projectId}">
                                            <i class="fas fa-trash"></i> Hapus Akses
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                    </div>
                `;
        }
        
        // Fungsi untuk setup event listeners tombol hapus
        function setupDeleteShareButtons() {
            // Setup event untuk tombol hapus
            document.querySelectorAll('.delete-share-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const shareId = this.getAttribute('data-share-id');
                    const userName = this.getAttribute('data-user-name');
                    const projectId = this.getAttribute('data-project-id');
                    
                    // Tampilkan modal konfirmasi hapus
                    document.getElementById('shareUserNameToDelete').textContent = userName;
                    document.getElementById('deleteShareForm').action = `/projects/${projectId}/share/${shareId}`;
                    document.getElementById('deleteShareConfirmModal').classList.remove('hidden');
                });
            });
            
            // Setup event untuk tombol switch role
            document.querySelectorAll('.switch-role-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const shareId = this.getAttribute('data-share-id');
                    const currentRole = this.getAttribute('data-current-role');
                    const projectId = this.getAttribute('data-project-id');
                    const newRole = currentRole === 'editor' ? 'viewer' : 'editor';
                    
                    // Kirim request untuk update role
            fetch(`/projects/${projectId}/share/${shareId}`, {
                        method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                            _method: 'PUT',
                            role: newRole
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                            // Refresh modal content
                            loadShareModal(projectId);
                    } else {
                            alert(data.message || 'Terjadi kesalahan saat mengubah peran.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                        alert('Terjadi kesalahan saat mengubah peran.');
                    });
                });
            });
            
            // Setup event untuk tombol batal modal konfirmasi
            document.getElementById('cancelDeleteShareBtn').addEventListener('click', function() {
                document.getElementById('deleteShareConfirmModal').classList.add('hidden');
            });
            
            // Setup event untuk form hapus share
            document.getElementById('deleteShareForm').addEventListener('submit', function(e) {
                    e.preventDefault();

                const projectId = this.action.split('/').slice(-3)[0];
                const shareId = this.action.split('/').slice(-1)[0]; 
                
                fetch(this.action, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({
                        _method: 'DELETE'
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                        .then(data => {
                    // Tutup modal konfirmasi
                    document.getElementById('deleteShareConfirmModal').classList.add('hidden');

                            if (data.success) {
                        // Refresh modal content
                        loadShareModal(projectId);
                            } else {
                        alert(data.message || 'Terjadi kesalahan saat menghapus akses.');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                    document.getElementById('deleteShareConfirmModal').classList.add('hidden');
                    
                    // Coba refresh modal content meskipun terjadi error
                    loadShareModal(projectId);
                        });
                });
            }

        // Fungsi untuk konfirmasi hapus akses proyek
        function confirmRemoveAccess(projectId, projectName) {
            showCustomConfirm(
                `Apakah Anda yakin ingin menghapus akses Anda dari proyek "${projectName}"?`,
                () => {
                    // Buat form untuk submit
                    const form = document.createElement('form');
                    form.action = `/projects/${projectId}/remove-own-access`;
                    form.method = 'POST';
                    form.innerHTML = `
                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="_method" value="DELETE">
                `;
                    document.body.appendChild(form);
                    form.submit();
                },
                () => {
                    console.log('Hapus akses dibatalkan.');
                }
            );
        }
    </script>

    <!-- Share Project Modal -->
    <div id="shareProjectModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
            <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-4xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
                <!-- Header -->
                <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
                    <div class="flex justify-between items-center">
                        <h2 class="text-white dark:text-dark-text text-lg font-semibold">Bagikan Proyek</h2>
            <button type="button" onclick="closeModal('shareProjectModal')"
                            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                            <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
                    </div>
                </div>
                <!-- Content -->
                <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
                    <div id="shareProjectModalContent" class="grid grid-cols-1 gap-8">
                        <div class="text-center py-10">
                            <i class="fas fa-spinner fa-spin text-4xl text-blue-500"></i>
                            <p class="mt-4 text-gray-600">Memuat data...</p>
                        </div>
                    </div>
                </div>
                    </div>
                </div>

        <!-- Delete Share Confirmation Modal -->
        <div id="deleteShareConfirmModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-[60] overflow-y-auto py-8 text-sm">
            <div class="bg-white dark:bg-dark-card rounded-xl shadow-lg p-0 max-w-sm mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
                <!-- Header -->
                <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
                    <div class="flex justify-between items-center">
                        <h3 class="text-white dark:text-dark-text text-lg font-semibold">Konfirmasi Hapus</h3>
                        <button id="closeDeleteShareBtn" type="button" onclick="document.getElementById('deleteShareConfirmModal').classList.add('hidden')"
                            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                            <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                    </button>
                </div>
                </div>
                <!-- Content -->
                <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
                        <p class="text-gray-700 dark:text-gray-300 mb-6">
                            Apakah Anda yakin ingin menghapus akses <span id="shareUserNameToDelete" class="font-semibold"></span> dari proyek ini?
                        </p>
                        <div class="flex justify-center space-x-4">
                            <button id="cancelDeleteShareBtn" type="button" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                                Tidak
                            </button>
                            <form id="deleteShareForm" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                    Ya, Hapus
                                </button>
            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @vite(['resources/js/loading.js'])
@endsection
