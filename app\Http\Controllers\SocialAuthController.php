<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;

class SocialAuthController extends Controller
{
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            $user = User::where('email', $googleUser->email)->first();

            if (!$user) {
                $user = User::create([
                    'name' => $googleUser->name,
                    'email' => $googleUser->email,
                    'password' => bcrypt(rand(100000, 999999)),
                    'google_id' => $googleUser->id,
                ]);
            }

            Auth::login($user);
            return redirect()->intended('/proyek');
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'Something went wrong with Google authentication');
        }
    }

    public function redirectToFacebook()
    {
        return Socialite::driver('facebook')->redirect();
    }

    public function handleFacebookCallback()
    {
        try {
            $facebookUser = Socialite::driver('facebook')->user();

            $user = User::where('email', $facebookUser->email)->first();

            if (!$user) {
                $user = User::create([
                    'name' => $facebookUser->name,
                    'email' => $facebookUser->email,
                    'password' => bcrypt(rand(100000, 999999)),
                    'facebook_id' => $facebookUser->id,
                ]);
            }

            Auth::login($user);
            return redirect()->intended('/proyek');
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'Something went wrong with Facebook authentication');
        }
    }
}
