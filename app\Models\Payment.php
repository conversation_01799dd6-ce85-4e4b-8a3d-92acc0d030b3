<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use \Illuminate\Database\Eloquent\Factories\HasFactory;

    protected $fillable = [
        'user_id',
        'subscription_id',
        'payment_id',
        'payment_method',
        'amount',
        'currency',
        'status',
        'paid_at',
        'payment_details',
        'invoice_number',
        'description',
        'payment_proof'
    ];

    protected $casts = [
        'amount' => 'float',
        'payment_details' => 'array',
        'paid_at' => 'datetime'
    ];

    /**
     * Get the user that made the payment
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription this payment is for
     */
    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute()
    {
        return 'Rp. ' . number_format($this->amount, 0, ',', '.');
    }

    /**
     * Check if payment is completed
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * Check if payment is pending
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if payment has failed
     */
    public function hasFailed()
    {
        return $this->status === 'failed';
    }

    /**
     * Check if payment was refunded
     */
    public function wasRefunded()
    {
        return $this->status === 'refunded';
    }
}
