<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Upah extends Model
{
    use HasFactory;

    protected $table = 'upahs';

    protected $fillable = [
        'uraian_tenaga',
        'satuan',
        'harga',
        'sumber',
        'alamat'
    ];

    public function details()
    {
        return $this->morphMany(\App\Models\AhspDetail::class, 'itemable');
    }

    // Accessor untuk download Excel dengan rumus
    public function getNamaUpahAttribute()
    {
        return $this->uraian_tenaga;
    }

    public function getHargaSatuanAttribute()
    {
        return $this->harga;
    }
}
