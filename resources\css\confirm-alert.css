/* 
 * File ini sudah tidak digunakan lagi.
 * Kode CSS dari confirm-alert.css telah digantikan dengan kelas Tailwind CSS
 * yang langsung diimplementasikan pada file resources/js/confirm.js
 */

/* Confirm Alert Styles - TIDAK DIGUNAKAN
.confirm-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.confirm-box {
    background-color: #bfdbfe; 
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-align: center;
    width: 300px;
    transition: all 0.3s ease;
}

.confirm-message {
    margin-bottom: 20px;
    color: #1f2937; 
    font-weight: 500;
}

.confirm-buttons {
    display: flex;
    justify-content: space-between;
}

.confirm-button {
    background-color: #2563eb; 
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
    transition: all 0.2s ease;
}

.confirm-button:hover {
    background-color: #1d4ed8; 
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cancel-button {
    background-color: #e5e7eb; 
    color: #374151; 
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-button:hover {
    background-color: #d1d5db; 
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .confirm-box {
    background-color: #2c2c2c; 
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .confirm-message {
    color: #e5e5e5; 
}

.dark .confirm-button {
    background-color: #ed8936; 
}

.dark .confirm-button:hover {
    background-color: #dd6b20; 
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark .cancel-button {
    background-color: #4b5563; 
    color: #e5e5e5; 
}

.dark .cancel-button:hover {
    background-color: #374151; 
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
*/
