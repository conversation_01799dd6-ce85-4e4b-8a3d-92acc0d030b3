<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $role
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $role)
    {
        // Log incoming request for debugging
        Log::info('CheckRole middleware', [
            'path' => $request->path(),
            'required_role' => $role,
            'user_role' => Auth::user() ? Auth::user()->role : 'guest',
            'is_ajax' => $request->ajax() || $request->wantsJson()
        ]);

        // Allow access to resource creation routes for all authenticated users
        $resourcePaths = ['/upah', '/alat', '/bahan'];
        if ($request->method() === 'POST' && in_array($request->path(), $resourcePaths)) {
            Log::info('Allowing resource creation access for authenticated user');
            return $next($request);
        }

        // Otherwise, check user role
        if (Auth::check() && Auth::user()->role === $role) {
            return $next($request);
        }

        // Handle AJAX requests with proper JSON response
        if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. You do not have permission to access this resource.'
            ], 403);
        }

        // For regular requests, redirect with error message
        if (Auth::check()) {
            if (Auth::user()->role === 'admin') {
                return redirect()->route('admin.dashboard')->with('error', 'Unauthorized. You do not have permission to access this resource.');
            }
            return redirect()->route('proyek')->with('error', 'Unauthorized. You do not have permission to access this resource.');
        }
        return redirect()->route('login')->with('error', 'You need to login first.');
    }
}
