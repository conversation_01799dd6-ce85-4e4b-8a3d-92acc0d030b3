/* Toast Dark Mode CSS - Copy dan paste ke file CSS Anda */

/* Toast Container */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

/* Toast Base */
.toast {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
    margin-bottom: 10px;
    animation: toastIn 0.3s ease forwards;
    position: relative;
    display: flex;
    align-items: flex-start;
    overflow: hidden;
    border-left: 5px solid;
}

/* Toast Colors */
.toast-success { border-left-color: #10b981; }
.toast-success .toast-icon { color: #10b981; }
.toast-error { border-left-color: #ef4444; }
.toast-error .toast-icon { color: #ef4444; }
.toast-info { border-left-color: #3b82f6; }
.toast-info .toast-icon { color: #3b82f6; }
.toast-warning { border-left-color: #f59e0b; }
.toast-warning .toast-icon { color: #f59e0b; }

/* Toast Dark Mode */
.dark .toast {
    background-color: #2c2c2c;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .toast-success .toast-icon { text-shadow: 0 0 5px rgba(16, 185, 129, 0.5); }
.dark .toast-error .toast-icon { text-shadow: 0 0 5px rgba(239, 68, 68, 0.5); }
.dark .toast-info .toast-icon { text-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
.dark .toast-warning .toast-icon { text-shadow: 0 0 5px rgba(245, 158, 11, 0.5); }

/* Toast Text Dark Mode */
.dark .toast-title {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dark .toast-message {
    color: #e5e5e5;
}

/* Toast Progress Bars Dark Mode */
.dark .toast-progress {
    background: linear-gradient(90deg, #ed8936, #dd6b20, #c05621, #9c4221);
    background-size: 300% 100%;
}

.dark .toast-success .toast-progress { box-shadow: 0 0 8px rgba(16, 185, 129, 0.4); }
.dark .toast-error .toast-progress { box-shadow: 0 0 8px rgba(239, 68, 68, 0.4); }
.dark .toast-warning .toast-progress { box-shadow: 0 0 8px rgba(245, 158, 11, 0.4); }
.dark .toast-info .toast-progress { box-shadow: 0 0 8px rgba(59, 130, 246, 0.4); }

/* Toast Close Button Dark Mode */
.dark .toast-close { color: #9ca3af; }
.dark .toast-close:hover { color: #ffffff; }
