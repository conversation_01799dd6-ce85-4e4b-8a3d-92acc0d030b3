<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Early theme initialization to prevent FOUC (Flash of Unstyled Content) -->
    <script>
        // Check for saved theme preference or use system preference
        const savedTheme = localStorage.getItem("theme");
        const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;

        // Apply theme immediately before page renders
        if (savedTheme === "dark" || (!savedTheme && prefersDark)) {
            document.documentElement.classList.add("dark");
        } else {
            document.documentElement.classList.remove("dark");
        }
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>RAB Estimator - @yield('title', 'Welcome')</title>

    <!-- Tailwind CSS -->
    @vite('resources/css/app.css')

    <!-- Flowbite CSS -->
    <link href="{{ asset('css/flowbite.min.css') }}" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ asset('css/all.min.css') }}">

    <!-- Additional styles -->
    @yield('styles')
</head>
<body class="bg-light-bg text-light-text dark:bg-dark-bg dark:text-dark-text transition-colors duration-200">
    <!-- Komponen Navbar -->
    <x-navbar />

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- WhatsApp Button -->
    <div id="whatsapp-button" class="fixed bottom-6 right-6 z-50 group transition-all duration-300 transform">
        <a href="https://wa.me/6281234567890" target="_blank" class="bg-green-500 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:bg-green-600 hover:scale-110 flex items-center justify-center transform">
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413Z"/>
            </svg>
        </a>
        <div class="absolute bottom-full right-0 mb-2 w-auto opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
            <div class="bg-black text-white text-sm py-1 px-3 rounded-lg shadow-lg whitespace-nowrap">
                Hubungi Kami di WhatsApp
                <div class="absolute top-full right-5 w-3 h-3 -mt-1.5 rotate-45 bg-black"></div>
            </div>
        </div>
    </div>

    <!-- Back to Top Button -->
    <div id="back-to-top-container" class="fixed bottom-6 right-6 z-50 group opacity-0 invisible transition-all duration-300">
        <button id="back-to-top" class="bg-light-accent dark:bg-dark-accent text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 hover:scale-110 flex items-center justify-center transform">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
            </svg>
        </button>
        <div class="absolute bottom-full right-0 mb-2 w-auto opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
            <div class="bg-black text-white text-sm py-1 px-3 rounded-lg shadow-lg whitespace-nowrap">
                Kembali ke Atas
                <div class="absolute top-full right-5 w-3 h-3 -mt-1.5 rotate-45 bg-black"></div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white dark:bg-dark-bg-secondary mt-8 transition-colors duration-200">
        <div class="mx-auto w-full max-w-screen-xl p-4 py-6 lg:py-8">
            <div class="md:flex md:justify-between">
                <div class="mb-6 md:mb-0">
                    <a href="{{ route('visitor.welcome') }}" class="flex items-center">
                        <span class="self-center text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent whitespace-nowrap">RAB Estimator</span>
                    </a>
                    <p class="mt-3 text-gray-600 dark:text-dark-text/70 max-w-md">
                        Aplikasi estimasi biaya konstruksi terbaik untuk membantu anda merencanakan anggaran proyek dengan tepat.
                    </p>
                </div>
                <div class="grid grid-cols-2 gap-8 sm:gap-6 sm:grid-cols-3">
                    <div>
                        <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-dark-text">Menu</h2>
                        <ul class="text-gray-500 dark:text-dark-text/70 font-medium">
                            <li class="mb-4">
                                <a href="{{ route('visitor.tutorial') }}" class="hover:underline">Tutorial</a>
                            </li>
                            <li class="mb-4">
                                <a href="{{ route('visitor.packages') }}" class="hover:underline">Paket</a>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-dark-text">Hubungi Kami</h2>
                        <ul class="text-gray-500 dark:text-dark-text/70 font-medium">
                            <li class="mb-4">
                                <a href="#" class="hover:underline">Email</a>
                            </li>
                            <li>
                                <a href="#" class="hover:underline">WhatsApp</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <hr class="my-6 border-gray-200 dark:border-dark-accent/10 sm:mx-auto lg:my-8" />
            <div class="sm:flex sm:items-center sm:justify-between">
                <span class="text-sm text-gray-500 dark:text-dark-text/70 sm:text-center">© {{ date('Y') }} <a href="{{ route('visitor.welcome') }}" class="hover:underline">RAB Estimator™</a>. All Rights Reserved.
                </span>
                <div class="flex mt-4 sm:justify-center sm:mt-0">
                    <a href="#" class="text-gray-500 hover:text-gray-900 dark:text-dark-text/50 dark:hover:text-dark-text ms-5">
                        <svg class="w-4 h-4" aria-hidden="true" fill="currentColor" viewBox="0 0 8 19">
                            <path fill-rule="evenodd" d="M6.135 3H8V0H6.135a4.147 4.147 0 0 0-4.142 4.142V6H0v3h2v9.938h3V9h2.021l.592-3H5V3.591A.6.6 0 0 1 5.592 3h.543Z" clip-rule="evenodd"/>
                        </svg>
                        <span class="sr-only">Facebook page</span>
                    </a>
                    <a href="#" class="text-gray-500 hover:text-gray-900 dark:text-dark-text/50 dark:hover:text-dark-text ms-5">
                        <svg class="w-4 h-4" aria-hidden="true" fill="currentColor" viewBox="0 0 21 16">
                            <path d="M16.942 1.556a16.3 16.3 0 0 0-4.126-1.3 12.04 12.04 0 0 0-.529 1.1 15.175 15.175 0 0 0-4.573 0 11.585 11.585 0 0 0-.535-1.1 16.274 16.274 0 0 0-4.129 1.3A17.392 17.392 0 0 0 .182 13.218a15.785 15.785 0 0 0 4.963 2.521c.41-.564.773-1.16 1.084-1.785a10.63 10.63 0 0 1-1.706-.83c.143-.106.283-.217.418-.33a11.664 11.664 0 0 0 10.118 0c.137.113.277.224.418.33-.544.328-1.116.606-1.71.832a12.52 12.52 0 0 0 1.084 1.785 16.46 16.46 0 0 0 5.064-2.595 17.286 17.286 0 0 0-2.973-11.59ZM6.678 10.813a1.941 1.941 0 0 1-1.8-2.045 1.93 1.93 0 0 1 1.8-2.047 1.919 1.919 0 0 1 1.8 2.047 1.93 1.93 0 0 1-1.8 2.045Zm6.644 0a1.94 1.94 0 0 1-1.8-2.045 1.93 1.93 0 0 1 1.8-2.047 1.918 1.918 0 0 1 1.8 2.047 1.93 1.93 0 0 1-1.8 2.045Z"/>
                        </svg>
                        <span class="sr-only">Discord community</span>
                    </a>
                    <a href="#" class="text-gray-500 hover:text-gray-900 dark:text-dark-text/50 dark:hover:text-dark-text ms-5">
                        <svg class="w-4 h-4" aria-hidden="true" fill="currentColor" viewBox="0 0 20 17">
                            <path fill-rule="evenodd" d="M20 1.892a8.178 8.178 0 0 1-2.355.635 4.074 4.074 0 0 0 1.8-2.235 8.344 8.344 0 0 1-2.605.98A4.13 4.13 0 0 0 13.85 0a4.068 4.068 0 0 0-4.1 4.038 4 4 0 0 0 .105.919A11.705 11.705 0 0 1 1.4.734a4.006 4.006 0 0 0 1.268 5.392 4.165 4.165 0 0 1-1.859-.5v.05A4.057 4.057 0 0 0 4.1 9.635a4.19 4.19 0 0 1-1.856.07 4.108 4.108 0 0 0 3.831 2.807A8.36 8.36 0 0 1 0 14.184 11.732 11.732 0 0 0 6.291 16 11.502 11.502 0 0 0 17.964 4.5c0-.177 0-.35-.012-.523A8.143 8.143 0 0 0 20 1.892Z" clip-rule="evenodd"/>
                        </svg>
                        <span class="sr-only">Twitter page</span>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Flowbite JS -->
    <script src="{{ asset('js/flowbite.min.js') }}"></script>

    <!-- Scripts -->
    @yield('scripts')

    <!-- Back to Top Button and WhatsApp Button Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const backToTopButton = document.getElementById('back-to-top');
            const backToTopContainer = document.getElementById('back-to-top-container');
            const whatsappButton = document.getElementById('whatsapp-button');

            // Default position for WhatsApp button (when back-to-top is visible)
            const whatsappDefaultPosition = 'right-20';
            // Position for WhatsApp button when it takes back-to-top's place
            const whatsappBackToTopPosition = 'right-6';

            // Show button when user scrolls down 300px from the top
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    // Show back-to-top button
                    backToTopContainer.classList.remove('opacity-0', 'invisible');
                    backToTopContainer.classList.add('opacity-100');

                    // Move WhatsApp button to its default position
                    whatsappButton.classList.remove(whatsappBackToTopPosition);
                    whatsappButton.classList.add(whatsappDefaultPosition);
                } else {
                    // Hide back-to-top button
                    backToTopContainer.classList.remove('opacity-100');
                    backToTopContainer.classList.add('opacity-0', 'invisible');

                    // Move WhatsApp button to back-to-top position
                    whatsappButton.classList.remove(whatsappDefaultPosition);
                    whatsappButton.classList.add(whatsappBackToTopPosition);
                }
            });

            // Initial position check on page load
            if (window.pageYOffset > 300) {
                // Show back-to-top button
                backToTopContainer.classList.remove('opacity-0', 'invisible');
                backToTopContainer.classList.add('opacity-100');

                // Move WhatsApp button to its default position
                whatsappButton.classList.remove(whatsappBackToTopPosition);
                whatsappButton.classList.add(whatsappDefaultPosition);
            } else {
                // Hide back-to-top button
                backToTopContainer.classList.remove('opacity-100');
                backToTopContainer.classList.add('opacity-0', 'invisible');

                // Move WhatsApp button to back-to-top position
                whatsappButton.classList.remove(whatsappDefaultPosition);
                whatsappButton.classList.add(whatsappBackToTopPosition);
            }

            // Scroll to top when button is clicked
            backToTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        });
    </script>

    <!-- Handle theme toggles for visitor pages -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sembunyikan mobile theme toggle saat menu utama terbuka
            const mobileMenuButton = document.querySelector('[data-collapse-toggle="navbar-mobile"]');
            const mobileThemeToggleContainer = document.getElementById('mobile-theme-toggle-container');
            const navbarMobile = document.getElementById('navbar-mobile');

            if (mobileMenuButton && mobileThemeToggleContainer && navbarMobile) {
                // Fungsi untuk memeriksa status menu dan menyesuaikan visibilitas toggle
                const updateToggleVisibility = function() {
                    // Periksa apakah menu sedang terbuka (memiliki class 'hidden' atau tidak)
                    const isMenuOpen = !navbarMobile.classList.contains('hidden');

                    // Sembunyikan toggle jika menu terbuka, tampilkan jika menu tertutup
                    if (isMenuOpen) {
                        mobileThemeToggleContainer.style.display = 'none';
                    } else {
                        mobileThemeToggleContainer.style.display = 'flex';
                    }
                };

                // Tambahkan event listener untuk tombol menu
                mobileMenuButton.addEventListener('click', function() {
                    // Beri waktu sedikit untuk transisi menu selesai
                    setTimeout(updateToggleVisibility, 50);
                });

                // Jalankan sekali saat halaman dimuat
                updateToggleVisibility();
            }
            // Handle mobile toggle - always connect to sidebar toggle if available
            const mobileToggle = document.getElementById('mobile-theme-toggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Mobile toggle clicked in visitor layout');

                    // Check if sidebar toggle exists and use it
                    const sidebarToggle = document.getElementById('theme-toggle');
                    if (sidebarToggle) {
                        console.log('Using sidebar toggle');
                        sidebarToggle.click();
                        return;
                    }

                    // If sidebar toggle doesn't exist, handle directly
                    console.log('Sidebar toggle not found, handling directly');
                    document.documentElement.classList.toggle("dark");

                    // Update all toggle icons
                    if (typeof updateToggleIcons === 'function') {
                        updateToggleIcons();
                    } else {
                        // Fallback if function not available
                        const isDark = document.documentElement.classList.contains("dark");
                        const visitorIcon = document.getElementById('theme-toggle-visitor-icon');
                        const mobileIcon = document.getElementById('mobile-theme-toggle-icon');

                        if (visitorIcon) {
                            visitorIcon.style.transform = isDark ? 'translateX(16px)' : 'translateX(0)';
                        }

                        if (mobileIcon) {
                            mobileIcon.style.transform = isDark ? 'translateX(28px)' : 'translateX(0)';
                        }
                    }

                    // Save theme preference
                    if (document.documentElement.classList.contains("dark")) {
                        localStorage.setItem("theme", "dark");
                    } else {
                        localStorage.setItem("theme", "light");
                    }
                });
            }

            // Handle visitor toggle - always connect to sidebar toggle if available
            const visitorToggle = document.getElementById('theme-toggle-visitor');
            if (visitorToggle) {
                visitorToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Visitor toggle clicked in visitor layout');

                    // Check if sidebar toggle exists and use it
                    const sidebarToggle = document.getElementById('theme-toggle');
                    if (sidebarToggle) {
                        console.log('Using sidebar toggle');
                        sidebarToggle.click();
                        return;
                    }

                    // If sidebar toggle doesn't exist, handle directly
                    console.log('Sidebar toggle not found, handling directly');
                    document.documentElement.classList.toggle("dark");

                    // Update all toggle icons
                    if (typeof updateToggleIcons === 'function') {
                        updateToggleIcons();
                    } else {
                        // Fallback if function not available
                        const isDark = document.documentElement.classList.contains("dark");
                        const visitorIcon = document.getElementById('theme-toggle-visitor-icon');
                        const mobileIcon = document.getElementById('mobile-theme-toggle-icon');

                        if (visitorIcon) {
                            visitorIcon.style.transform = isDark ? 'translateX(16px)' : 'translateX(0)';
                        }

                        if (mobileIcon) {
                            mobileIcon.style.transform = isDark ? 'translateX(28px)' : 'translateX(0)';
                        }
                    }

                    // Save theme preference
                    if (document.documentElement.classList.contains("dark")) {
                        localStorage.setItem("theme", "dark");
                    } else {
                        localStorage.setItem("theme", "light");
                    }
                });
            }
        });
    </script>
</body>
</html>