<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class KoleksiPaginationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register the custom paginator view
        $this->loadViewsFrom(resource_path('views/components'), 'koleksi-pagination');

        // Use the custom paginator view for the 'koleksi-pagination' paginator
        \Illuminate\Pagination\LengthAwarePaginator::defaultView('components.koleksi-pagination');
    }
}
