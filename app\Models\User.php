<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'facebook_id',
        'role',
        'photo',
        'address',
        'phone',
        'current_subscription_id',
        'has_used_trial',
        'trial_started_at',
        'trial_ends_at',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'has_used_trial' => 'boolean',
            'trial_started_at' => 'datetime',
            'trial_ends_at' => 'datetime',
        ];
    }

    /**
     * Get the current subscription of the user
     */
    public function currentSubscription()
    {
        return $this->belongsTo(Subscription::class, 'current_subscription_id');
    }

    /**
     * Get the current subscription as an attribute
     */
    public function getCurrentSubscriptionAttribute()
    {
        return $this->currentSubscription()->first();
    }

    /**
     * Get all subscriptions of the user
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get all payments made by the user
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Check if user has an active subscription
     */
    public function hasActiveSubscription()
    {
        if (!$this->currentSubscription) {
            return false;
        }

        return ($this->currentSubscription->status === 'active' &&
            $this->currentSubscription->end_date > Carbon::now()) ||
            $this->isOnTrial();
    }

    /**
     * Check if user is on trial period
     */
    public function isOnTrial()
    {
        return $this->trial_ends_at && $this->trial_ends_at > Carbon::now();
    }

    /**
     * Start trial period for user
     */
    public function startTrial($days = 15)
    {
        if ($this->has_used_trial) {
            return false;
        }

        $this->has_used_trial = true;
        $this->trial_started_at = Carbon::now();
        $this->trial_ends_at = Carbon::now()->addDays($days);
        $this->save();

        return true;
    }

    /**
     * Get days remaining in trial
     *
     * @param bool $formatted Whether to return formatted string or number of days
     * @return string|int
     */
    public function trialDaysRemaining($formatted = false)
    {
        if (!$this->isOnTrial()) {
            return $formatted ? '0 hari 0 jam 0 menit 0 detik' : 0;
        }

        if (!$formatted) {
            return Carbon::now()->diffInDays($this->trial_ends_at);
        }

        // Hitung total detik yang tersisa
        $now = Carbon::now();
        $end = $this->trial_ends_at;
        $totalSeconds = $end->timestamp - $now->timestamp;

        if ($totalSeconds < 0) {
            return '0 hari 0 jam 0 menit 0 detik';
        }

        // Hitung hari, jam, menit, detik
        $days = floor($totalSeconds / 86400); // 86400 detik dalam sehari
        $totalSeconds %= 86400;

        $hours = floor($totalSeconds / 3600); // 3600 detik dalam sejam
        $totalSeconds %= 3600;

        $minutes = floor($totalSeconds / 60); // 60 detik dalam semenit
        $seconds = $totalSeconds % 60;

        return "{$days} hari {$hours} jam {$minutes} menit {$seconds} detik";
    }

    /**
     * Get all projects owned by the user
     */
    public function projects()
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get all projects shared with the user
     */
    public function sharedProjects()
    {
        return $this->belongsToMany(Project::class, 'project_shares')
            ->withPivot('role')
            ->withTimestamps();
    }

    /**
     * Get all project shares for the user
     */
    public function projectShares()
    {
        return $this->hasMany(ProjectShare::class);
    }

    /**
     * Get the maximum number of users that can be shared with based on the current subscription
     */
    public function getMaxShareUsersAttribute()
    {
        if (!$this->currentSubscription || !$this->currentSubscription->plan) {
            return 0;
        }

        return $this->currentSubscription->plan->max_users;
    }

    /**
     * Check if the user can share more projects based on subscription limits
     */
    public function canShareMoreProjects($projectId)
    {
        if (!$this->currentSubscription || !$this->currentSubscription->plan) {
            return false;
        }

        $maxUsers = $this->currentSubscription->plan->max_users;

        // If max_users is 1, user can't share projects
        if ($maxUsers <= 1) {
            return false;
        }

        // Count current shares for this project
        $currentShares = ProjectShare::where('project_id', $projectId)->count();

        // Check if we can add more shares
        return $currentShares < ($maxUsers - 1); // -1 because the owner counts as one user
    }

    /**
     * Get the number of remaining shares available for a project
     */
    public function getRemainingSharesForProject($projectId)
    {
        if (!$this->currentSubscription || !$this->currentSubscription->plan) {
            return 0;
        }

        $maxUsers = $this->currentSubscription->plan->max_users;

        // If max_users is 1, user can't share projects
        if ($maxUsers <= 1) {
            return 0;
        }

        // Count current shares for this project
        $currentShares = ProjectShare::where('project_id', $projectId)->count();

        // Calculate remaining shares
        $remainingShares = $maxUsers - 1 - $currentShares; // -1 because the owner counts as one user

        return max(0, $remainingShares);
    }

    /**
     * Determine if the user has verified their email address.
     *
     * @return bool
     */
    public function hasVerifiedEmail()
    {
        return $this->email_verified_at !== null;
    }

    /**
     * Mark the given user's email as verified.
     *
     * @return bool
     */
    public function markEmailAsVerified()
    {
        return $this->forceFill([
            'email_verified_at' => $this->freshTimestamp(),
        ])->save();
    }

    /**
     * Get the email for verification.
     */
    public function getEmailForVerification()
    {
        return $this->email;
    }

    /**
     * Send the email verification notification.
     *
     * @return void
     */
    public function sendEmailVerificationNotification()
    {
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60), // Link berlaku selama 60 menit
            [
                'id' => $this->getKey(),
                'hash' => sha1($this->getEmailForVerification()),
            ]
        );

        // Jika pakai facade Mail, bisa kirim email custom disini
        Mail::send('emails.verify-email', ['url' => $verificationUrl, 'user' => $this], function ($message) {
            $message->to($this->email)
                ->subject('Verifikasi Email Anda');
        });
    }
}
