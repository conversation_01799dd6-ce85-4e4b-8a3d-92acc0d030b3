<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    use \Illuminate\Database\Eloquent\Factories\HasFactory;

    protected $fillable = [
        'user_id',
        'subscription_plan_id',
        'duration_id',
        'start_date',
        'end_date',
        'status',
        'is_trial',
        'trial_ends_at',
        'cancelled_at',
        'cancellation_reason',
        'auto_renew'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'trial_ends_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'is_trial' => 'boolean',
        'auto_renew' => 'boolean'
    ];

    /**
     * Get the user that owns the subscription
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the plan for this subscription
     */
    public function plan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'subscription_plan_id');
    }

    /**
     * Get the duration for this subscription
     */
    public function duration()
    {
        return $this->belongsTo(SubscriptionDuration::class, 'duration_id');
    }

    /**
     * Get all payments for this subscription
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Check if subscription is active
     */
    public function isActive()
    {
        return $this->status === 'active' && $this->end_date > now();
    }

    /**
     * Check if subscription is in trial period
     */
    public function isOnTrial()
    {
        return $this->is_trial && $this->trial_ends_at > now();
    }

    /**
     * Check if subscription is cancelled
     */
    public function isCancelled()
    {
        return $this->cancelled_at !== null;
    }

    /**
     * Check if subscription is expired
     */
    public function isExpired()
    {
        return $this->end_date < now();
    }

    /**
     * Get days remaining in subscription
     *
     * @param bool $formatted Whether to return formatted string or number of days
     * @return string|int
     */
    public function daysRemaining($formatted = false)
    {
        if ($this->isExpired()) {
            return $formatted ? '0 hari 0 jam 0 menit 0 detik' : 0;
        }

        $now = now();
        $end = $this->end_date;

        if (!$formatted) {
            return $now->diffInDays($end);
        }

        // Hitung total detik yang tersisa
        $totalSeconds = $end->timestamp - $now->timestamp;

        if ($totalSeconds < 0) {
            return '0 hari 0 jam 0 menit 0 detik';
        }

        // Hitung hari, jam, menit, detik
        $days = floor($totalSeconds / 86400); // 86400 detik dalam sehari
        $totalSeconds %= 86400;

        $hours = floor($totalSeconds / 3600); // 3600 detik dalam sejam
        $totalSeconds %= 3600;

        $minutes = floor($totalSeconds / 60); // 60 detik dalam semenit
        $seconds = $totalSeconds % 60;

        return "{$days} hari {$hours} jam {$minutes} menit {$seconds} detik";
    }
}
