@extends('layouts.visitor')

@section('title', 'Tutorial')

@section('content')
    <!-- Header Section -->
    <section class="bg-gradient-to-r from-light-navbar via-[#0C4A7A] to-[#083D66] dark:from-blue-900 dark:to-blue-950 text-white pt-32 pb-20 transition-colors duration-200">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl font-bold mb-6">Cara Menggunakan RAB Estimator</h1>
            <p class="text-xl max-w-3xl mx-auto mb-4">Pelajari cara memaksimalkan penggunaan RAB Estimator untuk kebutuhan proyek konstruksi Anda.</p>
        </div>
    </section>

    <!-- Getting Started Steps -->
    <section class="py-16 bg-white dark:bg-dark-bg-secondary transition-colors duration-200">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4 text-gray-900 dark:text-white transition-colors duration-200">Panduan Memulai</h2>
                <p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto transition-colors duration-200">Ikuti langkah-langkah berikut untuk mulai menggunakan RAB Estimator dengan optimal.</p>
            </div>

            <div class="max-w-4xl mx-auto">
                <div class="mb-12">
                    <div class="flex items-start">
                        <div class="bg-light-accent dark:bg-dark-accent text-white rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0 mr-4 transition-colors duration-200">
                            <span class="font-bold">1</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Membuat Akun</h3>
                            <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">Daftar dan buat akun baru di RAB Estimator. Anda akan mendapatkan akses uji coba gratis selama 14 hari.</p>
                            <a href="{{ route('register') }}" class="text-blue-600 dark:text-blue-400 font-medium hover:underline transition-colors duration-200">Daftar Sekarang →</a>
                        </div>
                    </div>
                </div>

                <div class="mb-12">
                    <div class="flex items-start">
                        <div class="bg-light-accent dark:bg-dark-accent text-white rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0 mr-4 transition-colors duration-200">
                            <span class="font-bold">2</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Membuat Proyek Baru</h3>
                            <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">Klik tombol "Proyek Baru" pada dashboard dan masukkan informasi dasar proyek seperti nama, lokasi, dan tanggal.</p>
                            <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-4 transition-colors duration-200">
                                <p class="text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Tip: Gunakan penamaan proyek yang jelas agar mudah diidentifikasi nantinya, contoh: "Pembangunan Rumah Tinggal - Jl. Mawar No.10"</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-12">
                    <div class="flex items-start">
                        <div class="bg-light-accent dark:bg-dark-accent text-white rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0 mr-4 transition-colors duration-200">
                            <span class="font-bold">3</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Menyusun Rencana Pekerjaan</h3>
                            <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">Tambahkan struktur pekerjaan dengan menentukan kelompok pekerjaan, item pekerjaan, dan detail volume pekerjaan.</p>
                            <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-4 transition-colors duration-200">
                                <p class="text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Contoh struktur pekerjaan:</p>
                                <ul class="list-disc pl-5 text-sm text-gray-700 dark:text-gray-300 mt-2 transition-colors duration-200">
                                    <li>I. Pekerjaan Persiapan</li>
                                    <li>II. Pekerjaan Struktur</li>
                                    <li>III. Pekerjaan Arsitektur</li>
                                    <li>IV. Pekerjaan MEP</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-12">
                    <div class="flex items-start">
                        <div class="bg-light-accent dark:bg-dark-accent text-white rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0 mr-4 transition-colors duration-200">
                            <span class="font-bold">4</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Menambahkan AHSP</h3>
                            <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">Pilih AHSP dari database atau buat AHSP custom sesuai kebutuhan proyek Anda. Anda dapat menyesuaikan harga material dan upah.</p>
                            <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-4 transition-colors duration-200">
                                <p class="text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Tip: Database AHSP kami mencakup standar dari berbagai sumber seperti SNI, PU, dan PUPR yang terus diperbarui.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-12">
                    <div class="flex items-start">
                        <div class="bg-light-accent dark:bg-dark-accent text-white rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0 mr-4 transition-colors duration-200">
                            <span class="font-bold">5</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Melihat Hasil dan Laporan</h3>
                            <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">RAB Estimator akan menghitung secara otomatis dan menampilkan hasil estimasi biaya. Anda dapat melihat laporan detail dan mengekspornya dalam berbagai format.</p>
                            <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-4 transition-colors duration-200">
                                <p class="text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Lihat laporan detail RAB untuk analisis lebih lanjut</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Tutorials -->
    <section class="py-16 bg-gray-50 dark:bg-dark-bg transition-colors duration-200">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4 text-gray-900 dark:text-white transition-colors duration-200">Video Tutorial</h2>
                <p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto transition-colors duration-200">Pelajari lebih lanjut dengan video tutorial yang kami sediakan untuk membantu Anda memahami fitur-fitur RAB Estimator.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-colors duration-200">
                    <div class="bg-gray-300 dark:bg-gray-700 aspect-video flex items-center justify-center transition-colors duration-200">
                        <svg  class="h-20 w-20 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2 text-gray-900 dark:text-white transition-colors duration-200">Pengenalan RAB Estimator</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">Pelajari fitur-fitur dasar dan navigasi di platform RAB Estimator.</p>
                        <a href="#" class="text-blue-600 dark:text-blue-400 font-medium hover:underline transition-colors duration-200">Tonton Video →</a>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-colors duration-200">
                    <div class="bg-gray-300 dark:bg-gray-700 aspect-video flex items-center justify-center transition-colors duration-200">
                        <svg  class="h-20 w-20 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2 text-gray-900 dark:text-white transition-colors duration-200">Membuat Proyek Pertama</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">Tutorial lengkap pembuatan proyek dari awal hingga menghasilkan laporan RAB.</p>
                        <a href="#" class="text-blue-600 dark:text-blue-400 font-medium hover:underline transition-colors duration-200">Tonton Video →</a>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-colors duration-200">
                    <div class="bg-gray-300 dark:bg-gray-700 aspect-video flex items-center justify-center transition-colors duration-200">
                        <svg  class="h-20 w-20 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2 text-gray-900 dark:text-white transition-colors duration-200">Menggunakan AHSP Custom</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">Belajar cara membuat dan menggunakan AHSP khusus untuk kebutuhan proyek Anda.</p>
                        <a href="#" class="text-blue-600 dark:text-blue-400 font-medium hover:underline transition-colors duration-200">Tonton Video →</a>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-colors duration-200">
                    <div class="bg-gray-300 dark:bg-gray-700 aspect-video flex items-center justify-center transition-colors duration-200">
                        <svg  class="h-20 w-20 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2 text-gray-900 dark:text-white transition-colors duration-200">Kolaborasi Tim</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">Cara menggunakan fitur kolaborasi untuk bekerja bersama tim pada proyek yang sama.</p>
                        <a href="#" class="text-blue-600 dark:text-blue-400 font-medium hover:underline transition-colors duration-200">Tonton Video →</a>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-colors duration-200">
                    <div class="bg-gray-300 dark:bg-gray-700 aspect-video flex items-center justify-center transition-colors duration-200">
                        <svg  class="h-20 w-20 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2 text-gray-900 dark:text-white transition-colors duration-200">Menganalisis Laporan RAB</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">Cara membaca dan menganalisis laporan RAB untuk pengambilan keputusan yang lebih baik.</p>
                        <a href="#" class="text-blue-600 dark:text-blue-400 font-medium hover:underline transition-colors duration-200">Tonton Video →</a>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-colors duration-200">
                    <div class="bg-gray-300 dark:bg-gray-700 aspect-video flex items-center justify-center transition-colors duration-200">
                        <svg  class="h-20 w-20 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2 text-gray-900 dark:text-white transition-colors duration-200">Tips dan Trik Lanjutan</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">Teknik lanjutan untuk memaksimalkan penggunaan RAB Estimator dalam proyek besar.</p>
                        <a href="#" class="text-blue-600 dark:text-blue-400 font-medium hover:underline transition-colors duration-200">Tonton Video →</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQs -->
    <section class="py-16 bg-white dark:bg-dark-bg-secondary transition-colors duration-200">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4 text-gray-900 dark:text-white transition-colors duration-200">Pertanyaan yang Sering Diajukan (FAQ)</h2>
                <p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto transition-colors duration-200">Temukan jawaban untuk pertanyaan umum tentang penggunaan RAB Estimator.</p>
            </div>

            <div class="max-w-3xl mx-auto">
                <div class="mb-6 border-b border-gray-200 dark:border-gray-700 pb-6 transition-colors duration-200">
                    <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Apakah saya bisa menggunakan RAB Estimator secara gratis?</h3>
                    <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">Ya, Anda bisa mencoba RAB Estimator secara gratis selama 14 hari. Setelah masa uji coba berakhir, Anda perlu berlangganan salah satu paket yang tersedia untuk terus menggunakan layanan ini.</p>
                </div>

                <div class="mb-6 border-b border-gray-200 dark:border-gray-700 pb-6 transition-colors duration-200">
                    <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Apakah data proyek saya aman?</h3>
                    <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">Ya, keamanan data adalah prioritas kami. Semua data dilindungi dengan enkripsi tingkat lanjut dan kami tidak pernah membagikan data Anda kepada pihak ketiga tanpa izin.</p>
                </div>

                <div class="mb-6 border-b border-gray-200 dark:border-gray-700 pb-6 transition-colors duration-200">
                    <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Bagaimana cara mengupdate harga material?</h3>
                    <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">Anda dapat memperbarui harga material melalui menu "Database Material" di dashboard. Anda juga dapat mengimpor daftar harga material dalam format Excel atau CSV.</p>
                </div>

                <div class="mb-6 border-b border-gray-200 dark:border-gray-700 pb-6 transition-colors duration-200">
                    <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Apakah saya bisa mengakses RAB Estimator dari perangkat mobile?</h3>
                    <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">Ya, RAB Estimator dapat diakses dari berbagai perangkat seperti smartphone, tablet, dan laptop. Interface kami responsif dan menyesuaikan dengan ukuran layar perangkat Anda.</p>
                </div>

                <div class="mb-6 border-b border-gray-200 dark:border-gray-700 pb-6 transition-colors duration-200">
                    <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Bisakah saya mengundang orang lain untuk berkolaborasi dalam proyek?</h3>
                    <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">Ya, fitur kolaborasi tersedia di paket Team dan Enterprise. Anda dapat mengundang anggota tim atau klien untuk melihat atau mengedit proyek sesuai dengan hak akses yang Anda berikan.</p>
                </div>

                <div class="mb-6 border-b border-gray-200 dark:border-gray-700 pb-6 transition-colors duration-200">
                    <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Bagaimana jika saya membutuhkan bantuan teknis?</h3>
                    <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">Tim dukungan teknis kami siap membantu Anda melalui live chat, email, atau telepon selama jam kerja. Untuk pengguna paket Enterprise, kami menyediakan dukungan prioritas 24/7.</p>
                </div>

                <div class="mb-6">
                    <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white transition-colors duration-200">Apakah RAB Estimator mendukung standar AHSP dari luar Indonesia?</h3>
                    <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">Saat ini, kami fokus pada standar AHSP Indonesia. Namun, Anda dapat membuat AHSP custom sesuai dengan standar negara lain jika diperlukan.</p>
                </div>
            </div>

            <div class="text-center mt-12">
        <div class="max-w-screen-xl mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4 transition-colors duration-200">Video Tutorial</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto transition-colors duration-200">
                    Pelajari langkah demi langkah cara menggunakan RAB Estimator melalui tutorial video
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Tutorial Video 1 -->
                <div class="bg-white dark:bg-dark-card rounded-lg overflow-hidden shadow-md border border-gray-200 dark:border-gray-700 transition-colors duration-200">
                    <div class="aspect-video bg-gray-100 dark:bg-gray-800 relative transition-colors duration-200">
                        <img src="https://source.unsplash.com/random/600x400/?construction,tutorial" alt="Tutorial RAB" class="w-full h-full object-cover">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-16 h-16 rounded-full bg-light-accent/80 dark:bg-dark-accent/80 flex items-center justify-center cursor-pointer hover:bg-light-accent dark:hover:bg-dark-accent transition-all">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24" >
                                    <path d="M8 5v14l11-7z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 transition-colors duration-200">Pengenalan RAB Estimator</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">
                            Tutorial dasar untuk memahami antarmuka dan fitur utama RAB Estimator.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">Durasi: 15 menit</span>
                            <a href="#" class="text-light-accent dark:text-dark-accent font-medium hover:underline transition-colors duration-200">Tonton Video</a>
                        </div>
                    </div>
                </div>

                <!-- Tutorial Video 2 -->
                <div class="bg-white rounded-lg overflow-hidden shadow-md border border-gray-200">
                    <div class="aspect-video bg-gray-100 relative">
                        <img src="https://source.unsplash.com/random/600x400/?construction,estimate" alt="Tutorial Estimasi" class="w-full h-full object-cover">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-16 h-16 rounded-full bg-light-accent/80 flex items-center justify-center cursor-pointer hover:bg-light-accent transition-all">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24" >
                                    <path d="M8 5v14l11-7z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Membuat Proyek Baru</h3>
                        <p class="text-gray-600 mb-4">
                            Pelajari cara membuat proyek baru dan menyiapkan data awal untuk RAB.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Durasi: 12 menit</span>
                            <a href="#" class="text-light-accent font-medium hover:underline">Tonton Video</a>
                        </div>
                    </div>
                </div>

                <!-- Tutorial Video 3 -->
                <div class="bg-white rounded-lg overflow-hidden shadow-md border border-gray-200">
                    <div class="aspect-video bg-gray-100 relative">
                        <img src="https://source.unsplash.com/random/600x400/?architect,blueprint" alt="Tutorial AHS" class="w-full h-full object-cover">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-16 h-16 rounded-full bg-light-accent/80 flex items-center justify-center cursor-pointer hover:bg-light-accent transition-all">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24" >
                                    <path d="M8 5v14l11-7z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Pengelolaan AHSP</h3>
                        <p class="text-gray-600 mb-4">
                            Tutorial tentang cara mengelola dan menggunakan Analisis Harga Satuan Pekerjaan.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Durasi: 18 menit</span>
                            <a href="#" class="text-light-accent font-medium hover:underline">Tonton Video</a>
                        </div>
                    </div>
                </div>

                <!-- Tutorial Video 4 -->
                <div class="bg-white rounded-lg overflow-hidden shadow-md border border-gray-200">
                    <div class="aspect-video bg-gray-100 relative">
                        <img src="https://source.unsplash.com/random/600x400/?architecture,measurement" alt="Tutorial Volume" class="w-full h-full object-cover">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-16 h-16 rounded-full bg-light-accent/80 flex items-center justify-center cursor-pointer hover:bg-light-accent transition-all">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24" >
                                    <path d="M8 5v14l11-7z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Perhitungan Volume</h3>
                        <p class="text-gray-600 mb-4">
                            Belajar cara menghitung volume pekerjaan dengan benar dan efisien.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Durasi: 15 menit</span>
                            <a href="#" class="text-light-accent font-medium hover:underline">Tonton Video</a>
                        </div>
                    </div>
                </div>

                <!-- Tutorial Video 5 -->
                <div class="bg-white rounded-lg overflow-hidden shadow-md border border-gray-200">
                    <div class="aspect-video bg-gray-100 relative">
                        <img src="https://source.unsplash.com/random/600x400/?report,document" alt="Tutorial Laporan" class="w-full h-full object-cover">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-16 h-16 rounded-full bg-light-accent/80 flex items-center justify-center cursor-pointer hover:bg-light-accent transition-all">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24" >
                                    <path d="M8 5v14l11-7z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Pembuatan Laporan</h3>
                        <p class="text-gray-600 mb-4">
                            Tutorial cara menghasilkan laporan RAB yang profesional dan siap dicetak.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Durasi: 10 menit</span>
                            <a href="#" class="text-light-accent font-medium hover:underline">Tonton Video</a>
                        </div>
                    </div>
                </div>

                <!-- Tutorial Video 6 -->
                <div class="bg-white rounded-lg overflow-hidden shadow-md border border-gray-200">
                    <div class="aspect-video bg-gray-100 relative">
                        <img src="https://source.unsplash.com/random/600x400/?teamwork,collaboration" alt="Tutorial Kolaborasi" class="w-full h-full object-cover">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-16 h-16 rounded-full bg-light-accent/80 flex items-center justify-center cursor-pointer hover:bg-light-accent transition-all">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24" >
                                    <path d="M8 5v14l11-7z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Kolaborasi Tim</h3>
                        <p class="text-gray-600 mb-4">
                            Cara berbagi proyek dan bekerja secara kolaboratif dengan tim Anda.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Durasi: 14 menit</span>
                            <a href="#" class="text-light-accent font-medium hover:underline">Tonton Video</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-10">
                <a href="#" class="inline-block bg-light-accent dark:bg-dark-accent text-white font-bold py-2 px-6 rounded-lg hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all">
                    Lihat Semua Video
                </a>
            </div>
        </div>
    </section>

    <!-- E-Book Section -->
    <section class="py-16 bg-gray-50 dark:bg-dark-bg transition-colors duration-200">
        <div class="max-w-screen-xl mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4 transition-colors duration-200">E-Book & Panduan</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto transition-colors duration-200">
                    Panduan lengkap dalam format PDF yang dapat Anda unduh dan baca kapan saja
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- E-Book 1 -->
                <div class="bg-white dark:bg-dark-card rounded-lg overflow-hidden shadow-md border border-gray-200 dark:border-gray-700 flex flex-col transition-colors duration-200">
                    <div class="h-56 bg-gray-100 dark:bg-gray-800 flex items-center justify-center p-6 transition-colors duration-200">
                        <img src="https://via.placeholder.com/200x280/EBF4FF/4F46E5?text=RAB+Estimator" alt="E-Book Panduan Pengguna" class="h-full object-contain shadow-lg">
                    </div>
                    <div class="p-6 flex-1 flex flex-col">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 transition-colors duration-200">Panduan Pengguna RAB Estimator</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 flex-1 transition-colors duration-200">
                            Panduan komprehensif mengenai seluruh fitur dan cara penggunaan RAB Estimator.
                        </p>
                        <div class="flex items-center justify-between mt-auto">
                            <span class="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">95 halaman</span>
                            <a href="#" class="text-white bg-light-accent dark:bg-dark-accent font-medium px-4 py-2 rounded-lg hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                                Unduh PDF
                            </a>
                        </div>
                    </div>
                </div>

                <!-- E-Book 2 -->
                <div class="bg-white dark:bg-dark-card rounded-lg overflow-hidden shadow-md border border-gray-200 dark:border-gray-700 flex flex-col transition-colors duration-200">
                    <div class="h-56 bg-gray-100 dark:bg-gray-800 flex items-center justify-center p-6 transition-colors duration-200">
                        <img src="https://via.placeholder.com/200x280/F0FDF4/047857?text=Panduan+AHSP" alt="E-Book AHSP" class="h-full object-contain shadow-lg">
                    </div>
                    <div class="p-6 flex-1 flex flex-col">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 transition-colors duration-200">Panduan Analisis Harga Satuan Pekerjaan</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 flex-1 transition-colors duration-200">
                            Penjelasan detail tentang AHSP, termasuk cara mengonfigurasi dan menghitung AHSP.
                        </p>
                        <div class="flex items-center justify-between mt-auto">
                            <span class="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">78 halaman</span>
                            <a href="#" class="text-white bg-light-accent dark:bg-dark-accent font-medium px-4 py-2 rounded-lg hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                                Unduh PDF
                            </a>
                        </div>
                    </div>
                </div>

                <!-- E-Book 3 -->
                <div class="bg-white dark:bg-dark-card rounded-lg overflow-hidden shadow-md border border-gray-200 dark:border-gray-700 flex flex-col transition-colors duration-200">
                    <div class="h-56 bg-gray-100 dark:bg-gray-800 flex items-center justify-center p-6 transition-colors duration-200">
                        <img src="https://via.placeholder.com/200x280/FDF2F8/DB2777?text=Tips+Estimasi" alt="E-Book Tips Estimasi" class="h-full object-contain shadow-lg">
                    </div>
                    <div class="p-6 flex-1 flex flex-col">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 transition-colors duration-200">Tips dan Trik Estimasi Proyek</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 flex-1 transition-colors duration-200">
                            Kumpulan tips dan trik dari para ahli untuk estimasi proyek yang lebih akurat.
                        </p>
                        <div class="flex items-center justify-between mt-auto">
                            <span class="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">64 halaman</span>
                            <a href="#" class="text-white bg-light-accent dark:bg-dark-accent font-medium px-4 py-2 rounded-lg hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                                Unduh PDF
                            </a>
                        </div>
                    </div>
                </div>

                <!-- E-Book 4 -->
                <div class="bg-white dark:bg-dark-card rounded-lg overflow-hidden shadow-md border border-gray-200 dark:border-gray-700 flex flex-col transition-colors duration-200">
                    <div class="h-56 bg-gray-100 dark:bg-gray-800 flex items-center justify-center p-6 transition-colors duration-200">
                        <img src="https://via.placeholder.com/200x280/ECFDF5/059669?text=Template+RAB" alt="E-Book Template RAB" class="h-full object-contain shadow-lg">
                    </div>
                    <div class="p-6 flex-1 flex flex-col">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 transition-colors duration-200">Template RAB Siap Pakai</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 flex-1 transition-colors duration-200">
                            Kumpulan template RAB yang dapat Anda gunakan untuk berbagai jenis proyek konstruksi.
                        </p>
                        <div class="flex items-center justify-between mt-auto">
                            <span class="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">42 halaman</span>
                            <a href="#" class="text-white bg-light-accent dark:bg-dark-accent font-medium px-4 py-2 rounded-lg hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                                Unduh PDF
                            </a>
                        </div>
                    </div>
                </div>

                <!-- E-Book 5 -->
                <div class="bg-white dark:bg-dark-card rounded-lg overflow-hidden shadow-md border border-gray-200 dark:border-gray-700 flex flex-col transition-colors duration-200">
                    <div class="h-56 bg-gray-100 dark:bg-gray-800 flex items-center justify-center p-6 transition-colors duration-200">
                        <img src="https://via.placeholder.com/200x280/FEF2F2/DC2626?text=Kasus+Studi" alt="E-Book Kasus Studi" class="h-full object-contain shadow-lg">
                    </div>
                    <div class="p-6 flex-1 flex flex-col">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 transition-colors duration-200">Kasus Studi Proyek Nyata</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 flex-1 transition-colors duration-200">
                            Analisis kasus proyek konstruksi nyata dan bagaimana RAB Estimator membantu keberhasilannya.
                        </p>
                        <div class="flex items-center justify-between mt-auto">
                            <span class="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">56 halaman</span>
                            <a href="#" class="text-white bg-light-accent dark:bg-dark-accent font-medium px-4 py-2 rounded-lg hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                                Unduh PDF
                            </a>
                        </div>
                    </div>
                </div>

                <!-- E-Book 6 -->
                <div class="bg-white dark:bg-dark-card rounded-lg overflow-hidden shadow-md border border-gray-200 dark:border-gray-700 flex flex-col transition-colors duration-200">
                    <div class="h-56 bg-gray-100 dark:bg-gray-800 flex items-center justify-center p-6 transition-colors duration-200">
                        <img src="https://via.placeholder.com/200x280/F0F9FF/0369A1?text=Panduan+Pemula" alt="E-Book Panduan Pemula" class="h-full object-contain shadow-lg">
                    </div>
                    <div class="p-6 flex-1 flex flex-col">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 transition-colors duration-200">Panduan Pemula: RAB dari Nol</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 flex-1 transition-colors duration-200">
                            Panduan langkah demi langkah membuat RAB dari nol hingga menjadi dokumen profesional.
                        </p>
                        <div class="flex items-center justify-between mt-auto">
                            <span class="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">85 halaman</span>
                            <a href="#" class="text-white bg-light-accent dark:bg-dark-accent font-medium px-4 py-2 rounded-lg hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                                Unduh PDF
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-16 bg-white dark:bg-dark-bg-secondary transition-colors duration-200">
        <div class="max-w-screen-xl mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4 transition-colors duration-200">Pertanyaan Umum</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto transition-colors duration-200">
                    Jawaban atas pertanyaan yang sering diajukan tentang penggunaan RAB Estimator
                </p>
            </div>

            <div class="max-w-3xl mx-auto space-y-4">
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden transition-colors duration-200">
                    <button class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-900 dark:text-white bg-white dark:bg-dark-card transition-colors duration-200" data-accordion-target="#faq-1" aria-expanded="true" aria-controls="faq-1">
                        <span>Bagaimana cara membuat proyek baru di RAB Estimator?</span>
                        <svg class="w-6 h-6 rotate-180 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="faq-1" class="p-5 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-card transition-colors duration-200">
                        <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">
                            Untuk membuat proyek baru, login ke akun Anda, klik tombol "Proyek Baru" pada dashboard, isi informasi dasar proyek seperti nama, lokasi, dan tanggal, kemudian klik "Simpan". Anda akan langsung diarahkan ke halaman RAB proyek tersebut untuk mulai menambahkan item pekerjaan.
                        </p>
                    </div>
                </div>

                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden transition-colors duration-200">
                    <button class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-900 dark:text-white bg-white dark:bg-dark-card transition-colors duration-200" data-accordion-target="#faq-2" aria-expanded="false" aria-controls="faq-2">
                        <span>Apakah saya dapat mengimpor data dari Excel?</span>
                        <svg class="w-6 h-6 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="faq-2" class="hidden p-5 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-card transition-colors duration-200">
                        <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">
                            Ya, RAB Estimator mendukung impor data dari file Excel. Buka proyek yang ingin Anda tambahkan data, klik menu "Impor" dan pilih "Dari Excel". Pastikan format excel Anda sesuai dengan template yang disediakan, yang dapat diunduh dari halaman yang sama.
                        </p>
                    </div>
                </div>

                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden transition-colors duration-200">
                    <button class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-900 dark:text-white bg-white dark:bg-dark-card transition-colors duration-200" data-accordion-target="#faq-3" aria-expanded="false" aria-controls="faq-3">
                        <span>Bagaimana cara menambahkan AHSP kustom?</span>
                        <svg class="w-6 h-6 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="faq-3" class="hidden p-5 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-card transition-colors duration-200">
                        <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">
                            Untuk menambahkan AHSP kustom, buka menu "AHSP", klik "Tambah AHSP", isi informasi yang diperlukan seperti nama, satuan, dan deskripsi. Kemudian tambahkan komponen upah, bahan, dan alat yang diperlukan beserta koefisiennya. Setelah selesai, klik "Simpan" untuk menyimpan AHSP kustom Anda.
                        </p>
                    </div>
                </div>

                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden transition-colors duration-200">
                    <button class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-900 dark:text-white bg-white dark:bg-dark-card transition-colors duration-200" data-accordion-target="#faq-4" aria-expanded="false" aria-controls="faq-4">
                        <span>Apakah RAB Estimator bisa digunakan secara offline?</span>
                        <svg class="w-6 h-6 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="faq-4" class="hidden p-5 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-card transition-colors duration-200">
                        <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">
                            Saat ini, RAB Estimator adalah aplikasi berbasis web yang memerlukan koneksi internet untuk mengakses seluruh fitur. Namun, kami memiliki fitur "Mode Offline" terbatas yang memungkinkan Anda tetap bekerja dengan data yang telah disinkronkan sebelumnya, yang akan otomatis disinkronkan kembali ketika Anda online.
                        </p>
                    </div>
                </div>

                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden transition-colors duration-200">
                    <button class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-900 dark:text-white bg-white dark:bg-dark-card transition-colors duration-200" data-accordion-target="#faq-5" aria-expanded="false" aria-controls="faq-5">
                        <span>Bagaimana cara berbagi proyek dengan rekan kerja?</span>
                        <svg class="w-6 h-6 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="faq-5" class="hidden p-5 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-card transition-colors duration-200">
                        <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">
                            Untuk berbagi proyek, buka proyek yang ingin dibagikan, klik tombol "Bagikan" di pojok kanan atas, masukkan alamat email rekan kerja Anda dan pilih level akses (hanya lihat atau edit). Mereka akan menerima email undangan untuk bergabung dengan proyek Anda. Pastikan mereka sudah memiliki akun RAB Estimator.
                        </p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <p class="text-gray-600 dark:text-gray-300 mb-4 transition-colors duration-200">Masih memiliki pertanyaan?</p>
                <a href="#" class="inline-block bg-light-accent dark:bg-dark-accent text-white font-bold py-2 px-6 rounded-lg hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all">
                    Hubungi Tim Dukungan
                </a>


            </div>
        </div>
    </section>
@endsection

@section('scripts')
<script>
    // Simple accordion functionality
    document.addEventListener('DOMContentLoaded', function() {
        const accordionButtons = document.querySelectorAll('[data-accordion-target]');

        accordionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-accordion-target');
                const target = document.querySelector(targetId);
                const isExpanded = this.getAttribute('aria-expanded') === 'true';

                // Toggle the content visibility
                if (isExpanded) {
                    target.classList.add('hidden');
                    this.setAttribute('aria-expanded', 'false');
                    this.querySelector('svg').classList.remove('rotate-180');
                } else {
                    target.classList.remove('hidden');
                    this.setAttribute('aria-expanded', 'true');
                    this.querySelector('svg').classList.add('rotate-180');
                }
            });
        });
    });
</script>
@endsection