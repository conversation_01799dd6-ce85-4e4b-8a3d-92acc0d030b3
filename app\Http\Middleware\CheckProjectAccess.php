<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Project;
use App\Models\ProjectShare;
use Symfony\Component\HttpFoundation\Response;

class CheckProjectAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get project ID from route parameter or request
        $projectId = $request->route('project') ? $request->route('project')->id : $request->input('project_id');

        if (!$projectId) {
            return redirect()->route('proyek')->with('error', 'Proyek tidak ditemukan.');
        }

        // Get the project
        $project = Project::find($projectId);

        if (!$project) {
            return redirect()->route('proyek')->with('error', 'Proyek tidak ditemukan.');
        }

        // Check if user is the owner of the project
        if ($project->user_id === Auth::id()) {
            // User is the owner, allow access
            return $next($request);
        }

        // Check if project is shared with the user
        $share = ProjectShare::where('project_id', $project->id)
            ->where('user_id', Auth::id())
            ->first();

        if (!$share) {
            return redirect()->route('proyek')->with('error', 'Anda tidak memiliki akses ke proyek ini.');
        }

        // Check if user has editor access or just readonly
        if ($request->isMethod('get')) {
            // For GET requests, allow both editor and readonly access
            return $next($request);
        } else {
            // For non-GET requests (POST, PUT, DELETE), only allow editor access
            if ($share->access_type === 'editor') {
                return $next($request);
            } else {
                return redirect()->route('proyek')->with('error', 'Anda hanya memiliki akses baca ke proyek ini.');
            }
        }
    }
}
