@extends('visitor.koleksi.layout')

@section('collection-title', 'Detail AHSP')
@section('collection-description', 'Informasi detail tentang analisa harga satuan pekerjaan.')

@section('collection-content')
    <div class="mb-8">
        <a href="{{ route('visitor.koleksi.ahsp') }}" class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:underline">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Kembali ke Daftar AHSP
        </a>
    </div>

    <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gradient-to-r from-light-navbar via-[#0C4A7A] to-[#083D66] dark:from-blue-900 dark:to-blue-950 text-white p-6">
            <h2 class="text-2xl font-bold">{{ $ahsp->judul }}</h2>
            <div class="flex flex-wrap gap-4 mt-4">
                <div class="bg-white/10 dark:bg-dark-accent/20 px-3 py-1 rounded-full">
                    <span class="text-sm font-medium">Kode: {{ $ahsp->kode }}</span>
                </div>
                <div class="bg-white/10 dark:bg-dark-accent/20 px-3 py-1 rounded-full">
                    <span class="text-sm font-medium">Satuan: {{ $ahsp->satuan }}</span>
                </div>
                <div class="bg-white/10 dark:bg-dark-accent/20 px-3 py-1 rounded-full">
                    <span class="text-sm font-medium">Overhead: {{ $ahsp->overhead }}%</span>
                </div>
            </div>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 gap-6">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Informasi AHSP</h3>
                    <div class="text-right">
                        <p class="text-sm text-gray-500 dark:text-gray-400">Harga Total</p>
                        <p class="text-xl font-bold text-gray-900 dark:text-white">Rp {{ number_format($ahsp->grand_total, 2) }}</p>
                    </div>
                </div>

                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-800">
                                <tr>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">No</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Uraian</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Satuan</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Koefisien</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Harga Satuan</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Jumlah Harga</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                                <!-- A. Tenaga Kerja -->
                                <tr class="bg-gray-100 dark:bg-gray-700">
                                    <td colspan="6" class="px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white">
                                        A. Tenaga Kerja
                                    </td>
                                </tr>

                                @if(count($upah) > 0)
                                    @foreach($upah as $index => $item)
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ $index + 1 }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $item->item_text }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ $item->satuan }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ $item->koefisien }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            Rp {{ number_format($item->harga_dasar, 2) }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            Rp {{ number_format($item->harga_satuan, 2) }}
                                        </td>
                                    </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="6" class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 italic">
                                            Tidak ada data tenaga kerja.
                                        </td>
                                    </tr>
                                @endif

                                <!-- B. Bahan -->
                                <tr class="bg-gray-100 dark:bg-gray-700">
                                    <td colspan="6" class="px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white">
                                        B. Bahan
                                    </td>
                                </tr>

                                @if(count($bahan) > 0)
                                    @foreach($bahan as $index => $item)
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ $index + 1 }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $item->item_text }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ $item->satuan }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ $item->koefisien }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            Rp {{ number_format($item->harga_dasar, 2) }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            Rp {{ number_format($item->harga_satuan, 2) }}
                                        </td>
                                    </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="6" class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 italic">
                                            Tidak ada data bahan.
                                        </td>
                                    </tr>
                                @endif

                                <!-- C. Peralatan -->
                                <tr class="bg-gray-100 dark:bg-gray-700">
                                    <td colspan="6" class="px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white">
                                        C. Peralatan
                                    </td>
                                </tr>

                                @if(count($alat) > 0)
                                    @foreach($alat as $index => $item)
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ $index + 1 }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $item->item_text }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ $item->satuan }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ $item->koefisien }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            Rp {{ number_format($item->harga_dasar, 2) }}
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            Rp {{ number_format($item->harga_satuan, 2) }}
                                        </td>
                                    </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="6" class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 italic">
                                            Tidak ada data peralatan.
                                        </td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-600 dark:text-gray-300">Jumlah Harga (A+B+C)</span>
                            @php
                                $subtotal = 0;
                                foreach ($upah as $item) {
                                    $subtotal += $item->harga_satuan;
                                }
                                foreach ($bahan as $item) {
                                    $subtotal += $item->harga_satuan;
                                }
                                foreach ($alat as $item) {
                                    $subtotal += $item->harga_satuan;
                                }
                            @endphp
                            <span class="text-gray-900 dark:text-white font-medium">Rp {{ number_format($subtotal, 2) }}</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-600 dark:text-gray-300">Overhead ({{ $ahsp->overhead }}%)</span>
                            @php
                                $overhead = $subtotal * ($ahsp->overhead / 100);
                            @endphp
                            <span class="text-gray-900 dark:text-white font-medium">Rp {{ number_format($overhead, 2) }}</span>
                        </div>
                        <div class="flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-700">
                            <span class="text-gray-900 dark:text-white font-semibold">Harga Satuan Pekerjaan</span>
                            <span class="text-gray-900 dark:text-white font-bold">Rp {{ number_format($ahsp->grand_total, 2) }}</span>
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-2">Informasi Tambahan</h4>
                    <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-2">
                        <li>Sumber: {{ $ahsp->sumber ?: 'Tidak ada' }}</li>
                        <li>Data terakhir diperbarui: {{ $ahsp->updated_at->format('d F Y') }}</li>
                        <li>Harga sudah termasuk overhead {{ $ahsp->overhead }}%</li>
                        <li>Harga belum termasuk pajak</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
@endsection
