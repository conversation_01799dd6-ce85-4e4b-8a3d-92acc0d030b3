@extends('layouts.app')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2">Bagikan Proyek</h1>
                <p class="text-gray-600 dark:text-gray-400">Bagikan proyek "{{ $project->name }}" dengan pengguna lain</p>
            </div>
            <div>
                <a href="{{ route('proyek') }}"
                    class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                    <i class="fas fa-arrow-left mr-1"></i> Kembali
                </a>
            </div>
        </div>

        @if (session('success'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded dark:bg-green-900/30 dark:text-green-400"
                role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif

        @if (session('error'))
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400"
                role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Informasi Proyek -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
                    <div class="bg-blue-600 dark:bg-blue-700 p-4">
                        <h2 class="text-white text-lg font-semibold">Informasi Proyek</h2>
                    </div>

                    <div class="p-6">
                        <div class="mb-4">
                            <h3 class="font-semibold text-light-text dark:text-dark-text mb-2">Detail Proyek</h3>
                            <div class="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                                <div class="flex justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">Nama Proyek:</span>
                                    <span class="font-medium text-light-text dark:text-dark-text">{{ $project->name }}</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">Pemilik Proyek:</span>
                                    <span class="font-medium text-light-text dark:text-dark-text">{{ $project->project_owner }}</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">Lokasi:</span>
                                    <span class="font-medium text-light-text dark:text-dark-text">{{ $project->province }}, {{ $project->city }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Tahun:</span>
                                    <span class="font-medium text-light-text dark:text-dark-text">{{ $project->year }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h3 class="font-semibold text-light-text dark:text-dark-text mb-2">Batasan Berbagi</h3>
                            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-100 dark:border-blue-800">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-500 dark:text-blue-400 mt-1 mr-2"></i>
                                    <div>
                                        <p class="text-sm text-blue-700 dark:text-blue-300">
                                            Berdasarkan paket langganan Anda, Anda dapat berbagi proyek ini dengan maksimal <strong>{{ $remainingShares + count($shares) }}</strong> pengguna.
                                        </p>
                                        <p class="text-sm text-blue-700 dark:text-blue-300 mt-2">
                                            Sisa kuota berbagi: <strong>{{ $remainingShares }}</strong> pengguna
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Berbagi dan Daftar Pengguna yang Berbagi -->
            <div class="lg:col-span-2">
                <!-- Form Berbagi -->
                @if($remainingShares > 0 && count($users) > 0)
                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200 mb-6">
                    <div class="bg-blue-600 dark:bg-blue-700 p-4">
                        <h2 class="text-white text-lg font-semibold">Bagikan dengan Pengguna Baru</h2>
                    </div>

                    <div class="p-6">
                        <form action="{{ route('projects.share.store', $project->id) }}" method="POST">
                            @csrf
                            <div class="mb-4">
                                <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Pilih Pengguna</label>
                                <select id="user_id" name="user_id" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-200">
                                    <option value="">-- Pilih Pengguna --</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Peran Akses</label>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <input type="radio" id="role_editor" name="role" value="editor" checked
                                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="role_editor" class="ml-2 text-light-text dark:text-dark-text">
                                            <span class="font-medium">Editor</span> - Dapat mengedit proyek (akses penuh)
                                        </label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="radio" id="role_viewer" name="role" value="viewer"
                                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="role_viewer" class="ml-2 text-light-text dark:text-dark-text">
                                            <span class="font-medium">Viewer</span> - Hanya dapat melihat proyek (tidak dapat mengedit)
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <button type="submit"
                                class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                                <i class="fas fa-share-alt mr-1"></i> Bagikan Proyek
                            </button>
                        </form>
                    </div>
                </div>
                @elseif($remainingShares <= 0)
                <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6 rounded dark:bg-yellow-900/30 dark:text-yellow-400">
                    <p class="font-medium">Batas berbagi tercapai</p>
                    <p class="mt-1">Anda telah mencapai batas maksimum pengguna yang dapat berbagi proyek ini berdasarkan paket langganan Anda. Upgrade paket langganan Anda untuk berbagi dengan lebih banyak pengguna.</p>
                </div>
                @elseif(count($users) <= 0)
                <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6 rounded dark:bg-yellow-900/30 dark:text-yellow-400">
                    <p class="font-medium">Tidak ada pengguna yang tersedia</p>
                    <p class="mt-1">Tidak ada pengguna lain yang tersedia untuk berbagi proyek ini. Semua pengguna yang memenuhi syarat sudah memiliki akses ke proyek ini.</p>
                </div>
                @endif

                <!-- Daftar Pengguna yang Berbagi -->
                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
                    <div class="bg-blue-600 dark:bg-blue-700 p-4">
                        <h2 class="text-white text-lg font-semibold">Pengguna yang Memiliki Akses</h2>
                    </div>

                    <div class="p-6">
                        @if(count($shares) > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Pengguna</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Peran</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                        @foreach($shares as $share)
                                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-200">
                                                    {{ $share->user->name }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                    {{ $share->user->email }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                    @if($share->role === 'editor')
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                                            Editor
                                                        </span>
                                                    @else
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                                            Viewer
                                                        </span>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div class="flex space-x-2">
                                                        <form action="{{ route('projects.share.update', ['project' => $project->id, 'shareId' => $share->id]) }}" method="POST" class="inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <input type="hidden" name="role" value="{{ $share->role === 'editor' ? 'viewer' : 'editor' }}">
                                                            <button type="submit" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                                                <i class="fas fa-exchange-alt"></i> Ubah Peran
                                                            </button>
                                                        </form>
                                                        
                                                        <button type="button" 
                                                            onclick="confirmDelete('{{ route('projects.share.remove', ['project' => $project->id, 'shareId' => $share->id]) }}')"
                                                            data-user-name="{{ $share->user->name }}"
                                                            class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                                            <i class="fas fa-trash-alt"></i> Hapus
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-6 text-gray-500 dark:text-gray-400">
                                <i class="fas fa-users text-4xl mb-3"></i>
                                <p>Belum ada pengguna yang memiliki akses ke proyek ini.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmationModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden overflow-y-auto py-8 text-sm">
        <div class="bg-white dark:bg-dark-card p-0 rounded-lg shadow-xl max-w-md w-full mx-auto my-auto border-2 border-light-accent dark:border-dark-accent">
            <!-- Header -->
            <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10 rounded-t-lg">
                <h3 class="text-white dark:text-dark-text text-lg font-semibold">Konfirmasi Hapus</h3>
            </div>
            
            <!-- Content -->
            <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
                <p class="text-gray-600 dark:text-gray-400 mb-6">Apakah Anda yakin ingin menghapus akses pengguna ini dari proyek?</p>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeConfirmationModal()" 
                        class="bg-gray-500 hover:bg-gray-700 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150">
                        Batal
                    </button>
                    <form id="deleteForm" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                            class="bg-red-600 hover:bg-red-700 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150 shadow-sm hover:shadow transform hover:scale-105">
                            Hapus
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function confirmDelete(url) {
            document.getElementById('deleteForm').action = url;
            document.getElementById('confirmationModal').classList.remove('hidden');
        }

        function closeConfirmationModal() {
            document.getElementById('confirmationModal').classList.add('hidden');
        }
    </script>
@endsection
