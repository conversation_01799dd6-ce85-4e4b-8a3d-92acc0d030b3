@extends('visitor.koleksi.layout')

@section('collection-title', 'Koleksi Data')
@section('collection-description', 'Lihat koleksi data upah, bahan, alat, dan AHSP yang tersedia di RAB Estimator.')

@section('collection-content')
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Koleksi Upah -->
        <div class="collection-card">
            <div class="collection-card-header">
                <h2 class="text-xl font-bold">Koleksi Upah</h2>
            </div>
            <div class="collection-card-body">
                <p class="text-gray-600 dark:text-gray-300 mb-4">Daftar harga satuan upah tenaga kerja untuk berbagai jenis pekerjaan konstruksi.</p>
                <div class="flex items-center text-gray-500 dark:text-gray-400 mb-2">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Tenaga Kerja</span>
                </div>
                <div class="flex items-center text-gray-500 dark:text-gray-400 mb-2">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Harga Satuan</span>
                </div>
            </div>
            <div class="collection-card-footer">
                <a href="{{ route('visitor.koleksi.upah') }}" class="view-btn block text-center">Lihat Koleksi</a>
            </div>
        </div>

        <!-- Koleksi Bahan -->
        <div class="collection-card">
            <div class="collection-card-header">
                <h2 class="text-xl font-bold">Koleksi Bahan</h2>
            </div>
            <div class="collection-card-body">
                <p class="text-gray-600 dark:text-gray-300 mb-4">Daftar harga satuan bahan material untuk berbagai jenis pekerjaan konstruksi.</p>
                <div class="flex items-center text-gray-500 dark:text-gray-400 mb-2">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Material Konstruksi</span>
                </div>
                <div class="flex items-center text-gray-500 dark:text-gray-400 mb-2">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Harga Satuan</span>
                </div>
            </div>
            <div class="collection-card-footer">
                <a href="{{ route('visitor.koleksi.bahan') }}" class="view-btn block text-center">Lihat Koleksi</a>
            </div>
        </div>

        <!-- Koleksi Alat -->
        <div class="collection-card">
            <div class="collection-card-header">
                <h2 class="text-xl font-bold">Koleksi Alat</h2>
            </div>
            <div class="collection-card-body">
                <p class="text-gray-600 dark:text-gray-300 mb-4">Daftar harga satuan alat untuk berbagai jenis pekerjaan konstruksi.</p>
                <div class="flex items-center text-gray-500 dark:text-gray-400 mb-2">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Peralatan Konstruksi</span>
                </div>
                <div class="flex items-center text-gray-500 dark:text-gray-400 mb-2">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Harga Satuan</span>
                </div>
            </div>
            <div class="collection-card-footer">
                <a href="{{ route('visitor.koleksi.alat') }}" class="view-btn block text-center">Lihat Koleksi</a>
            </div>
        </div>

        <!-- Koleksi AHSP -->
        <div class="collection-card">
            <div class="collection-card-header">
                <h2 class="text-xl font-bold">Koleksi AHSP</h2>
            </div>
            <div class="collection-card-body">
                <p class="text-gray-600 dark:text-gray-300 mb-4">Daftar analisa harga satuan pekerjaan untuk berbagai jenis pekerjaan konstruksi.</p>
                <div class="flex items-center text-gray-500 dark:text-gray-400 mb-2">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Analisa Harga Satuan</span>
                </div>
                <div class="flex items-center text-gray-500 dark:text-gray-400 mb-2">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Perhitungan Biaya</span>
                </div>
            </div>
            <div class="collection-card-footer">
                <a href="{{ route('visitor.koleksi.ahsp') }}" class="view-btn block text-center">Lihat Koleksi</a>
            </div>
        </div>
    </div>
@endsection
