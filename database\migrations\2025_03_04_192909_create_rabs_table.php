<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('rabs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->string('judul');
            $table->decimal('jumlah_harga', 15, 2)->nullable();
            $table->decimal('total_harga', 15, 2)->nullable();
            $table->decimal('dibulatkan', 15, 2)->nullable();
            $table->string('uraian_pekerjaan')->nullable();
            $table->decimal('volume', 10, 2)->nullable();
            $table->string('satuan')->nullable();
            $table->decimal('harga_satuan', 15, 2)->nullable();
            $table->foreignId('ahs_id')->nullable()->references('id')->on('ahs')->onDelete('set null');
            $table->unsignedBigInteger('proyek_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rabs');
    }
};
