<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Status Pembayaran Estimateeng</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #3182ce;
            margin-bottom: 10px;
        }

        .content {
            padding: 20px 0;
        }

        .button {
            display: inline-block;
            background-color: #3182ce;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-weight: bold;
            margin: 20px 0;
        }

        .button:hover {
            background-color: #2c5282;
        }

        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }

        .details {
            background-color: #f5f7fa;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }

        .details-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .details-label {
            font-weight: bold;
            color: #4a5568;
        }

        .status {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
            margin: 10px 0;
        }

        .status-completed {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-pending {
            background-color: #fefcbf;
            color: #744210;
        }

        .status-failed {
            background-color: #fed7d7;
            color: #822727;
        }

        .status-processing {
            background-color: #bee3f8;
            color: #2a4365;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="logo">ESTIMATEENG</div>
            <p>Aplikasi Estimasi Biaya Konstruksi</p>
        </div>

        <div class="content">
            <h2>Halo {{ $user->name }},</h2>

            @if ($payment->status === 'completed')
                <p>Pembayaran Anda telah <strong>berhasil</strong> diverifikasi. Langganan Anda sekarang aktif.</p>
                <div class="status status-completed">Pembayaran Berhasil</div>
            @elseif ($payment->status === 'pending')
                <p>Kami telah menerima permintaan pembayaran Anda. Silakan selesaikan pembayaran untuk mengaktifkan
                    langganan Anda.</p>
                <div class="status status-pending">Menunggu Pembayaran</div>
            @elseif ($payment->status === 'failed')
                <p>Mohon maaf, pembayaran Anda <strong>gagal</strong> diproses. Silakan coba lagi atau hubungi tim
                    dukungan kami.</p>
                <div class="status status-failed">Pembayaran Gagal</div>
            @elseif ($payment->status === 'processing')
                <p>Pembayaran Anda sedang <strong>diproses</strong>. Kami akan memberi tahu Anda segera setelah
                    verifikasi selesai.</p>
                <div class="status status-processing">Sedang Diproses</div>
            @endif

            <p>Berikut adalah detail pembayaran Anda:</p>

            <div class="details">
                <div class="details-row">
                    <span class="details-label">No. Invoice:</span>
                    <span>{{ $payment->invoice_number }}</span>
                </div>
                <div class="details-row">
                    <span class="details-label">Tanggal:</span>
                    <span>{{ $payment->created_at->format('d F Y H:i') }}</span>
                </div>
                <div class="details-row">
                    <span class="details-label">Jumlah:</span>
                    <span>Rp {{ number_format($payment->amount, 0, ',', '.') }}</span>
                </div>
                <div class="details-row">
                    <span class="details-label">Metode Pembayaran:</span>
                    <span>{{ ucfirst($payment->payment_method) }}</span>
                </div>
                @if ($payment->subscription && $payment->subscription->plan)
                    <div class="details-row">
                        <span class="details-label">Paket:</span>
                        <span>{{ $payment->subscription->plan->name }}</span>
                    </div>
                    <div class="details-row">
                        <span class="details-label">Durasi:</span>
                        <span>{{ $payment->subscription->plan->duration_in_days }} hari</span>
                    </div>
                    <div class="details-row">
                        <span class="details-label">Tanggal Mulai:</span>
                        <span>{{ $payment->subscription->start_date->format('d F Y') }}</span>
                    </div>
                    <div class="details-row">
                        <span class="details-label">Tanggal Berakhir:</span>
                        <span>{{ $payment->subscription->end_date->format('d F Y') }}</span>
                    </div>
                @endif
            </div>

            <div style="text-align: center;">
                <a href="{{ route('payments.show', $payment->id) }}" class="button">Lihat Detail Pembayaran</a>
            </div>

            @if ($payment->status === 'completed')
                <p>Terima kasih telah berlangganan Estimateeng. Anda sekarang dapat menikmati semua fitur premium kami.
                </p>
            @elseif ($payment->status === 'pending')
                <p>Jika Anda sudah melakukan pembayaran, mohon tunggu beberapa saat untuk verifikasi. Jika Anda belum
                    melakukan pembayaran, silakan klik tombol di atas untuk melanjutkan proses pembayaran.</p>
            @elseif ($payment->status === 'failed')
                <p>Jika Anda mengalami masalah dengan pembayaran, silakan hubungi tim dukungan kami untuk bantuan lebih
                    lanjut.</p>
            @endif

            <p>Salam,<br>Tim Estimateeng</p>
        </div>

        <div class="footer">
            <p>&copy; {{ date('Y') }} Estimateeng. Semua hak dilindungi.</p>
            <p>Jika Anda tidak merasa melakukan pendaftaran di Estimateeng, silakan abaikan email ini.</p>
        </div>
    </div>
</body>

</html>
