<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Models\User;

class ProfileController extends Controller
{
    /**
     * Reset user profile photo
     */
    public function resetPhoto()
    {
        $user = User::find(Auth::id());

        // Delete old photo if exists
        if ($user->photo) {
            Storage::disk('public')->delete($user->photo);
            $user->photo = null;
            $user->save();
        }

        return response()->json([
            'success' => true,
            'message' => 'Foto profil berhasil dihapus',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'photo' => null,
                'address' => $user->address,
                'phone' => $user->phone,
            ]
        ]);
    }

    /**
     * Get user profile data
     */
    public function getProfile()
    {
        $user = Auth::user();
        return response()->json([
            'success' => true,
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'photo' => $user->photo ? asset('storage/' . $user->photo) : null,
                'address' => $user->address,
                'phone' => $user->phone,
            ]
        ]);
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $user = User::find(Auth::id());

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],
            'password' => 'nullable|string|min:8',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'reset_photo' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        // Update basic info
        $user->name = $request->name;
        $user->email = $request->email;
        $user->address = $request->address;
        $user->phone = $request->phone;

        // Update password if provided
        if ($request->filled('password')) {
            $user->password = Hash::make($request->password);
        }

        // Handle reset photo flag
        if ($request->has('reset_photo') && $request->reset_photo === 'true') {
            // Delete photo if exists
            if ($user->photo) {
                Storage::disk('public')->delete($user->photo);
                $user->photo = null;
            }
        }
        // Handle photo upload (only if not resetting photo)
        else if ($request->hasFile('photo')) {
            try {
                // Delete old photo if exists
                if ($user->photo) {
                    Storage::disk('public')->delete($user->photo);
                }

                // Make sure the directory exists
                $directory = 'profile-photos';
                if (!Storage::disk('public')->exists($directory)) {
                    Storage::disk('public')->makeDirectory($directory);
                }

                // Store new photo with a unique name
                $file = $request->file('photo');
                $filename = uniqid() . '_' . time() . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs($directory, $filename, 'public');

                // Check if the file was stored successfully
                if (!Storage::disk('public')->exists($path)) {
                    throw new \Exception('Failed to store the photo');
                }

                $user->photo = $path;
            } catch (\Exception $e) {
                // Log the error
                Log::error('Error uploading profile photo: ' . $e->getMessage());

                // Return error response
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal mengunggah foto profil: ' . $e->getMessage()
                ], 500);
            }
        }

        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Profil berhasil diperbarui',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'photo' => $user->photo ? asset('storage/' . $user->photo) : null,
                'address' => $user->address,
                'phone' => $user->phone,
            ]
        ]);
    }
}
