@extends('layouts.visitor')

@section('title', 'Paket <PERSON>')

@section('content')
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-light-navbar via-[#0C4A7A] to-[#083D66] dark:from-blue-900 dark:to-indigo-900 text-white transition-colors duration-200">
        <div class="max-w-screen-xl mx-auto px-4 pt-32 pb-20 md:pt-32 md:pb-28">
            <div class="max-w-3xl">
                <h1 class="text-3xl md:text-4xl font-extrabold mb-6">
                    <PERSON><PERSON> Berlangganan RAB Estimator
                </h1>
                <p class="text-lg mb-4">
                    Pilih paket yang sesuai dengan kebutuhan bisnis konstruksi Anda
                </p>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-16 bg-white dark:bg-dark-bg-secondary transition-colors duration-200">
        <div class="max-w-screen-xl mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4 transition-colors duration-200">Pilihan Paket</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto transition-colors duration-200">
                    Kami menawarkan berbagai paket berlangganan yang dapat disesuaikan dengan kebutuhan dan anggaran Anda
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <!-- Paket Standar -->
                <div class="bg-white dark:bg-dark-card rounded-lg border border-gray-200 dark:border-gray-700 shadow-md overflow-hidden hover:shadow-lg transition-all">
                    <div class="p-6 bg-blue-50 dark:bg-blue-900/30 transition-colors duration-200">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 transition-colors duration-200">Paket Standar</h3>
                        <div class="flex items-baseline gap-1">
                            <span class="text-3xl font-bold text-blue-600 dark:text-blue-400 transition-colors duration-200">Rp 99.000</span>
                            <span class="text-gray-500 dark:text-gray-400 transition-colors duration-200">/bulan</span>
                        </div>
                        <p class="mt-2 text-gray-600 dark:text-gray-300 transition-colors duration-200">Cocok untuk kontraktor perorangan dan bisnis kecil</p>
                    </div>
                    <div class="p-6 dark:bg-dark-card transition-colors duration-200">
                        <ul class="space-y-4 mb-6">
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Maksimal 10 proyek aktif</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Akses database AHSP standar</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Ekspor ke PDF & Excel</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Template RAB dasar</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-500 dark:text-gray-400 transition-colors duration-200">Kolaborasi tim (hingga 5 pengguna)</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-red-600 dark:text-red-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                <span class="text-gray-500 dark:text-gray-400 transition-colors duration-200">AHSP kustom tak terbatas</span>
                            </li>
                        </ul>
                        <a href="#" class="block w-full text-center bg-light-accent dark:bg-dark-accent text-white font-bold py-3 px-6 rounded-lg hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all">
                            Pilih Paket
                        </a>
                    </div>
                </div>

                <!-- Paket PRO -->
                <div class="bg-white dark:bg-dark-card rounded-lg border-2 border-blue-600 dark:border-blue-500 shadow-xl overflow-hidden hover:shadow-2xl transition-all relative">
                    <div class="absolute top-0 right-0 bg-light-accent dark:bg-dark-accent text-white px-4 py-1 rounded-bl-lg text-sm font-bold transition-colors duration-200">
                        Paling Populer
                    </div>
                    <div class="p-6 bg-blue-50 dark:bg-blue-900/30 transition-colors duration-200">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 transition-colors duration-200">Paket PRO</h3>
                        <div class="flex items-baseline gap-1">
                            <span class="text-3xl font-bold text-blue-600 dark:text-blue-400 transition-colors duration-200">Rp 249.000</span>
                            <span class="text-gray-500 dark:text-gray-400 transition-colors duration-200">/bulan</span>
                        </div>
                        <p class="mt-2 text-gray-600 dark:text-gray-300 transition-colors duration-200">Ideal untuk kontraktor dan bisnis menengah</p>
                    </div>
                    <div class="p-6 dark:bg-dark-card transition-colors duration-200">
                        <ul class="space-y-4 mb-6">
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Maksimal 20 proyek aktif</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Akses database AHSP lengkap</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Ekspor ke PDF, Excel, dan Excel dengan Formula</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Semua template RAB</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Kolaborasi tim (hingga 20 pengguna)</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">AHSP kustom (100 item)</span>
                            </li>
                        </ul>
                        <a href="#" class="block w-full text-center bg-light-accent dark:bg-dark-accent text-white font-bold py-3 px-6 rounded-lg hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all">
                            Pilih Paket
                        </a>
                    </div>
                </div>

                <!-- Paket Full -->
                <div class="bg-white dark:bg-dark-card rounded-lg border border-gray-200 dark:border-gray-700 shadow-md overflow-hidden hover:shadow-lg transition-all">
                    <div class="p-6 bg-blue-50 dark:bg-blue-900/30 transition-colors duration-200">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 transition-colors duration-200">Paket Enterprise</h3>
                        <div class="flex items-baseline gap-1">
                            <span class="text-3xl font-bold text-blue-600 dark:text-blue-400 transition-colors duration-200">Rp 499.000</span>
                            <span class="text-gray-500 dark:text-gray-400 transition-colors duration-200">/bulan</span>
                        </div>
                        <p class="mt-2 text-gray-600 dark:text-gray-300 transition-colors duration-200">Untuk perusahaan dan proyek besar</p>
                    </div>
                    <div class="p-6 dark:bg-dark-card transition-colors duration-200">
                        <ul class="space-y-4 mb-6">
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Proyek tak terbatas</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Akses database AHSP premium</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Ekspor ke PDF, Excel, dan Excel dengan Formula</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Semua template RAB + kustom</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">Kolaborasi tim tak terbatas</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 shrink-0 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300 transition-colors duration-200">AHSP kustom tak terbatas</span>
                            </li>
                        </ul>
                        <a href="#" class="block w-full text-center bg-light-accent dark:bg-dark-accent text-white font-bold py-3 px-6 rounded-lg hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all">
                            Pilih Paket
                        </a>
                    </div>
                </div>
            </div>

            <div class="text-center mt-10 text-gray-600 dark:text-gray-300 transition-colors duration-200">
                <p>Butuh paket khusus untuk bisnis Anda?</p>
                <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline font-medium transition-colors duration-200">Hubungi Tim Sales Kami</a>
            </div>
        </div>
    </section>

    <!-- Features Comparison -->
    <section class="py-16 bg-gray-50 dark:bg-dark-bg transition-colors duration-200">
        <div class="max-w-screen-xl mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4 transition-colors duration-200">Perbandingan Fitur</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto transition-colors duration-200">
                    Bandingkan fitur yang tersedia di setiap paket untuk menemukan yang sesuai dengan kebutuhan Anda
                </p>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full bg-white dark:bg-dark-card border border-gray-200 dark:border-gray-700 rounded-lg shadow-md transition-colors duration-200">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-800 transition-colors duration-200">
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900 dark:text-white transition-colors duration-200">Fitur</th>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900 dark:text-white transition-colors duration-200">Standar</th>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-blue-600 dark:text-blue-400 transition-colors duration-200">PRO</th>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900 dark:text-white transition-colors duration-200">Enterprise</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700 transition-colors duration-200">
                        <tr class="dark:bg-dark-card transition-colors duration-200">
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white transition-colors duration-200">Jumlah Proyek Aktif</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">5</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">20</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Tak terbatas</td>
                        </tr>
                        <tr class="dark:bg-dark-card transition-colors duration-200">
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white transition-colors duration-200">Database AHSP</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Standar</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Lengkap</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Premium</td>
                        </tr>
                        <tr class="dark:bg-dark-card transition-colors duration-200">
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white transition-colors duration-200">AHSP Kustom</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">10</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">100</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Tak terbatas</td>
                        </tr>
                        <tr class="dark:bg-dark-card transition-colors duration-200">
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white transition-colors duration-200">Format Ekspor</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">PDF, Excel</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">PDF, Excel, Word</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Semua Format</td>
                        </tr>
                        <tr class="dark:bg-dark-card transition-colors duration-200">
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white transition-colors duration-200">Template RAB</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Dasar</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Semua Template</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Semua + Kustom</td>
                        </tr>
                        <tr class="dark:bg-dark-card transition-colors duration-200">
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white transition-colors duration-200">Kolaborasi Tim</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">
                                <svg class="w-5 h-5 text-red-600 dark:text-red-500 mx-auto transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">5 Pengguna</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">20 Pengguna</td>
                        </tr>
                        <tr class="dark:bg-dark-card transition-colors duration-200">
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white transition-colors duration-200">Analisis Biaya & Laporan</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Dasar</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Lanjutan</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Premium</td>
                        </tr>
                        <tr class="dark:bg-dark-card transition-colors duration-200">
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white transition-colors duration-200">Dukungan Pelanggan</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Email</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Email, Chat</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">Email, Chat, Telepon</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-16 bg-white dark:bg-dark-bg-secondary transition-colors duration-200">
        <div class="max-w-screen-xl mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4 transition-colors duration-200">Pertanyaan Umum</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto transition-colors duration-200">
                    Jawaban atas pertanyaan yang sering diajukan tentang paket berlangganan kami
                </p>
            </div>

            <div class="max-w-3xl mx-auto space-y-4">
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden transition-colors duration-200">
                    <button class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-900 dark:text-white bg-white dark:bg-dark-card transition-colors duration-200" data-accordion-target="#faq-1" aria-expanded="true" aria-controls="faq-1">
                        <span>Bisakah saya mengubah paket berlangganan kapan saja?</span>
                        <svg class="w-6 h-6 rotate-180 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="faq-1" class="p-5 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-card transition-colors duration-200">
                        <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">
                            Ya, Anda dapat mengubah paket berlangganan Anda kapan saja. Jika Anda meningkatkan paket, perbedaan harga akan diprorata berdasarkan sisa waktu berlangganan Anda. Jika Anda menurunkan paket, perubahan akan berlaku pada periode penagihan berikutnya.
                        </p>
                    </div>
                </div>

                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden transition-colors duration-200">
                    <button class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-900 dark:text-white bg-white dark:bg-dark-card transition-colors duration-200" data-accordion-target="#faq-2" aria-expanded="false" aria-controls="faq-2">
                        <span>Apa yang terjadi dengan data saya jika berlangganan berakhir?</span>
                        <svg class="w-6 h-6 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="faq-2" class="hidden p-5 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-card transition-colors duration-200">
                        <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">
                            Data Anda akan tetap tersimpan dalam sistem kami selama 90 hari setelah berlangganan berakhir. Selama periode ini, Anda dapat memperpanjang berlangganan dan mendapatkan akses kembali ke semua data Anda. Setelah 90 hari, data Anda mungkin akan dihapus dari sistem kami sesuai dengan kebijakan privasi.
                        </p>
                    </div>
                </div>

                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden transition-colors duration-200">
                    <button class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-900 dark:text-white bg-white dark:bg-dark-card transition-colors duration-200" data-accordion-target="#faq-3" aria-expanded="false" aria-controls="faq-3">
                        <span>Apakah ada diskon untuk berlangganan tahunan?</span>
                        <svg class="w-6 h-6 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="faq-3" class="hidden p-5 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-card transition-colors duration-200">
                        <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">

                        </p>                            Ya, kami menawarkan diskon 20% untuk semua paket berlangganan tahunan. Ini berarti Anda membayar untuk 10 bulan dan mendapatkan 12 bulan layanan. Diskon ini secara otomatis diterapkan saat Anda memilih opsi pembayaran tahunan.
                    </div>
                </div>

                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden transition-colors duration-200">
                    <button class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-900 dark:text-white bg-white dark:bg-dark-card transition-colors duration-200" data-accordion-target="#faq-4" aria-expanded="false" aria-controls="faq-4">
                        <span>Apakah ada masa percobaan gratis?</span>
                        <svg class="w-6 h-6 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="faq-4" class="hidden p-5 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-card transition-colors duration-200">
                        <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">
                            Ya, kami menawarkan masa percobaan gratis selama 14 hari untuk paket Standar dan PRO. Selama masa percobaan, Anda dapat mengakses semua fitur paket yang dipilih dan memutuskan apakah RAB Estimator sesuai dengan kebutuhan Anda. Tidak diperlukan kartu kredit untuk memulai masa percobaan.
                        </p>
                    </div>
                </div>

                <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden transition-colors duration-200">
                    <button class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-900 dark:text-white bg-white dark:bg-dark-card transition-colors duration-200" data-accordion-target="#faq-5" aria-expanded="false" aria-controls="faq-5">
                        <span>Bagaimana cara membatalkan berlangganan?</span>
                        <svg class="w-6 h-6 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="faq-5" class="hidden p-5 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-card transition-colors duration-200">
                        <p class="text-gray-600 dark:text-gray-300 transition-colors duration-200">
                            Anda dapat membatalkan berlangganan kapan saja dari halaman pengaturan akun Anda. Setelah pembatalan, Anda akan tetap memiliki akses ke akun Anda sampai akhir periode penagihan saat ini. Kami tidak menawarkan pengembalian dana untuk sebagian dari bulan yang belum digunakan, kecuali diharuskan oleh hukum.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-gradient-to-r from-light-navbar via-[#0C4A7A] to-[#083D66] dark:from-blue-900 dark:to-indigo-900 text-white transition-colors duration-200">
        <div class="max-w-screen-xl mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-6">Mulai Optimalkan Perhitungan RAB Anda Hari Ini</h2>
            <p class="text-lg max-w-3xl mx-auto mb-8">
                Daftar sekarang dan nikmati masa percobaan gratis 14 hari tanpa perlu kartu kredit
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('register') }}" class="bg-white text-blue-600 dark:text-blue-800 font-bold py-3 px-8 rounded-lg hover:bg-gray-100 transition-all">
                    Daftar Sekarang
                </a>
                <a href="#" class="bg-transparent border-2 border-white text-white font-bold py-3 px-8 rounded-lg hover:bg-white/10 transition-all">
                    Jadwalkan Demo
                </a>


            </div>
        </div>
    </section>
@endsection

@section('scripts')
<script>
    // Simple accordion functionality
    document.addEventListener('DOMContentLoaded', function() {
        const accordionButtons = document.querySelectorAll('[data-accordion-target]');

        accordionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-accordion-target');
                const target = document.querySelector(targetId);
                const isExpanded = this.getAttribute('aria-expanded') === 'true';

                // Toggle the content visibility
                if (isExpanded) {
                    target.classList.add('hidden');
                    this.setAttribute('aria-expanded', 'false');
                    this.querySelector('svg').classList.remove('rotate-180');
                } else {
                    target.classList.remove('hidden');
                    this.setAttribute('aria-expanded', 'true');
                    this.querySelector('svg').classList.add('rotate-180');
                }
            });
        });
    });
</script>
@endsection