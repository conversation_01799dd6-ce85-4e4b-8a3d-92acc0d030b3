<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Models\Payment;
use App\Models\Subscription;

class PaymentLinkMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $payment;
    public $subscription;
    public $paymentUrl;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, Payment $payment, Subscription $subscription, string $paymentUrl)
    {
        $this->user = $user;
        $this->payment = $payment;
        $this->subscription = $subscription;
        $this->paymentUrl = $paymentUrl;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Link Pembayaran Langganan Estimateeng',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.payment-link',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
