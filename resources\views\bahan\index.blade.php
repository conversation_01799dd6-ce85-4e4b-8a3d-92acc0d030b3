@extends('layouts.app')

@section('content')
<div class="container mx-auto p-6 max-w-[1080px]">

  @if(session('success'))
  <div class="bg-green-200 text-green-800 p-2 rounded mb-4">
    {{ session('success') }}
  </div>
  @endif

  <div class="flex justify-between items-center mb-4">
    <button onclick="openBahanModal(false)" class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
      <i class="fas fa-plus-circle"></i> <PERSON><PERSON>han
    </button>
    <div class="flex items-center space-x-2">
      <form id="searchForm" action="{{ route('bahan.index') }}" method="GET" class="flex items-center space-x-2">
        <label for="searchBahan" class="text-sm font-medium">Cari Data:</label>
        <input type="text" id="searchBahan" name="search" placeholder="Cari..."
               class="border px-3 py-2 rounded" value="{{ request('search') }}" oninput="filterTable()">
      </form>
    </div>
  </div>

  <!-- Tabel Data Bahan -->
  <div class="overflow-y-auto max-h-[620px]">
    <table id="bahanTable" class="text-sm min-w-full bg-white border">
      <thead class="bg-blue-200 sticky top-0 z-10">
        <tr>
          <th class="py-2 px-4 border">No.</th>
          <th class="py-2 px-4 border">Uraian Bahan</th>
          <th class="py-2 px-4 border">Satuan</th>
          <th class="py-2 px-4 border">Harga Bahan (Rp)</th>
          <th class="py-2 px-4 border">Sumber</th>
          <th class="py-2 px-4 border">Aksi</th>
        </tr>
      </thead>
      <tbody>
        @foreach($bahans as $index => $bahan)
        <tr>
          <td class="py-2 px-4 border text-center">{{ ($bahans->currentPage() - 1) * $bahans->perPage() + $index + 1 }}</td>
          <td class="py-2 px-4 border">{{ $bahan->uraian_bahan }}</td>
          <td class="py-2 px-4 border">{{ $bahan->satuan }}</td>
          <td class="py-2 px-4 border">
            <span class="float-left">Rp.</span>
            <span class="float-right">{{ number_format($bahan->harga_bahan, 2) }}</span>
          </td>
          <td class="py-2 px-4 border">{{ $bahan->sumber }}</td>
          <td class="py-2 px-4 border text-center">
            <!-- Tombol Action untuk Context Menu -->
            <button type="button"
                    class="action-btn px-4 text-blue-500 hover:text-blue-900 p-1 rounded-full hover:bg-blue-200"
                    data-id="{{ $bahan->id }}"
                    data-uraian="{{ $bahan->uraian_bahan }}"
                    data-satuan="{{ $bahan->satuan }}"
                    data-harga="{{ $bahan->harga_bahan }}"
                    data-sumber="{{ $bahan->sumber }}"
                    data-module="bahan">
              <i class="fas fa-ellipsis-v"></i>
            </button>
          </td>
        </tr>
        @endforeach
        @if($bahans->isEmpty())
        <tr>
          <td colspan="6" class="py-2 px-4 border text-center">Tidak ada data.</td>
        </tr>
        @endif
      </tbody>
    </table>
  </div>

  <!-- Add Pagination Links -->
  {{ $bahans->links('components.user-fixed-pagination') }}
</div>

<!-- Modal Bahan (gunakan Blade component atau markup langsung) -->
<x-modal id="bahanModal" title="Tambah Bahan">
  <form id="bahanForm" method="POST" action="{{ route('bahan.store') }}" data-original-action="{{ route('bahan.store') }}">
    @csrf
    <div class="mb-4">
      <label for="uraian_bahan" class="block font-medium">Uraian Bahan</label>
      <input type="text" id="uraian_bahan" name="uraian_bahan" class="w-full border px-3 py-2 rounded" placeholder="Masukkan Uraian Bahan" required>
    </div>
    <div class="mb-4">
      <label for="satuan_bahan" class="block font-medium">Satuan</label>
      <input type="text" id="satuan_bahan" name="satuan" class="w-full border px-3 py-2 rounded" placeholder="Masukkan Satuan" required>
    </div>
    <div class="mb-4">
      <label for="harga_bahan" class="block font-medium">Harga Bahan</label>
      <input type="number" step="0.01" id="harga_bahan" name="harga_bahan" class="w-full border px-3 py-2 rounded" placeholder="Masukkan Harga Bahan" required>
    </div>
    <div class="mb-4">
      <label for="sumber_bahan" class="block font-medium">Sumber</label>
      <input type="text" id="sumber_bahan" name="sumber" class="w-full border px-3 py-2 rounded" placeholder="Masukkan Sumber">
    </div>
    <div class="flex justify-end">
      <button type="button" onclick="closeBahanModal()" class="bg-gray-500 hover:bg-gray-700 hover:text-blue-100 text-white px-4 py-2 rounded mr-2">
        <i class="fas fa-times-circle"></i> Batal</button>
      <button type="submit" class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
        <i class="fas fa-save"></i> Simpan</button>
    </div>
  </form>
</x-modal>

<!-- Context Menu -->
<div id="context-menu" class="hidden absolute bg-white dark:bg-dark-bg-secondary shadow-lg rounded-md py-2 w-32 border border-gray-200 dark:border-dark-accent/20 z-50 text-sm">
  <button onclick="contextMenuEdit()" class="w-full px-4 py-2 text-left text-blue-500 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-dark-accent/20 hover:text-blue-900 text-sm flex items-center edit-btn">
    <i class="fas fa-edit mr-2 w-4 h-4"></i> Edit
  </button>
  <button onclick="handleDelete(this)" class="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-dark-accent/20 text-sm flex items-center hover:text-red-900 text-red-500 delete-btn">
    <i class="fas fa-trash-alt mr-2 w-4 h-4"></i> Hapus
  </button>
</div>
@endsection

@section('scripts')
<script>
  window.csrfToken = '{{ csrf_token() }}';

  function filterTable() {
    const searchValue = document.getElementById('searchBahan').value.toLowerCase();

    // Client-side filtering for current page
    const rows = document.querySelectorAll('#bahanTable tbody tr');
    rows.forEach(row => {
      const uraian = row.children[1]?.textContent.toLowerCase() || '';
      const satuan = row.children[2]?.textContent.toLowerCase() || '';
      const sumber = row.children[4]?.textContent.toLowerCase() || '';
      row.style.display = (uraian.includes(searchValue) || satuan.includes(searchValue) || sumber.includes(searchValue)) ? '' : 'none';
    });

    // Server-side search for pagination
    if (searchValue.length > 2) {
      clearTimeout(window.searchTimeout);
      window.searchTimeout = setTimeout(() => {
        document.getElementById('searchForm').submit();
      }, 500);
    }
  }

  document.getElementById('searchBahan').addEventListener('input', filterTable);
</script>
@vite(['resources/js/modal.js', 'resources/js/table-sort.js'])
@endsection
