<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SubscriptionDuration extends Model
{
    use \Illuminate\Database\Eloquent\Factories\HasFactory;

    protected $fillable = [
        'subscription_plan_id',
        'duration_months',
        'promo_type',
        'promo_value',
        'description',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'duration_months' => 'integer',
        'promo_value' => 'integer',
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    /**
     * Get the subscription plan that owns this duration
     */
    public function plan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'subscription_plan_id');
    }

    /**
     * Get the formatted description of the duration
     */
    public function getFormattedDescriptionAttribute()
    {
        if ($this->promo_type === 'discount') {
            return "Diskon {$this->promo_value}% untuk langganan {$this->duration_months} bulan";
        } else {
            return "Gratis {$this->promo_value} bulan untuk langganan {$this->duration_months} bulan";
        }
    }

    /**
     * Calculate the total price after discount
     */
    public function calculateTotalPrice()
    {
        $basePrice = $this->plan->price;
        $totalMonths = $this->duration_months;

        if ($this->promo_type === 'discount') {
            // Calculate price with discount percentage
            $discountPercentage = $this->promo_value;
            $discountAmount = ($basePrice * $totalMonths * $discountPercentage) / 100;
            return ($basePrice * $totalMonths) - $discountAmount;
        } else {
            // Calculate price with free months
            $freeMonths = $this->promo_value;
            $paidMonths = $totalMonths - $freeMonths;
            return $basePrice * $paidMonths;
        }
    }

    /**
     * Get the formatted total price
     */
    public function getFormattedTotalPriceAttribute()
    {
        return 'Rp. ' . number_format($this->calculateTotalPrice(), 0, ',', '.');
    }
}
