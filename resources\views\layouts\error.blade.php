<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Early theme initialization to prevent FOUC (Flash of Unstyled Content) -->
    <script>
        // Check for saved theme preference or use system preference
        const savedTheme = localStorage.getItem("theme");
        const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;

        // Apply theme immediately before page renders
        if (savedTheme === "dark" || (!savedTheme && prefersDark)) {
            document.documentElement.classList.add("dark");
        } else {
            document.documentElement.classList.remove("dark");
        }
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Estimator App</title>

    <!-- Tailwind CSS -->
    @vite('resources/css/app.css')

    <!-- Flowbite CSS -->
    <link href="{{ asset('css/flowbite.min.css') }}" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ asset('css/all.min.css') }}">
</head>
<body class="bg-light-bg text-light-text dark:bg-dark-bg dark:text-dark-text transition-colors duration-200">
    <!-- Theme Toggle Button (Fixed at top-right) -->
    <div class="fixed top-4 right-4 z-50">
        <button id="theme-toggle" type="button" class="theme-toggle-elegant relative w-14 h-7 rounded-full bg-gradient-to-r from-yellow-400 to-light-accent dark:from-amber-700 dark:to-amber-900 transition-all duration-300 shadow-lg cursor-pointer">
            <span class="sr-only">Toggle theme</span>
            <div id="theme-toggle-icon" class="absolute top-1 left-1 h-5 w-5 transform rounded-full bg-white shadow-md transition-transform duration-300 flex items-center justify-center overflow-hidden">
                <svg class="moon-icon h-3 w-3 text-amber-500 absolute transition-opacity duration-200" viewBox="0 0 24 24" fill="none">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z" fill="currentColor"/>
                </svg>
                <svg class="sun-icon h-3 w-3 text-yellow-500 absolute transition-opacity duration-200" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="5" fill="currentColor"/>
                    <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" stroke="currentColor" stroke-width="2"/>
                </svg>
            </div>
        </button>
    </div>

    @yield('content')

    @vite(['resources/js/app.js'])

    <!-- Flowbite JS -->
    <script src="{{ asset('js/flowbite.min.js') }}"></script>

    @yield('scripts')
</body>
</html>