<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    /**
     * Display a listing of all payments.
     */
    public function index()
    {
        $payments = \App\Models\Payment::with(['user', 'subscription.plan'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.payments.index', compact('payments'));
    }

    /**
     * Display the specified payment.
     */
    public function show($id)
    {
        $payment = \App\Models\Payment::with(['user', 'subscription.plan'])
            ->findOrFail($id);

        return view('admin.payments.show', compact('payment'));
    }

    /**
     * Verify a payment.
     */
    public function verify($id)
    {
        $payment = \App\Models\Payment::with('subscription')->findOrFail($id);

        // Pastikan payment hanya dalam status processing yang dapat diverifikasi
        if ($payment->status !== 'processing') {
            return redirect()->route('admin.payments.show', $payment->id)
                ->with('error', 'Hanya pembayaran dengan status "processing" yang dapat diverifikasi.');
        }

        // Update payment status
        $payment->status = 'completed';
        $payment->paid_at = now();
        $payment->save();

        // Activate subscription
        if ($payment->subscription) {
            $payment->subscription->status = 'active';
            $payment->subscription->save();

            // Update user current subscription
            $user = $payment->user;
            $user->current_subscription_id = $payment->subscription_id;
            $user->save();
        }

        return redirect()->route('admin.payments.show', $payment->id)
            ->with('success', 'Pembayaran berhasil diverifikasi.');
    }

    /**
     * Reject a payment.
     */
    public function reject($id)
    {
        $payment = \App\Models\Payment::findOrFail($id);

        // Pastikan payment hanya dalam status processing yang dapat ditolak
        if ($payment->status !== 'processing') {
            return redirect()->route('admin.payments.show', $payment->id)
                ->with('error', 'Hanya pembayaran dengan status "processing" yang dapat ditolak.');
        }

        // Update payment status
        $payment->status = 'failed';
        $payment->save();

        return redirect()->route('admin.payments.show', $payment->id)
            ->with('success', 'Pembayaran ditolak.');
    }
}
