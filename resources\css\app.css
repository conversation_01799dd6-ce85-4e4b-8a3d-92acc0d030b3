@import "pulse-animation.css";
@import "pagination.css";
@import "user-pagination.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Foto Profil Styling */
.profile-photo-container {
    position: relative;
    overflow: hidden;
    border-radius: 9999px;
    border: 4px solid #0a558e; /* light-accent color */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.dark .profile-photo-container {
    border-color: #2563eb; /* dark-accent color */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3),
        0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.profile-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.profile-photo-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #e5e7eb; /* gray-200 */
    color: #9ca3af; /* gray-400 */
}

.dark .profile-photo-placeholder {
    background-color: #374151; /* gray-700 */
    color: #6b7280; /* gray-500 */
}

/* Memastikan bg-light-accent terlihat jelas dan mengatasi masalah caching */
.bg-light-accent {
    background-color: #0a558e !important;
}

.hover\:bg-light-accent\/80:hover {
    background-color: rgba(10, 85, 142, 0.8) !important;
}

/* Override tabel header pada dark mode */
.dark .bg-blue-200 {
    background-color: #121212 !important;
    color: white;
}

.dark .bg-blue-100 {
    background-color: #1a1a1a !important;
    color: white;
}

/* Modal styling di dark mode */
.dark .fixed.inset-0.flex.items-center.justify-center .bg-white {
    background-color: #1a1a1a !important;
    color: #e0e0e0;
}

.dark .fixed.inset-0.flex.items-center.justify-center input,
.dark .fixed.inset-0.flex.items-center.justify-center select,
.dark .fixed.inset-0.flex.items-center.justify-center textarea {
    background-color: #2d2d2d !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

.dark .fixed.inset-0.flex.items-center.justify-center label {
    color: #e0e0e0 !important;
}

/* Elegant Theme Switch Styling */
.theme-switch-container {
    position: relative;
    transition: all 0.3s ease;
}

.theme-toggle-elegant {
    cursor: pointer;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    z-index: 100;
    position: relative;
}

.theme-toggle-elegant:hover {
    transform: scale(1.05);
}

[id$="-toggle-icon"] {
    pointer-events: none; /* Ensure clicks pass through to the button */
}

[id$="-toggle-icon"] .moon-icon {
    opacity: 0;
    pointer-events: none;
}

[id$="-toggle-icon"] .sun-icon {
    opacity: 1;
    pointer-events: none;
}

.dark [id$="-toggle-icon"] .moon-icon {
    opacity: 1;
}

.dark [id$="-toggle-icon"] .sun-icon {
    opacity: 0;
}

/* Fixed position for toggle at the bottom of the sidebar */
#logo-sidebar .theme-switch-container {
    border-radius: 0.75rem;
    overflow: hidden;
}

/* Fixed position for toggle in auth and error pages */
.fixed.top-4.right-4 .theme-toggle-elegant {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Make sure toggle buttons are clickable */
#theme-toggle,
#theme-toggle-visitor,
#mobile-theme-toggle {
    position: relative;
    z-index: 100;
}

/* Fix for toggle icons */
#theme-toggle-icon,
#theme-toggle-visitor-icon,
#mobile-theme-toggle-icon {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* Add hover effect to all table rows in tbody */
@layer components {
    tbody tr {
        @apply transition-colors duration-150 ease-in-out;
    }

    tbody tr:hover {
        @apply bg-light-hover dark:bg-dark-hover;
    }

    /* Table Styling for Light Mode */
    thead tr {
        @apply bg-blue-200 text-gray-800 border-gray-300;
    }

    tbody tr:nth-child(even) {
        @apply bg-gray-50;
    }

    tbody tr:nth-child(odd) {
        @apply bg-white;
    }

    td,
    th {
        @apply border-gray-300;
    }

    /* Table Hover Effects for Light Mode */
    tbody tr:hover {
        @apply bg-blue-50 transition-colors duration-150 ease-in-out;
    }

    tbody tr:hover td {
        @apply text-blue-900;
    }

    /* Table Styling for Dark Mode */
    .dark thead tr {
        @apply bg-dark-table-header text-white border-dark-border;
    }

    .dark tbody tr:nth-child(even) {
        @apply bg-dark-table-row-even;
    }

    .dark tbody tr:nth-child(odd) {
        @apply bg-dark-table-row-odd;
    }

    .dark td,
    .dark th {
        @apply border-dark-border;
    }

    /* Table Hover Effects for Dark Mode */
    .dark tbody tr:hover {
        @apply bg-dark-hover transition-colors duration-150 ease-in-out;
    }

    .dark tbody tr:hover td {
        @apply text-white;
    }

    /* Modal Styling for Dark Mode */
    .dark .modal-content {
        @apply bg-dark-modal border-dark-border;
    }

    /* Button Hover States for Dark Mode */
    .dark .btn:hover,
    .dark button:hover {
        @apply bg-opacity-80;
    }

    /* Action button styles */
    .action-btn {
        @apply text-blue-500 hover:text-blue-900 p-1 rounded-full hover:bg-blue-100 transition-colors duration-150;
    }

    /* Dark mode override for action-btn */
    .dark .action-btn {
        @apply text-dark-accent hover:text-white hover:bg-dark-accent/20;
    }

    .dark .action-btn:hover {
        @apply bg-dark-accent/20 text-white shadow-sm shadow-dark-border/30;
    }

    /* Action button item styles - disamakan dengan action-btn */
    .action-btn-item {
        @apply text-blue-500 hover:text-blue-900 p-1 rounded-full hover:bg-blue-100 transition-colors duration-150;
    }

    /* Dark mode overrides untuk action-btn-item */
    .dark .action-btn-item {
        @apply text-dark-accent hover:text-white hover:bg-dark-accent/20;
    }

    .dark .action-btn-item:hover {
        @apply bg-dark-accent/20 text-white shadow-sm shadow-dark-border/30;
    }

    /* Action button kategori styles - disamakan dengan action-btn dan action-btn-item */
    .action-btn-kategori {
        @apply text-blue-500 hover:text-blue-900 p-1 rounded-full hover:bg-blue-200 transition-colors duration-150;
        transition: all 0.15s ease-in-out;
    }

    /* Dark mode overrides untuk action-btn-kategori */
    .dark .action-btn-kategori {
        @apply text-dark-accent hover:text-white hover:bg-dark-accent/20;
    }

    .dark .action-btn-kategori:hover {
        @apply bg-dark-accent/20 text-white shadow-sm shadow-dark-border/30;
    }

    /* Context menu button styles - dark mode */
    .dark #item-context-menu button,
    .dark #kategori-context-menu button,
    .dark #context-menu button,
    .dark #detail-context-menu button {
        @apply text-dark-accent hover:bg-dark-accent/20 hover:text-white;
    }

    /* Delete button in context menu - dark mode */
    .dark #item-context-menu button:last-child,
    .dark #kategori-context-menu button:last-child,
    .dark #context-menu button.delete-btn,
    .dark #detail-context-menu button:last-child {
        @apply text-red-400 hover:text-red-300;
    }
}

/* Theme related styles */
@layer base {
    body {
        @apply bg-light-bg text-light-text dark:bg-dark-bg dark:text-dark-text transition-colors duration-200;
    }

    /* Standard card */
    .card {
        @apply bg-light-card dark:bg-dark-card shadow-lg rounded-lg transition-colors duration-200;
    }

    /* Theme toggle button */
    .theme-toggle {
        @apply relative h-6 w-12 rounded-full bg-gray-200 dark:bg-dark-bg-secondary transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent;
    }

    .theme-toggle-icon {
        @apply absolute top-[2px] left-[2px] h-5 w-5 transform rounded-full bg-white text-yellow-500 transition-transform duration-200;
    }

    .theme-toggle-icon.active {
        @apply translate-x-6 bg-dark-accent text-white;
    }

    /* Input fields in light mode */
    input,
    select,
    textarea {
        @apply bg-white dark:bg-dark-bg-secondary border border-gray-300 dark:border-dark-border rounded-lg focus:ring-light-accent dark:focus:ring-dark-accent focus:border-light-accent dark:focus:border-dark-accent transition-colors duration-200;
    }

    /* Buttons in light mode */
    .btn-primary {
        @apply bg-light-accent text-white dark:bg-dark-accent hover:bg-light-accent/90 dark:hover:bg-dark-accent/90 transition-colors duration-200;
    }

    .btn-secondary {
        @apply bg-white text-light-text dark:bg-dark-bg-secondary dark:text-dark-text border border-gray-300 dark:border-dark-border hover:bg-light-hover dark:hover:bg-dark-hover transition-colors duration-200;
    }

    /* Modal backgrounds */
    .fixed.inset-0.flex.items-center.justify-center.bg-black.bg-opacity-50
        > div {
        @apply dark:bg-dark-modal;
    }
}

/* Override untuk tombol dengan bg-light-accent dalam dark mode */
.dark .bg-light-accent {
    background-color: #ed8936 !important; /* Amber/Orange 500 - Warna lebih gelap */
}

/* Override untuk hover state tombol dalam dark mode */
.dark .hover\:bg-light-accent\/80:hover {
    background-color: rgba(
        237,
        137,
        54,
        0.8
    ) !important; /* Amber/Orange 500 dengan opacity 80% */
}

/* Peningkatan visibilitas teks dalam dark mode */
.dark h1,
.dark h2,
.dark h3,
.dark h4,
.dark h5,
.dark h6 {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Tambahkan text shadow untuk teks putih tanpa menggunakan selector .text-white */
.dark nav a,
.dark #logo-sidebar a,
.dark button[class*="text-white"],
.dark [class*="text-white"] {
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

/* Sinkronisasi warna sidebar dan navbar dengan komponen lain */
.dark #logo-sidebar {
    border-inline-end-color: rgba(237, 137, 54, 0.3) !important;
}

.dark nav.bg-dark-navbar {
    border-block-end: 1px solid rgba(237, 137, 54, 0.3);
}

/* Aksen warna Amber pada elemen sidebar */
.dark #logo-sidebar div[class*="p-2"] {
    background-color: rgba(237, 137, 54, 0.15) !important;
}

/* Efek hover yang sinkron dengan warna tombol */
.dark #logo-sidebar a:hover,
.dark nav button:hover {
    background-color: rgba(237, 137, 54, 0.15) !important;
}

/* Sinkronisasi card/div dengan tema dark */
.dark .bg-white.rounded-lg.shadow-md {
    background-color: #2c2c2c !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3),
        0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
}

.dark .bg-white.rounded-lg.shadow-md:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4),
        0 4px 6px -2px rgba(0, 0, 0, 0.3) !important;
}

/* Sinkronisasi teks dalam card dengan tema dark */
.dark .bg-white.rounded-lg.shadow-md h3 {
    color: #ffffff;
}

.dark .bg-white.rounded-lg.shadow-md p {
    color: #e5e5e5 !important;
}

/* Sinkronisasi modal dengan tema dark */
.dark
    .fixed.inset-0.flex.items-center.justify-center
    .bg-white.rounded-lg.shadow-lg {
    background-color: #232323 !important;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5),
        0 8px 10px -6px rgba(0, 0, 0, 0.4) !important;
}

.dark
    .fixed.inset-0.flex.items-center.justify-center
    .bg-white.rounded-lg.shadow-lg
    h2 {
    color: #ffffff;
}

.dark
    .fixed.inset-0.flex.items-center.justify-center
    .bg-white.rounded-lg.shadow-lg
    label {
    color: #e5e5e5 !important;
}

/* Sinkronisasi modal inputKategoriModal dengan tema dark */
.dark #inputKategoriModal .bg-white {
    background-color: #232323 !important;
}

.dark #inputKategoriModal .bg-gray-100 {
    background-color: #2c2c2c !important;
    border-color: #3a3a3a !important;
}

.dark #inputKategoriModal #inputKategoriModalTitle {
    color: #ffffff !important;
}

.dark #inputKategoriModal label {
    color: #e5e5e5 !important;
}

.dark #inputKategoriModal input {
    background-color: #252525 !important;
    border-color: #3a3a3a !important;
    color: #ffffff !important;
}

/* Tombol dalam inputKategoriModal */
.dark #inputKategoriModal .bg-gray-500:hover {
    background-color: #4a4a4a !important;
}

.dark #inputKategoriModal .bg-light-accent {
    background-color: #ed8936 !important;
}

.dark #inputKategoriModal .hover\:bg-light-accent\/80:hover {
    background-color: rgba(237, 137, 54, 0.8) !important;
}

/* Sinkronisasi modal rabKategoriModal dengan tema dark */
.dark #rabKategoriModal .bg-white {
    background-color: #232323 !important;
}

.dark #rabKategoriModal h2 {
    color: #ffffff !important;
}

.dark #rabKategoriModal .border-gray-200 {
    border-color: #3a3a3a !important;
}

/* Sinkronisasi modal inputItemModal dengan tema dark */
.dark #inputItemModal .bg-white {
    background-color: #232323 !important;
}

.dark #inputItemModal h2 {
    color: #ffffff !important;
}

.dark #inputItemModal input[type="text"] {
    background-color: #252525 !important;
    border-color: #3a3a3a !important;
    color: #ffffff !important;
}

/* Styling untuk semua checkbox di mode light */
input[type="checkbox"],
.w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-500,
.h-4.w-4.text-indigo-600.focus\:ring-indigo-500.border-gray-300.rounded,
[type="checkbox"].w-4.h-4,
[type="checkbox"].h-4.w-4 {
    background-color: #ffffff !important;
    border-color: #d1d5db !important; /* gray-300 */
    border-width: 2px !important;
    transition: all 0.2s ease-in-out !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    border-radius: 0.25rem !important;
    inline-size: 1rem !important;
    block-size: 1rem !important;
}

/* Hover effect untuk checkbox di mode light */
input[type="checkbox"]:hover:not(:checked),
.w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-500:hover:not(
        :checked
    ),
.h-4.w-4.text-indigo-600.focus\:ring-indigo-500.border-gray-300.rounded:hover:not(
        :checked
    ),
[type="checkbox"].w-4.h-4:hover:not(:checked),
[type="checkbox"].h-4.w-4:hover:not(:checked) {
    border-color: #0a558e !important; /* light-accent */
    box-shadow: 0 0 0 1px rgba(10, 85, 142, 0.1) !important;
}

/* Warna checkbox saat aktif di mode light - meningkatkan spesifisitas */
html body input[type="checkbox"]:checked,
html
    body
    .w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-500:checked,
html
    body
    .h-4.w-4.text-indigo-600.focus\:ring-indigo-500.border-gray-300.rounded:checked,
html body [type="checkbox"].w-4.h-4:checked,
html body [type="checkbox"].h-4.w-4:checked {
    background-color: #0a558e !important; /* light-accent color */
    border-color: #0a558e !important;
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e") !important;
    background-size: 100% 100% !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

/* Warna ring saat fokus di mode light */
input[type="checkbox"]:focus,
.w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-500:focus,
.h-4.w-4.text-indigo-600.focus\:ring-indigo-500.border-gray-300.rounded:focus,
[type="checkbox"].w-4.h-4:focus,
[type="checkbox"].h-4.w-4:focus {
    --tw-ring-color: #0a558e !important;
    --tw-ring-opacity: 0.3 !important;
    box-shadow: 0 0 0 3px rgba(10, 85, 142, 0.3) !important;
    outline: none !important;
}

/* Styling untuk checkbox di mode dark */
.dark input[type="checkbox"],
.dark
    .w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-500,
.dark
    .w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-600.dark\:ring-offset-gray-800.focus\:ring-2.dark\:bg-gray-700.dark\:border-gray-600,
.dark .h-4.w-4.text-indigo-600.focus\:ring-indigo-500.border-gray-300.rounded,
.dark [type="checkbox"].w-4.h-4,
.dark [type="checkbox"].h-4.w-4 {
    background-color: #252525 !important;
    border-color: #3a3a3a !important;
    border-width: 2px !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    border-radius: 0.25rem !important;
    inline-size: 1rem !important;
    block-size: 1rem !important;
}

/* Hover effect untuk checkbox di mode dark */
.dark input[type="checkbox"]:hover:not(:checked),
.dark
    .w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-500:hover:not(
        :checked
    ),
.dark
    .w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-600.dark\:ring-offset-gray-800.focus\:ring-2.dark\:bg-gray-700.dark\:border-gray-600:hover:not(
        :checked
    ),
.dark
    .h-4.w-4.text-indigo-600.focus\:ring-indigo-500.border-gray-300.rounded:hover:not(
        :checked
    ),
.dark [type="checkbox"].w-4.h-4:hover:not(:checked),
.dark [type="checkbox"].h-4.w-4:hover:not(:checked) {
    border-color: #ed8936 !important; /* dark-accent */
    box-shadow: 0 0 0 1px rgba(237, 137, 54, 0.2) !important;
}

/* Warna checkbox saat aktif di mode dark */
.dark input[type="checkbox"]:checked,
.dark
    .w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-500:checked,
.dark
    .w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-600.dark\:ring-offset-gray-800.focus\:ring-2.dark\:bg-gray-700.dark\:border-gray-600:checked,
.dark
    .h-4.w-4.text-indigo-600.focus\:ring-indigo-500.border-gray-300.rounded:checked,
.dark [type="checkbox"].w-4.h-4:checked,
.dark [type="checkbox"].h-4.w-4:checked {
    background-color: #ed8936 !important; /* dark-accent color - warna cerah */
    border-color: #ed8936 !important;
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='%23252525' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e") !important;
    background-size: 100% 100% !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

/* Warna ring saat fokus di mode dark */
.dark input[type="checkbox"]:focus,
.dark
    .w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-500:focus,
.dark
    .w-4.h-4.text-blue-600.bg-gray-100.border-gray-300.rounded.focus\:ring-blue-600.dark\:ring-offset-gray-800.focus\:ring-2.dark\:bg-gray-700.dark\:border-gray-600:focus,
.dark
    .h-4.w-4.text-indigo-600.focus\:ring-indigo-500.border-gray-300.rounded:focus,
.dark [type="checkbox"].w-4.h-4:focus,
.dark [type="checkbox"].h-4.w-4:focus {
    --tw-ring-color: #ed8936 !important;
    --tw-ring-opacity: 0.5 !important;
    box-shadow: 0 0 0 3px rgba(237, 137, 54, 0.3) !important;
    outline: none !important;
}

/* Styling untuk checkbox di dalam tabel */
table input[type="checkbox"],
table [type="checkbox"],
#detailModal input[type="checkbox"],
#inputItemModal input[type="checkbox"],
#masterTable input[type="checkbox"],
#ahspTable input[type="checkbox"] {
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    inline-size: 1rem !important;
    block-size: 1rem !important;
    border-radius: 0.25rem !important;
    border-width: 2px !important;
    background-color: #ffffff !important;
    border-color: #d1d5db !important;
    margin: 0 auto !important;
    display: block !important;
    cursor: pointer !important;
}

/* Styling untuk checkbox di dalam tabel - mode dark */
.dark table input[type="checkbox"],
.dark table [type="checkbox"],
.dark #detailModal input[type="checkbox"],
.dark #inputItemModal input[type="checkbox"],
.dark #masterTable input[type="checkbox"],
.dark #ahspTable input[type="checkbox"] {
    background-color: #252525 !important;
    border-color: #3a3a3a !important;
}

/* Styling untuk checkbox aktif di dalam tabel */
table input[type="checkbox"]:checked,
table [type="checkbox"]:checked,
#detailModal input[type="checkbox"]:checked,
#inputItemModal input[type="checkbox"]:checked,
#masterTable input[type="checkbox"]:checked,
#ahspTable input[type="checkbox"]:checked {
    background-color: #0a558e !important;
    border-color: #0a558e !important;
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e") !important;
    background-size: 100% 100% !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

/* Styling untuk checkbox aktif di dalam tabel - mode dark */
.dark table input[type="checkbox"]:checked,
.dark table [type="checkbox"]:checked,
.dark #detailModal input[type="checkbox"]:checked,
.dark #inputItemModal input[type="checkbox"]:checked,
.dark #masterTable input[type="checkbox"]:checked,
.dark #ahspTable input[type="checkbox"]:checked {
    background-color: #ed8936 !important;
    border-color: #ed8936 !important;
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='%23252525' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e") !important;
}

/* Tombol tergembok (mode view-only) */
.locked-btn {
    cursor: not-allowed;
    opacity: 0.7;
    position: relative;
}

.locked-btn:hover::after {
    content: "\f023"; /* Font Awesome lock icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    color: #6b7280; /* gray-500 */
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    padding: 4px;
    z-index: 10;
}
