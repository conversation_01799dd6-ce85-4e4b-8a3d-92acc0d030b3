<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Socialite\Facades\Socialite;

class GoogleController extends Controller
{
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            $user = User::where('google_id', $googleUser->getId())->first();

            if (!$user) {
                $user = User::where('email', $googleUser->getEmail())->first();

                if (!$user) {
                    $user = User::create([
                        'name' => $googleUser->getName(),
                        'email' => $googleUser->getEmail(),
                        'google_id' => $googleUser->getId(),
                        'password' => bcrypt(uniqid()), // Generate random password
                        'email_verified_at' => now(), // Google sudah memverifikasi email
                    ]);
                } else {
                    $user->update([
                        'google_id' => $googleUser->getId(),
                        'email_verified_at' => now(), // Update email_verified_at jika belum terverifikasi
                    ]);
                }
            }

            Auth::login($user);

            return redirect()->intended('/proyek');
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'Something went wrong with Google login');
        }
    }
}
