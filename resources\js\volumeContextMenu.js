/**
 * Volume Context Menu Handler
 * Menangani context menu dan action-btn pada rabVolumeModal
 */

// Variabel global untuk menyimpan data volume calculation yang sedang dipilih
window.currentVolumeCalc = null;

// Fungsi untuk menangani context menu pada tombol aksi volume
function handleVolumeContextMenu(event, button) {
    event.preventDefault();
    event.stopPropagation();

    const contextMenu = document.getElementById("volume-context-menu");
    if (!contextMenu) return;

    // Pastikan context menu berada di body untuk positioning yang benar
    if (contextMenu.parentElement !== document.body) {
        document.body.appendChild(contextMenu);
    }

    // Simpan data volume calculation yang sedang dipilih
    const volumeId = button.getAttribute("data-id");
    const keterangan = button.getAttribute("data-keterangan");

    window.currentVolumeCalc = { id: volumeId, keterangan: keterangan };

    // Tampilkan context menu
    contextMenu.classList.remove("hidden");

    // Posisikan context menu
    const menuWidth = contextMenu.offsetWidth;
    const menuHeight = contextMenu.offsetHeight;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let x = event.pageX;
    let y = event.pageY;

    // Pastikan context menu tidak keluar dari viewport
    if (x + menuWidth > viewportWidth) x = viewportWidth - menuWidth;
    if (y + menuHeight > viewportHeight) y = viewportHeight - menuHeight;

    contextMenu.style.left = `${x}px`;
    contextMenu.style.top = `${y}px`;
    contextMenu.style.zIndex = "60"; // Pastikan context menu berada di depan
}
window.handleVolumeContextMenu = handleVolumeContextMenu;

// Fungsi untuk menangani klik edit pada context menu
function handleVolumeEdit() {
    if (!window.currentVolumeCalc) return;

    // Panggil fungsi editVolumeCalculation dengan ID volume calculation yang dipilih
    editVolumeCalculation(window.currentVolumeCalc.id);

    // Sembunyikan context menu
    const contextMenu = document.getElementById("volume-context-menu");
    if (contextMenu) contextMenu.classList.add("hidden");
}
window.handleVolumeEdit = handleVolumeEdit;

// Fungsi untuk menangani klik hapus pada context menu
function handleVolumeDelete() {
    if (!window.currentVolumeCalc) return;

    // Panggil fungsi deleteVolumeCalculation dengan ID volume calculation yang dipilih
    deleteVolumeCalculation(window.currentVolumeCalc.id);

    // Sembunyikan context menu
    const contextMenu = document.getElementById("volume-context-menu");
    if (contextMenu) contextMenu.classList.add("hidden");
}
window.handleVolumeDelete = handleVolumeDelete;

// Event listener untuk menyembunyikan context menu saat klik di luar
document.addEventListener("click", (event) => {
    const contextMenu = document.getElementById("volume-context-menu");
    if (
        contextMenu &&
        !contextMenu.contains(event.target) &&
        !event.target.closest(".action-btn")
    ) {
        contextMenu.classList.add("hidden");
    }
});

// Tooltip functionality removed
