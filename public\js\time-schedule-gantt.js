// — Data model awal —
let tasks = [
    {
        id: 1,
        name: "Proyek A",
        start: new Date(2025, 3, 1),
        duration: 10,
        price: 1000,
    },
    {
        id: 2,
        name: "Item Pekerjaan 1",
        start: new Date(2025, 3, 1),
        duration: 3,
        price: 500,
    },
    {
        id: 3,
        name: "Item Pekerjaan 2",
        start: new Date(2025, 3, 4),
        duration: 5,
        price: 800,
    },
];
let scale = "month",
    zoom = 1,
    dayW = 30;

document.addEventListener("DOMContentLoaded", function () {
    const gridBody = document.getElementById("grid-body"),
        gridTot = document.getElementById("grid-total"),
        totalWeight = document.getElementById("total-weight"),
        gridDiv = document.getElementById("grid"),
        chartCon = document.getElementById("chart-container"),
        tmContainer = document.querySelector(".timeline-container"),
        tm = document.querySelector(".timeline-month"),
        td = document.querySelector(".timeline-day"),
        gridLines = document.getElementById("grid-lines"),
        tasksEl = document.getElementById("tasks"),
        sctx = document.getElementById("scurve").getContext("2d");

    // Inisialisasi
    initControls();
    renderGrid();
    renderTimeline();
    renderTasks();

    // Sinkron scroll horizontal dan vertikal
    gridDiv.onscroll = () => {
        chartCon.scrollTop = gridDiv.scrollTop;
    };
    chartCon.onscroll = () => {
        gridDiv.scrollTop = chartCon.scrollTop;
    };
    chartCon.addEventListener("scroll", function () {
        tmContainer.scrollLeft = chartCon.scrollLeft;
    });

    // Fungsi inisialisasi kontrol
    function initControls() {
        document.getElementById("zoom-in").onclick = () => {
            zoom = Math.min(4, zoom * 2);
            renderTimeline();
            renderTasks();
        };

        document.getElementById("zoom-out").onclick = () => {
            zoom = Math.max(0.25, zoom / 2);
            renderTimeline();
            renderTasks();
        };

        document.getElementById("scale").onchange = (e) => {
            scale = e.target.value;
            renderTimeline();
            renderTasks();
        };

        // Form tambah item dari RAB
        const addItemForm = document.getElementById("addItemForm");
        if (addItemForm) {
            // Saat item dipilih, isi otomatis harga dan bobot
            const itemSelect = document.getElementById("itemSelect");
            const itemPriceInput = document.getElementById("itemPrice");
            const itemWeightInput = document.getElementById("itemWeight");

            if (itemSelect) {
                itemSelect.addEventListener("change", function () {
                    const selectedOption = this.options[this.selectedIndex];
                    if (selectedOption.value) {
                        const price = selectedOption.getAttribute("data-price");
                        const bobot = selectedOption.getAttribute("data-bobot");

                        // Format harga sebagai mata uang Indonesia
                        itemPriceInput.value = new Intl.NumberFormat(
                            "id-ID"
                        ).format(price);
                        itemPriceInput.dataset.rawValue = price;

                        // Isi bobot
                        itemWeightInput.value = bobot;
                    } else {
                        itemPriceInput.value = "";
                        itemPriceInput.dataset.rawValue = "0";
                        itemWeightInput.value = "";
                    }
                });
            }

            addItemForm.addEventListener("submit", function (e) {
                e.preventDefault();

                const itemSelect = document.getElementById("itemSelect");
                const startDate = document.getElementById("startDate").value;
                const duration = parseInt(
                    document.getElementById("duration").value
                );

                if (!itemSelect.value || !startDate || !duration) {
                    alert("Mohon lengkapi semua field");
                    return;
                }

                const selectedOption =
                    itemSelect.options[itemSelect.selectedIndex];
                const itemName = selectedOption.textContent.split(" (")[0];
                const itemPrice = parseFloat(
                    document.getElementById("itemPrice").dataset.rawValue || 0
                );
                const itemWeight = parseFloat(
                    document.getElementById("itemWeight").value || 0
                );

                // Tambahkan item baru ke daftar
                const newItem = {
                    id: Date.now(),
                    rabItemId: parseInt(itemSelect.value),
                    name: itemName,
                    start: new Date(startDate),
                    duration: duration,
                    price: itemPrice,
                    weight: itemWeight,
                };

                tasks.push(newItem);

                // Perbarui tampilan
                renderGrid();
                renderTimeline();
                renderTasks();

                // Reset form dan tutup modal
                addItemForm.reset();

                // Tutup modal menggunakan API Flowbite
                const modalEl = document.getElementById("tambahItemModal");
                const modal = flowbite.Modal.getInstance(modalEl);
                modal.hide();
            });
        }
    }
});

// Hitung rentang waktu
function calcRange() {
    if (tasks.length === 0) {
        // Default rentang waktu jika tidak ada tasks
        const today = new Date();
        const nextMonth = new Date(today);
        nextMonth.setMonth(today.getMonth() + 1);
        return { start: today, end: nextMonth };
    }

    let min = Infinity,
        max = -Infinity;
    tasks.forEach((t) => {
        min = Math.min(min, t.start.getTime());
        max = Math.max(max, t.start.getTime() + t.duration * 864e5);
    });
    return { start: new Date(min), end: new Date(max) };
}

// Render grid kiri: bobot=(price/Σprice)*100%
function renderGrid() {
    const gridBody = document.getElementById("grid-body"),
        gridTot = document.getElementById("grid-total"),
        totalWeight = document.getElementById("total-weight");

    let sum = tasks.reduce((s, t) => s + t.price, 0);
    let sumWeight = 0;

    gridBody.innerHTML = "";
    gridTot.innerText = new Intl.NumberFormat("id-ID").format(sum);

    tasks.forEach((t) => {
        // Gunakan bobot dari data jika sudah ada, jika tidak hitung
        if (!t.weight || t.weight === 0) {
            t.weight = (t.price / sum) * 100;
        }
        sumWeight += t.weight;

        const row = document.createElement("chart-row"),
            c1 = document.createElement("chart-cell"),
            c2 = document.createElement("chart-cell"),
            c3 = document.createElement("chart-cell"),
            c4 = document.createElement("chart-cell");

        c1.innerText = t.name;
        c2.innerText = new Intl.NumberFormat("id-ID").format(t.price);
        c3.innerText = t.duration + " Hari";
        c4.innerText = t.weight.toFixed(2) + "%";

        row.append(c1, c2, c3, c4);
        gridBody.appendChild(row);
    });

    // Tampilkan total bobot
    totalWeight.innerText = sumWeight.toFixed(2) + "%";
}

// Render header Gantt: bulan & hari
function renderTimeline() {
    const tmContainer = document.querySelector(".timeline-container"),
        tm = document.querySelector(".timeline-month"),
        td = document.querySelector(".timeline-day");

    const { start, end } = calcRange();
    tm.innerHTML = "";
    td.innerHTML = "";

    let totalWidth = 0;
    let cur = new Date(start);
    while (cur < end) {
        // Bulan
        if (scale !== "day") {
            const mcell = document.createElement("div");
            mcell.className = "cell";
            let cellWidth =
                dayW *
                (scale === "week"
                    ? 7
                    : getDaysInMonth(cur.getMonth(), cur.getFullYear())) *
                zoom;
            mcell.style.width = cellWidth + "px";
            mcell.innerText = cur.toLocaleDateString("id", {
                month: "long",
                year: "numeric",
            });
            tm.appendChild(mcell);

            // Tambahkan lebar untuk perhitungan total width
            totalWidth += cellWidth;
        }

        // Hari
        const days =
            scale === "week"
                ? 7
                : getDaysInMonth(cur.getMonth(), cur.getFullYear());
        let daysInThisMonth = new Date(
            cur.getFullYear(),
            cur.getMonth() + 1,
            0
        ).getDate();

        for (let i = 0; i < days && cur < end; i++) {
            const dcell = document.createElement("div");
            dcell.className = "cell";
            dcell.style.width = dayW * zoom + "px";
            dcell.innerText = cur.getDate();
            td.appendChild(dcell);

            // Jika sudah hari terakhir di bulan ini dan scale bukan day, pindah ke bulan berikutnya
            if (scale !== "day" && cur.getDate() === daysInThisMonth) {
                cur.setDate(1);
                cur.setMonth(cur.getMonth() + 1);
            } else {
                cur.setDate(cur.getDate() + 1);
            }
        }
    }

    // Set width container untuk memastikan horizontal scroll bekerja
    document.getElementById("chart-container").style.width =
        "calc(100% - 600px)";
}

// Helper function untuk mendapatkan jumlah hari dalam bulan
function getDaysInMonth(month, year) {
    return new Date(year, month + 1, 0).getDate();
}

// Render garis grid vertikal
function renderGridLines() {
    const gridLines = document.getElementById("grid-lines");
    gridLines.innerHTML = "";

    const { start, end } = calcRange();
    let x = 0,
        date = new Date(start);

    while (date < end) {
        const line = document.createElement("div");
        line.className = "line";
        line.style.left = x * dayW * zoom + "px";
        gridLines.appendChild(line);
        date.setDate(date.getDate() + 1);
        x++;
    }

    // Set lebar container grid lines
    const totalDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
    gridLines.style.width = totalDays * dayW * zoom + "px";
}

// Render tasks & attach drag/resize
function renderTasks() {
    const tasksEl = document.getElementById("tasks");
    tasksEl.innerHTML = "";
    renderGridLines();

    const { start, end } = calcRange();

    // Set lebar container tasks untuk mengakomodasi semua task
    const totalDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
    tasksEl.style.width = totalDays * dayW * zoom + "px";

    tasks.forEach((t, i) => {
        const row = document.createElement("div");
        row.className = "task-row";
        row.style.top = i * 30 + "px";

        const bar = document.createElement("div");
        bar.className = "task-bar";
        bar.innerText = `${t.name} (${t.weight.toFixed(1)}%)`;

        let off = ((t.start - start) / 864e5) * dayW * zoom;
        bar.style.left = off + "px";
        bar.style.width = t.duration * dayW * zoom + "px";

        ["left", "right"].forEach((side) => {
            const r = document.createElement("div");
            r.className = `resizer ${side}`;
            bar.appendChild(r);
        });

        row.appendChild(bar);
        tasksEl.appendChild(row);

        makeDrag(bar, t);
        makeResize(bar, t);
    });

    drawSCurve();
}

// Drag tugas via mousedown/mousemove/mouseup
function makeDrag(bar, task) {
    let dx;

    bar.addEventListener("mousedown", (e) => {
        if (e.target.classList.contains("resizer")) return;

        dx = e.clientX - bar.getBoundingClientRect().left;
        const chartCon = document.getElementById("chart-container");

        function mv(e2) {
            bar.style.left =
                e2.clientX - chartCon.getBoundingClientRect().left - dx + "px";
        }

        function up() {
            document.removeEventListener("mousemove", mv);
            document.removeEventListener("mouseup", up);

            const { start } = calcRange();
            task.start = new Date(
                start.getTime() +
                    Math.round(parseFloat(bar.style.left) / (dayW * zoom)) *
                        864e5
            );

            renderTimeline();
            renderTasks();
        }

        document.addEventListener("mousemove", mv);
        document.addEventListener("mouseup", up);
    });
}

// Resize tugas
function makeResize(bar, task) {
    const l = bar.querySelector(".resizer.left"),
        r = bar.querySelector(".resizer.right");
    let ox, ow, side;

    function start(e, s) {
        side = s;
        ox = e.clientX;
        ow = bar.offsetWidth;
        document.addEventListener("mousemove", rsz);
        document.addEventListener("mouseup", stp);
    }

    function rsz(e) {
        let d = e.clientX - ox;
        if (side === "right") bar.style.width = ow + d + "px";
        else {
            bar.style.width = ow - d + "px";
            bar.style.left = bar.offsetLeft + d + "px";
        }
    }

    function stp() {
        document.removeEventListener("mousemove", rsz);
        document.removeEventListener("mouseup", stp);

        const { start } = calcRange();
        let lp = parseFloat(bar.style.left),
            wp = parseFloat(bar.style.width);

        task.duration = Math.round(wp / (dayW * zoom));
        task.start = new Date(
            start.getTime() + Math.round(lp / (dayW * zoom)) * 864e5
        );

        // Update grid dengan durasi baru
        renderGrid();
        renderTimeline();
        renderTasks();
    }

    l.addEventListener("mousedown", (e) => start(e, "left"));
    r.addEventListener("mousedown", (e) => start(e, "right"));
}

// S-curve terbalik via Canvas transform
function drawSCurve() {
    const chartCon = document.getElementById("chart-container"),
        canvas = document.getElementById("scurve"),
        tasksContainer = document.getElementById("tasks");

    // Atur lebar canvas sesuai dengan durasi pekerjaan
    canvas.width = tasksContainer.scrollWidth;
    canvas.height = chartCon.clientHeight;

    const ctx = canvas.getContext("2d");
    ctx.setTransform(1, 0, 0, -1, 0, canvas.height);
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    const sumW = tasks.reduce((s, t) => s + t.weight, 0);
    let cum = 0;

    // Buat gradien untuk S-curve
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, "rgba(59, 130, 246, 0.7)"); // Blue
    gradient.addColorStop(1, "rgba(16, 185, 129, 0.7)"); // Green

    ctx.strokeStyle = gradient;
    ctx.fillStyle = "#1e293b";
    ctx.font = "bold 12px Arial";

    // Urutkan tasks berdasarkan tanggal mulai
    const sortedTasks = tasks.slice().sort((a, b) => a.start - b.start);
    const { start } = calcRange();

    sortedTasks.forEach((t, i) => {
        cum += t.weight;
        let pct = cum / sumW;

        // Hitung posisi x berdasarkan tanggal dan durasi
        let taskStart = ((t.start - start) / 864e5) * dayW * zoom;
        let taskEnd = taskStart + t.duration * dayW * zoom;
        let x = taskEnd; // Gunakan akhir task untuk plot S-curve

        let y = pct * (canvas.height * 0.9); // 90% dari tinggi untuk memberi ruang label

        if (i === 0) ctx.beginPath(), ctx.moveTo(x, y);
        else ctx.lineTo(x, y);

        // Gambar label persen dengan kontras yang lebih baik
        ctx.save();
        ctx.scale(1, -1);
        ctx.fillText(pct.toFixed(1) + "%", x + 5, -y - 2);
        ctx.restore();
    });

    ctx.lineWidth = 3;
    ctx.stroke();

    // Area fill di bawah curve
    ctx.lineTo(canvas.width, 0);
    ctx.lineTo(0, 0);
    ctx.closePath();
    ctx.fillStyle = "rgba(59, 130, 246, 0.1)";
    ctx.fill();

    ctx.setTransform(1, 0, 0, 1, 0, 0);
}
