@extends('layouts.app')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2">Pembayaran Tertunda</h1>
                <p class="text-gray-600 dark:text-gray-400">Daftar pembayaran yang belum diselesaikan</p>
            </div>
            <div>
                <a href="{{ route('subscriptions.show') }}"
                    class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                    <i class="fas fa-arrow-left mr-1"></i> Kembali
                </a>
            </div>
        </div>

        @if (session('error'))
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400"
                role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        @if (session('success'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded dark:bg-green-900/30 dark:text-green-400"
                role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif

        <!-- Daftar Pembayaran Tertunda -->
        <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-blue-600 dark:bg-blue-700 p-4">
                <h2 class="text-white text-lg font-semibold">Pembayaran Tertunda</h2>
            </div>

            <div class="p-6">
                @if ($pendingPayments->isEmpty())
                    <div class="text-center py-8">
                        <i class="fas fa-credit-card text-gray-400 dark:text-gray-600 text-5xl mb-4"></i>
                        <p class="text-gray-600 dark:text-gray-400">Anda tidak memiliki pembayaran tertunda.</p>
                    </div>
                @else
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead>
                                <tr>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        No. Invoice</th>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Tanggal</th>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Paket</th>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Jumlah</th>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Status</th>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach ($pendingPayments as $payment)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-light-text dark:text-dark-text">
                                                {{ $payment->invoice_number }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                                {{ $payment->created_at->format('d F Y H:i') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-light-text dark:text-dark-text">
                                                {{ $payment->subscription->plan->name ?? 'Paket tidak ditemukan' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-light-text dark:text-dark-text">
                                                {{ $payment->formatted_amount }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if ($payment->status === 'pending')
                                                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900/30 dark:text-yellow-400">
                                                    Belum Dibayar
                                                </span>
                                            @elseif ($payment->status === 'processing')
                                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900/30 dark:text-blue-400">
                                                    Sedang Diproses
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            @if ($payment->status === 'pending')
                                                <a href="{{ route('payments.pending.resume', $payment->id) }}"
                                                    class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-1 px-3 rounded-lg transition-colors duration-150 mr-2">
                                                    <i class="fas fa-credit-card mr-1"></i> Bayar Sekarang
                                                </a>
                                                <button type="button" onclick="confirmCancel({{ $payment->id }})"
                                                    class="bg-red-500 hover:bg-red-600 text-white font-medium py-1 px-3 rounded-lg transition-colors duration-150">
                                                    <i class="fas fa-times mr-1"></i> Batalkan
                                                </button>
                                                <form id="cancel-form-{{ $payment->id }}"
                                                    action="{{ route('payments.pending.cancel', $payment->id) }}" method="POST"
                                                    class="hidden">
                                                    @csrf
                                                </form>
                                            @elseif ($payment->status === 'processing')
                                                <a href="{{ route('payments.show', $payment->id) }}"
                                                    class="bg-green-500 hover:bg-green-600 text-white font-medium py-1 px-3 rounded-lg transition-colors duration-150 mr-2">
                                                    <i class="fas fa-eye mr-1"></i> Lihat Detail
                                                </a>
                                                @php
                                                    $paymentVerificationMode = \App\Models\SystemSetting::getValue('payment_verification_mode', 'manual');
                                                @endphp
                                                @if ($paymentVerificationMode !== 'manual')
                                                    <button type="button" onclick="confirmCancel({{ $payment->id }})"
                                                        class="bg-red-500 hover:bg-red-600 text-white font-medium py-1 px-3 rounded-lg transition-colors duration-150">
                                                        <i class="fas fa-times mr-1"></i> Batalkan
                                                    </button>
                                                    <form id="cancel-form-{{ $payment->id }}"
                                                        action="{{ route('payments.pending.cancel', $payment->id) }}" method="POST"
                                                        class="hidden">
                                                        @csrf
                                                    </form>
                                                @endif
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Modal Konfirmasi Pembatalan -->
    <div id="cancelModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white dark:bg-dark-card p-0 rounded-lg shadow-lg max-w-md w-full mx-4 border-2 border-light-accent dark:border-dark-accent">
            <!-- Header -->
            <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10 rounded-t-lg">
                <div class="flex justify-between items-center">
                    <h3 class="text-white dark:text-dark-text text-lg font-semibold">Konfirmasi Pembatalan</h3>
                </div>
            </div>
            
            <!-- Content -->
            <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text">
                <p class="text-gray-600 dark:text-gray-400 mb-6">Apakah Anda yakin ingin membatalkan pembayaran ini?</p>
                <div class="flex justify-end gap-4 border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                    <button type="button" onclick="closeCancelModal()"
                        class="bg-gray-500 hover:bg-gray-600 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150">
                        Batal
                    </button>
                    <button type="button" id="confirmCancelButton"
                        class="bg-red-500 hover:bg-red-600 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150">
                        Ya, Batalkan
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        let paymentIdToCancel = null;

        function confirmCancel(paymentId) {
            paymentIdToCancel = paymentId;
            document.getElementById('cancelModal').classList.remove('hidden');
            document.getElementById('confirmCancelButton').addEventListener('click', cancelPayment);
        }

        function closeCancelModal() {
            document.getElementById('cancelModal').classList.add('hidden');
            document.getElementById('confirmCancelButton').removeEventListener('click', cancelPayment);
            paymentIdToCancel = null;
        }

        function cancelPayment() {
            if (paymentIdToCancel) {
                document.getElementById('cancel-form-' + paymentIdToCancel).submit();
            }
        }
    </script>
@endsection
