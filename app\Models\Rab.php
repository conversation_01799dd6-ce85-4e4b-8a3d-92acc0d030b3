<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Rab extends Model
{
    protected $fillable = [
        'project_id',
        'proyek_id',
        'judul',
        'jumlah_harga',
        'total_harga',
        'dibulatkan',
        'uraian_p<PERSON><PERSON><PERSON>an',
        'volume',
        'satuan',
        'harga_satuan',
        'ahs_id'
    ];

    protected $appends = ['total'];

    protected $casts = [
        'volume' => 'decimal:2',
        'harga_satuan' => 'decimal:2',
        'jumlah_harga' => 'decimal:2',
        'total_harga' => 'decimal:2',
        'dibulatkan' => 'decimal:2',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function proyek()
    {
        return $this->belongsTo(Project::class, 'proyek_id');
    }

    public function ahs()
    {
        return $this->belongsTo(Ahs::class);
    }

    public function getTotalAttribute()
    {
        return $this->jumlah_harga ?? ($this->volume * $this->harga_satuan) ?? 0;
    }
}
