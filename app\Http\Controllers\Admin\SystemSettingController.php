<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use Illuminate\Http\Request;

class SystemSettingController extends Controller
{
    /**
     * Display the system settings page.
     */
    public function index()
    {
        // Get payment verification mode setting
        $paymentVerificationMode = SystemSetting::getValue('payment_verification_mode', 'manual');

        // Get payment gateway settings
        $paymentGateway = SystemSetting::getValue('payment_gateway', 'xendit');

        // Xendit settings
        $xenditSecretKey = SystemSetting::getValue('xendit_secret_key', '');
        $xenditPublicKey = SystemSetting::getValue('xendit_public_key', '');
        $xenditCallbackToken = SystemSetting::getValue('xendit_callback_token', '');
        $xenditProduction = SystemSetting::getValue('xendit_production', false);

        return view('admin.settings.index', compact(
            'paymentVerificationMode',
            'paymentGateway',
            'xenditSecretKey',
            'xenditPublicKey',
            'xenditCallbackToken',
            'xenditProduction'
        ));
    }

    /**
     * Update system settings.
     */
    public function update(Request $request)
    {
        // Validate request
        $request->validate([
            'payment_verification_mode' => 'required|in:manual,automatic',
            'payment_gateway' => 'required_if:payment_verification_mode,automatic|in:xendit',
            'xendit_secret_key' => 'required_if:payment_gateway,xendit',
            'xendit_public_key' => 'required_if:payment_gateway,xendit',
            'xendit_callback_token' => 'required_if:payment_gateway,xendit',
        ]);

        // Update payment verification mode setting
        SystemSetting::setValue(
            'payment_verification_mode',
            $request->payment_verification_mode,
            'Mode verifikasi pembayaran (manual/automatic)'
        );

        // If automatic verification is enabled, update payment gateway settings
        if ($request->payment_verification_mode === 'automatic') {
            // Update payment gateway setting
            SystemSetting::setValue(
                'payment_gateway',
                $request->payment_gateway,
                'Payment gateway yang digunakan (xendit)'
            );

            // Update Xendit settings if Xendit is selected
            if ($request->payment_gateway === 'xendit') {
                SystemSetting::setValue(
                    'xendit_secret_key',
                    $request->xendit_secret_key,
                    'Xendit Secret Key'
                );

                SystemSetting::setValue(
                    'xendit_public_key',
                    $request->xendit_public_key,
                    'Xendit Public Key'
                );

                // Log callback token untuk debugging
                \Illuminate\Support\Facades\Log::info('Saving Xendit callback token', [
                    'token' => $request->xendit_callback_token
                ]);

                SystemSetting::setValue(
                    'xendit_callback_token',
                    $request->xendit_callback_token,
                    'Xendit Callback Token'
                );

                SystemSetting::setValue(
                    'xendit_production',
                    $request->has('xendit_production') ? true : false,
                    'Xendit Production Mode'
                );
            }
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Pengaturan sistem berhasil diperbarui.');
    }
}
