<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_plans', function (Blueprint $table) {
            $table->boolean('can_export_excel')->default(false)->comment('Akses untuk fitur Export Excel');
            $table->boolean('can_export_excel_formula')->default(false)->comment('Akses untuk fitur Export Excel dengan Rumus');
            $table->boolean('can_export_pdf')->default(false)->comment('Akses untuk fitur Export PDF');
            $table->boolean('can_use_time_schedule')->default(false)->comment('Akses untuk fitur Time Schedule');
            $table->boolean('can_use_empirical_ahsp')->default(false)->comment('Akses untuk fitur AHSP Empiris');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_plans', function (Blueprint $table) {
            $table->dropColumn([
                'can_export_excel',
                'can_export_excel_formula',
                'can_export_pdf',
                'can_use_time_schedule',
                'can_use_empirical_ahsp'
            ]);
        });
    }
};
