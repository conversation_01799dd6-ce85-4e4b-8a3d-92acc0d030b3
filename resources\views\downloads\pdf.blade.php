<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>RAB Estimator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            font-size: 18px;
            margin-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
            padding: 8px;
        }
        td {
            padding: 8px;
        }
        .project-info {
            margin-bottom: 20px;
        }
        .project-info h2 {
            font-size: 14px;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .project-info p {
            margin: 5px 0;
        }
        .total {
            font-weight: bold;
            text-align: right;
        }
        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>RAB ESTIMATOR</h1>
        <p><PERSON><PERSON><PERSON>n Biaya</p>
    </div>
    
    @foreach($proyeks as $proyek)
    <div class="project-info">
        <h2>{{ $proyek->nama_proyek }}</h2>
        <p><strong>Lokasi:</strong> {{ $proyek->lokasi }}</p>
        <p><strong>Tanggal Mulai:</strong> {{ $proyek->tanggal_mulai }}</p>
        <p><strong>Tanggal Selesai:</strong> {{ $proyek->tanggal_selesai }}</p>
        <p><strong>Status:</strong> {{ $proyek->status }}</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="40%">Uraian Pekerjaan</th>
                <th width="10%">Volume</th>
                <th width="10%">Satuan</th>
                <th width="15%">Harga Satuan (Rp)</th>
                <th width="20%">Jumlah Harga (Rp)</th>
            </tr>
        </thead>
        <tbody>
            @php $total = 0; @endphp
            @foreach($proyek->rabs as $index => $rab)
            <tr>
                <td style="text-align: center;">{{ $index + 1 }}</td>
                <td>{{ $rab->uraian_pekerjaan }}</td>
                <td style="text-align: center;">{{ $rab->volume }}</td>
                <td style="text-align: center;">{{ $rab->satuan }}</td>
                <td style="text-align: right;">{{ number_format($rab->harga_satuan, 0, ',', '.') }}</td>
                <td style="text-align: right;">{{ number_format($rab->volume * $rab->harga_satuan, 0, ',', '.') }}</td>
            </tr>
            @php $total += $rab->volume * $rab->harga_satuan; @endphp
            @endforeach
            <tr>
                <td colspan="5" class="total">TOTAL</td>
                <td style="text-align: right; font-weight: bold;">{{ number_format($total, 0, ',', '.') }}</td>
            </tr>
        </tbody>
    </table>
    
    @if(!$loop->last)
    <div class="page-break"></div>
    @endif
    @endforeach
</body>
</html>
