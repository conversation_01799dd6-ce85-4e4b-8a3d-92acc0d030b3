@extends('layouts.app')

@php
use Illuminate\Support\Facades\Auth;
@endphp

@section('content')
<div class="container mx-auto max-w-[1080px] p-6">

    @if(session('success'))
    <div class="bg-green-200 text-green-800 p-2 rounded mb-4">
        {{ session('success') }}
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-200 text-red-800 p-2 rounded mb-4">
        {{ session('error') }}
    </div>
    @endif

    <div class="flex justify-between items-center mb-4">
        <!-- Tombol untuk membuka modal full form input data baru -->
        <button type="button" onclick="openFullFormModal(false)" class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
            <i class="fas fa-plus-circle"></i> <PERSON>bah Analisa
        </button>
        <!-- Fitur Search -->
        <div class="flex items-center space-x-2">
            <form id="searchForm" action="{{ route('ahs.index') }}" method="GET" class="flex items-center space-x-2">
                <label for="searchAHS" class="text-sm font-medium">Cari Data:</label>
                <input type="text" id="searchAHS" name="search" placeholder="Cari..."
                       class="border px-3 py-2 rounded" value="{{ request('search') }}" oninput="filterTable()">
            </form>
        </div>
    </div>

    <!-- Tabel View Data dari Tabel "ahs" -->
    <div class="overflow-y-auto max-h-[620px]">
        <table id="ahsTable" class="text-sm min-w-full bg-white boder">
            <thead class="bg-blue-200 sticky top-0 z-10">
                <tr>
                    <th class="py-2 px-4 border">No.</th>
                    <th class="py-2 px-4 border">Kode Analisa</th>
                    <th class="py-2 px-4 border">Uraian Pekerjaan</th>
                    <th class="py-2 px-4 border">Satuan</th>
                    <th class="py-2 px-4 border">Harga Satuan Pekerjaan</th>
                    <th class="py-2 px-4 border">Overhead (%)</th>
                    <th class="py-2 px-4 border">Sumber</th>
                    <th class="py-2 px-4 border">Pembuat</th>
                    <th class="py-2 px-4 border">Aksi</th>
                </tr>
            </thead>
            <tbody>
                @foreach($ahsRecords as $index => $record)
                <tr class="hover:bg-blue-50" data-id="{{ $record->id }}" data-judul="{{ $record->judul }}">
                    <td class="py-2 px-4 border text-center">{{ ($ahsRecords->currentPage() - 1) * $ahsRecords->perPage() + $index + 1 }}</td>
                    <td class="py-2 px-4 border">{{ $record->kode }}</td>
                    <td class="py-2 px-4 border">{{ $record->judul }}</td>
                    <td class="py-2 px-4 border">{{ $record->satuan }}</td>
                    <td class="py-2 px-4 border">
                        <span class="float-left">Rp.</span>
                        <span class="float-right">{{ number_format($record->grand_total, 2) }}</span>
                    </td>
                    <td class="py-2 px-4 border text-center">{{ number_format($record->overhead, 2) }} %</td>
                    <td class="py-2 px-4 border">{{ $record->sumber }}</td>
                    <td class="py-2 px-4 border">
                        @if($record->creator)
                            {{ $record->creator->name }}
                        @else
                            {{ ucfirst($record->creator_role) }}
                        @endif
                    </td>
                    <td class="py-2 px-4 border text-center space-x-2">
                        <!-- Tombol Tiga Titik untuk Context Menu -->
                        @if(Auth::user()->role === 'admin' || $record->created_by === Auth::id())
                        <button type="button"
                                class="action-btn px-4 text-blue-500 hover:text-blue-700 p-1 rounded-full hover:bg-blue-200"
                                data-id="{{ $record->id }}"
                                data-kode="{{ $record->kode }}"
                                data-judul="{{ $record->judul }}"
                                data-satuan="{{ $record->satuan }}"
                                data-sumber="{{ $record->sumber }}">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        @else
                        <span class="text-gray-400">
                            <i class="fas fa-lock"></i>
                        </span>
                        @endif
                    </td>
                </tr>
                @endforeach
                @if($ahsRecords->isEmpty())
                <tr>
                    <td colspan="9" class="p-2 border text-center">Tidak ada data.</td>
                </tr>
                @endif
            </tbody>
        </table>
    </div>

    <!-- Pagination Links -->
    {{ $ahsRecords->links('components.user-fixed-pagination') }}
</div>

<!-- Context Menu -->
<div id="context-menu" class="hidden absolute bg-white dark:bg-dark-bg-secondary shadow-lg rounded-md py-2 w-32 border border-gray-200 dark:border-dark-accent/20 z-50">
    <button onclick="handleEdit(this)"
            class="w-full px-4 py-2 text-left text-blue-500 hover:bg-gray-100 hover:text-blue-900 text-sm flex items-center edit-btn">
        <i class="fas fa-edit mr-2 w-4 h-4"></i> Edit
    </button>
    <button onclick="handleDelete(this)"
            class="w-full px-4 py-2 text-left hover:bg-gray-100 text-sm flex items-center hover:text-red-900 text-red-500 delete-btn">
        <i class="fas fa-trash-alt mr-2 w-4 h-4"></i> Hapus
    </button>
</div>

<!-- Modal AHSP khusus (menggunakan Blade Component aHS Modal) -->
<x-ahs-modal />

@endsection

@section('scripts')
<script>
  // Inisialisasi variabel global dari controller
  window.bahanItems = @json($bahanItems);
  window.upahItems = @json($upahItems);
  window.alatItems = @json($alatItems);
  window.ahsBaseUrl = '{{ url("ahs") }}';
  window.ahsDetailStoreRoute = '{{ route("ahs.detail.store") }}';
  window.csrfToken = '{{ csrf_token() }}';
  window.isAdmin = {{ Auth::user()->role === 'admin' ? 'true' : 'false' }};

  // Fungsi untuk filter tabel berdasarkan input pencarian
  function filterTable() {
      const searchValue = document.getElementById("searchAHS").value.toLowerCase();

      // Untuk filter client-side pada halaman saat ini
      const rows = document.querySelectorAll("#ahsTable tbody tr");
      rows.forEach(row => {
          if (row.classList.contains('no-data-row')) return;
          const judul = row.querySelector("td:nth-child(3)").innerText.toLowerCase();
          const satuan = row.querySelector("td:nth-child(4)").innerText.toLowerCase();
          const sumber = row.querySelector("td:nth-child(7)").innerText.toLowerCase();
          row.style.display = (judul.includes(searchValue) || satuan.includes(searchValue) || sumber.includes(searchValue)) ? "" : "none";
      });

      // Jika tidak ada hasil yang terlihat, tampilkan pesan
      let visibleRows = 0;
      rows.forEach(row => {
          if (row.style.display !== 'none' && !row.classList.contains('no-data-row')) {
              visibleRows++;
          }
      });

      // Jika pengguna mengetik lebih dari 2 karakter, submit form untuk pencarian server-side
      if (searchValue.length > 2) {
          // Gunakan debounce untuk mengurangi jumlah request
          clearTimeout(window.searchTimeout);
          window.searchTimeout = setTimeout(() => {
              document.getElementById('searchForm').submit();
          }, 500);
      }
  }
</script>
@vite(['resources/js/modal.js', 'resources/js/ahsp.js', 'resources/js/detail-modal.js', 'resources/js/table-sort.js'])
@endsection
