document.addEventListener("DOMContentLoaded", function () {
    // Cek apakah ada alert success yang tersimpan di localStorage
    const upahSuccessAlert = localStorage.getItem("upahSuccessAlert");
    const upahSuccessTime = localStorage.getItem("upahSuccessTime");
    const alatSuccessAlert = localStorage.getItem("alatSuccessAlert");
    const alatSuccessTime = localStorage.getItem("alatSuccessTime");
    const bahanSuccessAlert = localStorage.getItem("bahanSuccessAlert");
    const bahanSuccessTime = localStorage.getItem("bahanSuccessTime");

    // Tampilkan alert success upah jika ada
    if (upahSuccessAlert && upahSuccessTime) {
        const currentTime = new Date().getTime();
        const alertTime = parseInt(upahSuccessTime);

        // Jika alert disimpan kurang dari 1 menit yang lalu
        if (currentTime - alertTime < 60000) {
            setTimeout(() => {
                window.showSuccessToast(upahSuccessAlert, null, null, 3000);
            }, 1000);
        }

        // Hapus alert dari localStorage setelah ditampilkan
        localStorage.removeItem("upahSuccessAlert");
        localStorage.removeItem("upahSuccessTime");
    }

    // Tampilkan alert success alat jika ada
    if (alatSuccessAlert && alatSuccessTime) {
        const currentTime = new Date().getTime();
        const alertTime = parseInt(alatSuccessTime);

        // Jika alert disimpan kurang dari 1 menit yang lalu
        if (currentTime - alertTime < 60000) {
            setTimeout(() => {
                window.showSuccessToast(alatSuccessAlert, null, null, 3000);
            }, 1000);
        }

        // Hapus alert dari localStorage setelah ditampilkan
        localStorage.removeItem("alatSuccessAlert");
        localStorage.removeItem("alatSuccessTime");
    }

    // Tampilkan alert success bahan jika ada
    if (bahanSuccessAlert && bahanSuccessTime) {
        const currentTime = new Date().getTime();
        const alertTime = parseInt(bahanSuccessTime);

        // Jika alert disimpan kurang dari 1 menit yang lalu
        if (currentTime - alertTime < 60000) {
            setTimeout(() => {
                window.showSuccessToast(bahanSuccessAlert, null, null, 3000);
            }, 1000);
        }

        // Hapus alert dari localStorage setelah ditampilkan
        localStorage.removeItem("bahanSuccessAlert");
        localStorage.removeItem("bahanSuccessTime");
    }

    // === Fungsi Dasar Modal ===
    window.openModal = function (id) {
        const modal = document.getElementById(id);
        if (modal) {
            modal.classList.remove("hidden");
        }
    };

    window.closeModal = function (id) {
        const modal = document.getElementById(id);
        if (modal) {
            modal.classList.add("hidden");
        }
    };

    // === Utility Functions ===
    function setMethodInput(form, method) {
        let methodInput = form.querySelector('input[name="_method"]');
        if (!methodInput) {
            methodInput = document.createElement("input");
            methodInput.type = "hidden";
            methodInput.name = "_method";
            methodInput.value = method;
            form.appendChild(methodInput);
        } else {
            methodInput.value = method;
        }

        // Ensure the form method is POST when using _method for PUT/DELETE/etc.
        // Laravel expects a POST with _method field
        if (method !== "GET" && method !== "POST") {
            form.method = "POST";
        }
    }

    function removeMethodInput(form) {
        let methodInput = form.querySelector('input[name="_method"]');
        if (methodInput) methodInput.remove();
    }

    function setInputValue(id, value) {
        const input = document.getElementById(id);
        if (input) {
            input.value = value || "";
        }
    }

    // Custom alert function - moved here to ensure it's defined before use
    function showCustomAlert(message, callback) {
        // Gunakan window.showInfoToast yang dibuat di notifications.js
        window.showInfoToast(message, null, callback, 3000);
    }

    // === Modal Bahan (Tambah/Edit) ===
    window.openBahanModal = function (
        isEdit,
        id = "",
        uraian = "",
        satuan = "",
        harga = "",
        sumber = ""
    ) {
        // Store the current state for reopening detail modal later
        const wasDetailModalOpen =
            document.getElementById("detailModal") &&
            !document
                .getElementById("detailModal")
                .classList.contains("hidden");

        // Close the detail modal first if it's open
        if (typeof window.closeDetailModal === "function") {
            window.closeDetailModal();
        }

        const modalId = "bahanModal";
        const form = document.getElementById("bahanForm");
        const titleEl = document.getElementById("bahanModalTitle");
        if (!form || !titleEl) {
            console.error("Elemen modal Bahan tidak ditemukan.");
            return;
        }
        if (isEdit && id) {
            titleEl.innerText = "Edit Bahan";
            form.action = `/bahan/${id}`;
            setMethodInput(form, "PUT");
        } else {
            titleEl.innerText = "Tambah Bahan";
            form.action = form.getAttribute("data-original-action") || "/bahan";
            removeMethodInput(form);
            form.reset();
        }
        setInputValue("uraian_bahan", uraian);
        setInputValue("satuan_bahan", satuan);
        setInputValue("harga_bahan", harga);
        setInputValue("sumber_bahan", sumber);

        // Save state for reopening
        form.setAttribute(
            "data-reopen-detail",
            wasDetailModalOpen ? "true" : "false"
        );

        openModal(modalId);
    };

    window.closeBahanModal = function () {
        const form = document.getElementById("bahanForm");
        const shouldReopenDetail =
            form && form.getAttribute("data-reopen-detail") === "true";

        closeModal("bahanModal");

        // Reopen detail modal if it was open before
        if (
            shouldReopenDetail &&
            typeof window.openDetailModal === "function"
        ) {
            setTimeout(() => {
                window.openDetailModal("Bahan");
            }, 100);
        }
    };

    // === Modal Upah (Tambah/Edit) ===
    window.openUpahModal = function (
        isEdit,
        id = "",
        uraian = "",
        satuan = "",
        harga = "",
        sumber = ""
    ) {
        // Store the current state for reopening detail modal later
        const wasDetailModalOpen =
            document.getElementById("detailModal") &&
            !document
                .getElementById("detailModal")
                .classList.contains("hidden");

        // Close the detail modal first if it's open
        if (typeof window.closeDetailModal === "function") {
            window.closeDetailModal();
        }

        const modalId = "upahModal";
        const form = document.getElementById("upahForm");
        const titleEl = document.getElementById("upahModalTitle");
        if (!form || !titleEl) {
            console.error("Elemen modal Upah tidak ditemukan.");
            return;
        }
        if (isEdit && id) {
            titleEl.innerText = "Edit Upah";
            form.action = `/upah/${id}`;
            setMethodInput(form, "PUT");
        } else {
            titleEl.innerText = "Tambah Upah";
            form.action = form.getAttribute("data-original-action") || "/upah";
            removeMethodInput(form);
            form.reset();
        }
        setInputValue("uraian_tenaga", uraian);
        setInputValue("satuan_upah", satuan);
        setInputValue("harga_upah", harga);
        setInputValue("sumber_upah", sumber);

        // Save state for reopening
        form.setAttribute(
            "data-reopen-detail",
            wasDetailModalOpen ? "true" : "false"
        );

        openModal(modalId);
    };

    window.closeUpahModal = function () {
        const form = document.getElementById("upahForm");
        const shouldReopenDetail =
            form && form.getAttribute("data-reopen-detail") === "true";

        closeModal("upahModal");

        // Reopen detail modal if it was open before
        if (
            shouldReopenDetail &&
            typeof window.openDetailModal === "function"
        ) {
            setTimeout(() => {
                window.openDetailModal("Upah");
            }, 100);
        }
    };

    // === Modal Alat (Tambah/Edit) ===
    window.openAlatModal = function (
        isEdit,
        id = "",
        uraian = "",
        satuan = "",
        harga = "",
        sumber = ""
    ) {
        // Store the current state for reopening detail modal later
        const wasDetailModalOpen =
            document.getElementById("detailModal") &&
            !document
                .getElementById("detailModal")
                .classList.contains("hidden");

        // Close the detail modal first if it's open
        if (typeof window.closeDetailModal === "function") {
            window.closeDetailModal();
        }

        const modalId = "alatModal";
        const form = document.getElementById("alatForm");
        const titleEl = document.getElementById("alatModalTitle");
        if (!form || !titleEl) {
            console.error("Elemen modal Alat tidak ditemukan.");
            return;
        }
        if (isEdit && id) {
            titleEl.innerText = "Edit Alat";
            form.action = `/alat/${id}`;
            setMethodInput(form, "PUT");
        } else {
            titleEl.innerText = "Tambah Alat";
            form.action = form.getAttribute("data-original-action") || "/alat";
            removeMethodInput(form);
            form.reset();
        }
        setInputValue("uraian_alat", uraian);
        setInputValue("satuan_alat", satuan);
        setInputValue("harga_alat", harga);
        setInputValue("sumber_alat", sumber);

        // Save state for reopening
        form.setAttribute(
            "data-reopen-detail",
            wasDetailModalOpen ? "true" : "false"
        );

        openModal(modalId);
    };

    window.closeAlatModal = function () {
        const form = document.getElementById("alatForm");
        const shouldReopenDetail =
            form && form.getAttribute("data-reopen-detail") === "true";

        closeModal("alatModal");

        // Reopen detail modal if it was open before
        if (
            shouldReopenDetail &&
            typeof window.openDetailModal === "function"
        ) {
            setTimeout(() => {
                window.openDetailModal("Alat");
            }, 100);
        }
    };

    // === Modal Upah AHS (Tambah) ===
    window.openUpahAHSModal = function () {
        // Store the state of the detail modal
        window.savedModalState = {
            type: "detail",
            category: window.currentDetailCategory || "Upah",
        };

        const modalId = "upahahsModal";
        const form = document.getElementById("upahahsForm");
        if (!form) {
            console.error("Elemen modal Upah AHS tidak ditemukan.");
            return;
        }
        form.reset();
        openModal(modalId);
    };

    window.closeUpahAHSModal = function () {
        closeModal("upahahsModal");

        // Reopen detail modal if needed
        if (
            window.savedModalState &&
            window.savedModalState.type === "detail"
        ) {
            setTimeout(() => {
                if (typeof openDetailModal === "function") {
                    openDetailModal(window.savedModalState.category || "Upah");
                }
                // Clear saved state
                window.savedModalState = null;
            }, 100);
        }
    };

    // === Modal Alat AHS (Tambah) ===
    window.openAlatAHSModal = function () {
        // Store the state of the detail modal
        window.savedModalState = {
            type: "detail",
            category: window.currentDetailCategory || "Alat",
        };

        const modalId = "alatahsModal";
        const form = document.getElementById("alatahsForm");
        if (!form) {
            console.error("Elemen modal Alat AHS tidak ditemukan.");
            return;
        }
        form.reset();
        openModal(modalId);
    };

    window.closeAlatAHSModal = function () {
        closeModal("alatahsModal");

        // Reopen detail modal if needed
        if (
            window.savedModalState &&
            window.savedModalState.type === "detail"
        ) {
            setTimeout(() => {
                if (typeof openDetailModal === "function") {
                    openDetailModal(window.savedModalState.category || "Alat");
                }
                // Clear saved state
                window.savedModalState = null;
            }, 100);
        }
    };

    // === Modal Bahan AHS (Tambah) ===
    window.openBahanAHSModal = function () {
        // Store the state of the detail modal
        window.savedModalState = {
            type: "detail",
            category: window.currentDetailCategory || "Bahan",
        };

        const modalId = "bahanahsModal";
        const form = document.getElementById("bahanahsForm");
        if (!form) {
            console.error("Elemen modal Bahan AHS tidak ditemukan.");
            return;
        }
        form.reset();
        openModal(modalId);
    };

    window.closeBahanAHSModal = function () {
        closeModal("bahanahsModal");

        // Reopen detail modal if needed
        if (
            window.savedModalState &&
            window.savedModalState.type === "detail"
        ) {
            setTimeout(() => {
                if (typeof openDetailModal === "function") {
                    openDetailModal(window.savedModalState.category || "Bahan");
                }
                // Clear saved state
                window.savedModalState = null;
            }, 100);
        }
    };

    // === Handle Resource Form Submit ===
    window.handleResourceSubmit = function (event, resourceType) {
        event.preventDefault();
        const form = event.target;
        const formData = new FormData(form);

        // Get CSRF token from meta tag
        const token = document
            .querySelector('meta[name="csrf-token"]')
            .getAttribute("content");

        // Show loading indicator
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML =
                '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
            submitBtn.disabled = true;
        }

        console.log("Submitting form:", form.action, resourceType);

        fetch(form.action, {
            method: form.method,
            headers: {
                "X-CSRF-TOKEN": token,
                Accept: "application/json",
            },
            body: formData,
        })
            .then((response) => {
                // Log response details to help debug
                console.log("Response status:", response.status);
                console.log(
                    "Response type:",
                    response.headers.get("content-type")
                );

                // Check if the response is OK and if it's JSON
                if (!response.ok) {
                    // If the response is not OK, throw an error with the status
                    throw new Error(
                        `Server responded with status: ${response.status}`
                    );
                }

                const contentType = response.headers.get("content-type");
                if (contentType && contentType.includes("application/json")) {
                    return response.json();
                } else {
                    // Clone response to read it twice - once for error message, once for JSON parsing attempt
                    return response.text().then((text) => {
                        console.error(
                            "Non-JSON response:",
                            text.substring(0, 500)
                        ); // Log first 500 chars
                        throw new Error(
                            "Received non-JSON response from server"
                        );
                    });
                }
            })
            .then((result) => {
                if (result.success) {
                    // Store which modal to reopen
                    const reopenCategory =
                        resourceType === "upah"
                            ? "Upah"
                            : resourceType === "alat"
                            ? "Alat"
                            : resourceType === "bahan"
                            ? "Bahan"
                            : null;

                    // Save state for reopening
                    if (reopenCategory) {
                        window.savedModalState = {
                            type: "detail",
                            category: reopenCategory,
                        };
                    }

                    // Close the resource modal
                    if (resourceType === "upah") closeUpahAHSModal();
                    if (resourceType === "alat") closeAlatAHSModal();
                    if (resourceType === "bahan") closeBahanAHSModal();

                    // Add the new resource to the appropriate global array
                    const resource = result.resource;

                    if (resourceType === "upah" && window.upahItems) {
                        window.upahItems.unshift(resource);
                        // Refresh the display if this category is currently open
                        if (
                            window.currentDetailCategory === "Upah" &&
                            typeof window.populateMasterTable === "function"
                        ) {
                            window.populateMasterTable("Upah");
                        }
                    } else if (resourceType === "alat" && window.alatItems) {
                        window.alatItems.unshift(resource);
                        if (
                            window.currentDetailCategory === "Alat" &&
                            typeof window.populateMasterTable === "function"
                        ) {
                            window.populateMasterTable("Alat");
                        }
                    } else if (resourceType === "bahan" && window.bahanItems) {
                        window.bahanItems.unshift(resource);
                        if (
                            window.currentDetailCategory === "Bahan" &&
                            typeof window.populateMasterTable === "function"
                        ) {
                            window.populateMasterTable("Bahan");
                        }
                    }

                    // Show success message
                    alert(result.message || "Data berhasil disimpan");

                    // The closeXxxAHSModal functions will handle reopening the detail modal
                } else {
                    // Show error message
                    alert(
                        result.message ||
                            "Terjadi kesalahan saat menyimpan data"
                    );
                }
            })
            .catch((error) => {
                console.error("Error:", error.message);
                // Try to log more details about the failed request
                console.error("Form action:", form.action);
                console.error("Form method:", form.method);

                showCustomAlert(
                    "Terjadi kesalahan saat menyimpan data. Silakan coba lagi nanti. Detail: " +
                        error.message
                );
            })
            .finally(() => {
                // Reset button state
                if (submitBtn) {
                    submitBtn.innerHTML = "Simpan";
                    submitBtn.disabled = false;
                }
            });

        return false;
    };

    // === Tambah Baru Button Handler ===
    window.openTambahModal = function () {
        const selectedCategory =
            document
                .getElementById("selectedCategoryLabel")
                ?.textContent.trim() || "";

        // Close the detail modal first
        if (typeof closeDetailModal === "function") {
            // Store modal state for reopening later
            window.savedModalState = {
                type: "detail",
                category: selectedCategory,
            };
            closeDetailModal();
        }

        switch (selectedCategory) {
            case "Upah":
                openUpahAHSModal();
                break;
            case "Alat":
                openAlatAHSModal();
                break;
            case "Bahan":
                openBahanAHSModal();
                break;
            default:
                console.error("Kategori tidak valid:", selectedCategory);
        }
    };

    // === Context Menu untuk Edit & Hapus ===
    let currentRecord = null;
    const contextMenu = document.getElementById("context-menu");

    if (contextMenu) {
        document.querySelectorAll(".action-btn").forEach((button) => {
            button.addEventListener("click", function (e) {
                e.preventDefault();
                currentRecord = {
                    id: this.dataset.id,
                    uraian: this.dataset.uraian,
                    satuan: this.dataset.satuan,
                    harga: this.dataset.harga,
                    sumber: this.dataset.sumber,
                    module: this.dataset.module, // misalnya "bahan", "upah", atau "alat"
                    x: e.pageX,
                    y: e.pageY,
                };
                positionContextMenu(e);
                showContextMenu();
            });
        });
    }

    function positionContextMenu(e) {
        if (!contextMenu) return;
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const menuWidth = 128;
        const menuHeight = 80;
        let x = e.pageX;
        let y = e.pageY;
        if (x + menuWidth > viewportWidth) x = viewportWidth - menuWidth;
        if (y + menuHeight > viewportHeight) y = viewportHeight - menuHeight;
        contextMenu.style.left = x + "px";
        contextMenu.style.top = y + "px";
    }

    function showContextMenu() {
        if (contextMenu) contextMenu.classList.remove("hidden");
    }

    document.addEventListener("click", function (e) {
        if (
            contextMenu &&
            !contextMenu.contains(e.target) &&
            !e.target.closest(".action-btn")
        ) {
            contextMenu.classList.add("hidden");
        }
    });

    // Context Menu Edit Handler
    window.contextMenuEdit = function () {
        if (currentRecord) {
            // Store if detail modal is open
            const detailModal = document.getElementById("detailModal");
            const wasDetailModalOpen =
                detailModal && !detailModal.classList.contains("hidden");

            // Close the detail modal first if it's open
            if (
                wasDetailModalOpen &&
                typeof window.closeDetailModal === "function"
            ) {
                window.closeDetailModal();
            }

            if (currentRecord.module === "bahan") {
                openBahanModal(
                    true,
                    currentRecord.id,
                    currentRecord.uraian,
                    currentRecord.satuan,
                    currentRecord.harga,
                    currentRecord.sumber
                );
            } else if (currentRecord.module === "upah") {
                openUpahModal(
                    true,
                    currentRecord.id,
                    currentRecord.uraian,
                    currentRecord.satuan,
                    currentRecord.harga,
                    currentRecord.sumber
                );
            } else if (currentRecord.module === "alat") {
                openAlatModal(
                    true,
                    currentRecord.id,
                    currentRecord.uraian,
                    currentRecord.satuan,
                    currentRecord.harga,
                    currentRecord.sumber
                );
            }
        }
        if (contextMenu) contextMenu.classList.add("hidden");
    };

    // Fungsi showCustomConfirm telah dipindahkan ke file confirm.js
    // dan tersedia secara global sebagai window.showCustomConfirm

    window.handleDelete = function () {
        if (currentRecord) {
            showCustomConfirm(
                `Anda yakin ingin menghapus "${currentRecord.uraian}"?`,
                () => {
                    let deleteUrl = "";
                    if (currentRecord.module === "bahan") {
                        deleteUrl = `/bahan/${currentRecord.id}`;
                    } else if (currentRecord.module === "upah") {
                        deleteUrl = `/upah/${currentRecord.id}`;
                    } else if (currentRecord.module === "alat") {
                        deleteUrl = `/alat/${currentRecord.id}`;
                    }
                    const form = document.createElement("form");
                    form.action = deleteUrl;
                    form.method = "POST";
                    form.innerHTML = `
                        <input type="hidden" name="_token" value="${window.csrfToken}">
                        <input type="hidden" name="_method" value="DELETE">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                },
                () => {
                    console.log("Hapus dibatalkan.");
                }
            );
        }
        if (contextMenu) contextMenu.classList.add("hidden");
    };

    // === AHSP Modal Functions ===
    window.closeFullFormModal = function () {
        const modal = document.getElementById("fullFormModal");
        if (modal) {
            modal.classList.add("hidden");
        }

        // Cek apakah inputKategoriModal sedang terbuka
        const inputKategoriModal =
            document.getElementById("inputKategoriModal");
        const isInputKategoriModalOpen =
            inputKategoriModal &&
            !inputKategoriModal.classList.contains("hidden");

        // Reopen detail modal if needed, but only if inputKategoriModal is not open
        if (
            window.currentItem &&
            window.currentItem.kategoriId &&
            !isInputKategoriModalOpen
        ) {
            setTimeout(() => {
                if (typeof window.openDetailModal === "function") {
                    window.openDetailModal(window.currentItem.kategoriId);
                }
            }, 100);
        }
    };

    window.openFullFormModal = function (
        isEdit = false,
        itemId = null,
        kategoriId = null
    ) {
        // Store detail modal state for reopening
        if (typeof closeDetailModal === "function") {
            window.savedModalState = {
                type: "detail",
                category: kategoriId,
            };
            closeDetailModal();
        }

        // Set the current item and category
        window.currentItem = {
            itemId: itemId,
            kategoriId: kategoriId,
        };

        // Open the full form modal
        const modal = document.getElementById("fullFormModal");
        if (modal) {
            modal.classList.remove("hidden");
        }
    };

    // Note: The editAhsp function is defined in ahsrab.js and should not be redefined here

    window.handleEdit = function (button) {
        // Get the item data from the button's data attributes
        const itemId = button.getAttribute("data-id");
        const kategoriId = button.getAttribute("data-kategori-id");

        // Store detail modal state for reopening
        if (typeof closeDetailModal === "function") {
            window.savedModalState = {
                type: "detail",
                category: kategoriId,
            };
            closeDetailModal();
        }

        // Set the current item and category
        window.currentItem = {
            itemId: itemId,
            kategoriId: kategoriId,
        };

        // Open the full form modal
        const modal = document.getElementById("fullFormModal");
        if (modal) {
            modal.classList.remove("hidden");
        }
    };

    // === Event Listeners ===
    const setupModalCloseListeners = function () {
        // Close button for full form modal
        const fullFormCloseButton = document.querySelector(
            "#fullFormModal .close-modal"
        );
        if (fullFormCloseButton) {
            fullFormCloseButton.addEventListener("click", function () {
                closeFullFormModal();
                // Reopening detail modal is handled in closeFullFormModal
            });
        }

        // Close buttons for AHS resource modals
        const resourceCloseButtons = document.querySelectorAll(
            '[id$="ahsModal"] .close-modal'
        );
        resourceCloseButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const modalId = this.closest('[id$="Modal"]').id;
                if (modalId === "upahahsModal") {
                    closeUpahAHSModal();
                } else if (modalId === "alatahsModal") {
                    closeAlatAHSModal();
                } else if (modalId === "bahanahsModal") {
                    closeBahanAHSModal();
                }
                // Reopening detail modal is handled in closeXxxAHSModal
            });
        });

        // Close buttons for basic resource modals
        const basicResourceCloseButtons = document.querySelectorAll(
            '[id$="Modal"]:not([id$="ahsModal"]):not([id="fullFormModal"]):not([id="detailModal"]) .close-modal'
        );
        basicResourceCloseButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const modalId = this.closest('[id$="Modal"]').id;
                if (modalId === "upahModal") {
                    closeUpahModal();
                } else if (modalId === "alatModal") {
                    closeAlatModal();
                } else if (modalId === "bahanModal") {
                    closeBahanModal();
                }
            });
        });
    };

    // Setup form submission handler for fullForm
    const setupFullFormSubmitHandler = function () {
        const fullForm = document.getElementById("fullForm");
        if (fullForm) {
            fullForm.addEventListener("submit", function (event) {
                event.preventDefault();
                const formData = new FormData(this);

                // Get CSRF token
                const token = document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content");

                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML =
                        '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
                    submitBtn.disabled = true;
                }

                fetch(this.action, {
                    method: this.method,
                    headers: {
                        "X-CSRF-TOKEN": token,
                    },
                    body: formData,
                })
                    .then((response) => {
                        if (!response.ok) {
                            throw new Error("Network response was not ok");
                        }
                        return response.json();
                    })
                    .then((result) => {
                        if (result.success) {
                            // Close the full form modal
                            closeFullFormModal(); // This will handle reopening detail modal

                            // Show success message
                            alert(result.message || "Data berhasil disimpan");
                        } else {
                            alert(
                                result.message ||
                                    "Terjadi kesalahan saat menyimpan data"
                            );
                        }
                    })
                    .catch((error) => {
                        console.error("Error:", error.message);
                        // Try to log more details about the failed request
                        console.error("Form action:", this.action);
                        console.error("Form method:", this.method);

                        showCustomAlert(
                            "Terjadi kesalahan saat menyimpan data. Silakan coba lagi nanti. Detail: " +
                                error.message
                        );
                    })
                    .finally(() => {
                        // Reset button state
                        if (submitBtn) {
                            submitBtn.innerHTML = "Simpan";
                            submitBtn.disabled = false;
                        }
                    });
            });
        }
    };

    // Setup form submission handlers
    const setupFormSubmitHandlers = function () {
        // Generic form submission handler
        const handleFormSubmit = function (event) {
            event.preventDefault();
            const form = event.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML =
                    '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
                submitBtn.disabled = true;
            }

            // Get CSRF token
            const token =
                document
                    .querySelector('meta[name="csrf-token"]')
                    ?.getAttribute("content") || window.csrfToken;

            // Create FormData object and log form values for debugging
            const formData = new FormData(form);
            console.log("Form ID:", form.id);
            console.log("Form action:", form.action);
            console.log("Form method:", form.method);

            // Log form field values for debugging
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
            }

            // Check if _method field exists when method is PUT
            if (
                form.method.toLowerCase() === "post" &&
                form.querySelector('input[name="_method"][value="PUT"]')
            ) {
                console.log("Using PUT method via _method field");
            }

            // Make AJAX request
            fetch(form.action, {
                method: form.method,
                headers: {
                    "X-CSRF-TOKEN": token,
                    Accept: "application/json",
                },
                body: formData,
            })
                .then((response) => {
                    // Log response details to help debug
                    console.log("Response status:", response.status);
                    console.log(
                        "Response type:",
                        response.headers.get("content-type")
                    );

                    // Check if the response is OK and if it's JSON
                    if (!response.ok) {
                        // If the response is not OK, throw an error with the status
                        throw new Error(
                            `Server responded with status: ${response.status}`
                        );
                    }

                    const contentType = response.headers.get("content-type");
                    if (
                        contentType &&
                        contentType.includes("application/json")
                    ) {
                        return response.json();
                    } else {
                        // Clone response to read it twice - once for error message, once for JSON parsing attempt
                        return response.text().then((text) => {
                            console.error(
                                "Non-JSON response:",
                                text.substring(0, 500)
                            ); // Log first 500 chars
                            throw new Error(
                                "Received non-JSON response from server"
                            );
                        });
                    }
                })
                .then((data) => {
                    if (data.success) {
                        // Simpan success message ke localStorage berdasarkan jenis form
                        if (form.id === "upahForm") {
                            localStorage.setItem(
                                "upahSuccessAlert",
                                data.message || "Data upah berhasil disimpan."
                            );
                            localStorage.setItem(
                                "upahSuccessTime",
                                new Date().getTime().toString()
                            );
                        } else if (form.id === "bahanForm") {
                            localStorage.setItem(
                                "bahanSuccessAlert",
                                data.message || "Data bahan berhasil disimpan."
                            );
                            localStorage.setItem(
                                "bahanSuccessTime",
                                new Date().getTime().toString()
                            );
                        } else if (form.id === "alatForm") {
                            localStorage.setItem(
                                "alatSuccessAlert",
                                data.message || "Data alat berhasil disimpan."
                            );
                            localStorage.setItem(
                                "alatSuccessTime",
                                new Date().getTime().toString()
                            );
                        }

                        // Update AHSP tables if editing from there
                        if (
                            window.currentEditingDetail &&
                            window.currentEditingDetail.itemId
                        ) {
                            const category =
                                window.currentEditingDetail.category;
                            const index = window.currentEditingDetail.index;

                            // Update the item in the AHSP detail arrays
                            if (
                                category === "Bahan" &&
                                window.detailBahan &&
                                window.detailBahan[index]
                            ) {
                                let item = window.detailBahan[index];
                                item.item_text = data.data.uraian_bahan;
                                item.satuan = data.data.satuan;
                                item.harga_dasar = data.data.harga_bahan;
                                item.harga_satuan =
                                    parseFloat(data.data.harga_bahan) *
                                    parseFloat(item.koefisien);
                            } else if (
                                category === "Upah" &&
                                window.detailUpah &&
                                window.detailUpah[index]
                            ) {
                                let item = window.detailUpah[index];
                                item.item_text = data.data.uraian_tenaga;
                                item.satuan = data.data.satuan;
                                item.harga_dasar = data.data.harga;
                                item.harga_satuan =
                                    parseFloat(data.data.harga) *
                                    parseFloat(item.koefisien);
                            } else if (
                                category === "Alat" &&
                                window.detailAlat &&
                                window.detailAlat[index]
                            ) {
                                let item = window.detailAlat[index];
                                item.item_text = data.data.uraian_alat;
                                item.satuan = data.data.satuan;
                                item.harga_dasar = data.data.harga_alat;
                                item.harga_satuan =
                                    parseFloat(data.data.harga_alat) *
                                    parseFloat(item.koefisien);
                            }

                            // Update the AHSP table
                            if (
                                typeof window.updateDetailTable === "function"
                            ) {
                                window.updateDetailTable(category);
                            }

                            // Reset currentEditingDetail
                            window.currentEditingDetail = null;
                        }

                        // Close modal
                        if (form.id === "upahForm") {
                            closeUpahModal();
                        } else if (form.id === "bahanForm") {
                            closeBahanModal();
                        } else if (form.id === "alatForm") {
                            closeAlatModal();
                        }

                        // Reload the page for table refresh (optional)
                        if (!window.currentEditingDetail) {
                            location.reload();
                        }
                    } else {
                        // Show error message
                        showCustomAlert(
                            data.message ||
                                "Terjadi kesalahan saat menyimpan data."
                        );
                    }
                })
                .catch((error) => {
                    console.error("Error:", error.message);
                    // Try to log more details about the failed request
                    console.error("Form action:", form.action);
                    console.error("Form method:", form.method);

                    showCustomAlert(
                        "Terjadi kesalahan saat menyimpan data. Silakan coba lagi nanti. Detail: " +
                            error.message
                    );
                })
                .finally(() => {
                    if (submitBtn) {
                        submitBtn.innerHTML = "Simpan";
                        submitBtn.disabled = false;
                    }
                });
        };

        // Attach event listener to forms
        const upahForm = document.getElementById("upahForm");
        if (upahForm) {
            upahForm.addEventListener("submit", handleFormSubmit);
        }

        const bahanForm = document.getElementById("bahanForm");
        if (bahanForm) {
            bahanForm.addEventListener("submit", handleFormSubmit);
        }

        const alatForm = document.getElementById("alatForm");
        if (alatForm) {
            alatForm.addEventListener("submit", handleFormSubmit);
        }
    };

    // Initialize all event listeners
    setupModalCloseListeners();
    setupFullFormSubmitHandler();
    setupFormSubmitHandlers();
});
