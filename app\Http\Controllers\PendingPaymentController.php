<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Payment;
use App\Models\Subscription;

class PendingPaymentController extends Controller
{
    /**
     * Display a listing of the pending payments.
     */
    public function index()
    {
        $user = Auth::user();
        $pendingPayments = Payment::where('user_id', $user->id)
            ->whereIn('status', ['pending', 'processing'])
            ->with(['subscription.plan'])
            ->orderBy('created_at', 'desc')
            ->get();

        return view('payments.pending', compact('pendingPayments'));
    }

    /**
     * Resume a pending payment.
     */
    public function resume($id)
    {
        $payment = Payment::findOrFail($id);

        // Pastikan user hanya bisa melanjutkan pembayarannya sendiri
        if ($payment->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Pastikan payment masih pending
        if ($payment->status !== 'pending') {
            return redirect()->route('payments.pending')
                ->with('error', 'Hanya pembayaran dengan status pending yang dapat dilanjutkan.');
        }

        // Get payment gateway from system settings
        $paymentGateway = \App\Models\SystemSetting::getValue('payment_gateway', 'xendit');

        // Log untuk debugging
        \Illuminate\Support\Facades\Log::info('Resuming payment', [
            'payment_id' => $payment->id,
            'payment_method' => $payment->payment_method,
            'system_payment_gateway' => $paymentGateway
        ]);

        // Jika menggunakan Xendit, buat invoice baru
        if ($paymentGateway === 'xendit') {
            return $this->resumeXenditPayment($payment);
        }

        // Untuk metode pembayaran lain, redirect ke halaman detail pembayaran
        return redirect()->route('payments.show', $payment->id);
    }

    /**
     * Resume a Xendit payment.
     */
    private function resumeXenditPayment($payment)
    {
        // Get payment gateway configuration
        $xenditPublicKey = \App\Models\SystemSetting::getValue('xendit_public_key');
        $xenditSecretKey = \App\Models\SystemSetting::getValue('xendit_secret_key');

        if (empty($xenditSecretKey)) {
            return redirect()->route('payments.pending')
                ->with('error', 'Konfigurasi Xendit belum lengkap. Silakan hubungi administrator.');
        }

        // Set Xendit API URL
        $apiUrl = 'https://api.xendit.co/v2/invoices';

        // Prepare invoice data
        $invoiceData = [
            'external_id' => $payment->payment_id,
            'amount' => (int) $payment->amount,
            'payer_email' => Auth::user()->email,
            'description' => $payment->description,
            'success_redirect_url' => route('subscriptions.success'),
            'failure_redirect_url' => route('subscriptions.error'),
            'callback_url' => route('payments.callback')
        ];

        // Set headers
        $headers = [
            'Content-Type: application/json',
            'Authorization: Basic ' . base64_encode($xenditSecretKey . ':')
        ];

        // Initialize cURL
        $ch = curl_init($apiUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_USERPWD, $xenditSecretKey . ':');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($invoiceData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Execute cURL
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // Check if request was successful
        if ($httpCode == 200) {
            $responseData = json_decode($response, true);

            // Redirect to Xendit invoice URL
            return redirect()->away($responseData['invoice_url']);
        } else {
            // Log error
            \Illuminate\Support\Facades\Log::error('Failed to create Xendit invoice', [
                'payment_id' => $payment->id,
                'response' => $response,
                'http_code' => $httpCode
            ]);

            return redirect()->route('payments.pending')
                ->with('error', 'Gagal membuat invoice Xendit. Silakan coba lagi nanti.');
        }
    }

    /**
     * Cancel a pending payment.
     */
    public function cancel($id)
    {
        $payment = Payment::findOrFail($id);

        // Pastikan user hanya bisa membatalkan pembayarannya sendiri
        if ($payment->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Pastikan payment masih pending atau processing
        if (!in_array($payment->status, ['pending', 'processing'])) {
            return redirect()->route('payments.pending')
                ->with('error', 'Hanya pembayaran dengan status pending atau sedang diproses yang dapat dibatalkan.');
        }

        // Update payment status
        $payment->status = 'cancelled';
        $payment->save();

        // Jika ada subscription terkait, update statusnya
        if ($payment->subscription) {
            $payment->subscription->status = 'cancelled';
            $payment->subscription->save();
        }

        return redirect()->route('payments.pending')
            ->with('success', 'Pembayaran berhasil dibatalkan.');
    }
}
