/* User Pagination Styles */

.user-pagination-container {
    @apply flex justify-center my-4;
}

.user-pagination-wrapper {
    @apply inline-flex items-center justify-center space-x-1 sm:space-x-2 rounded-lg bg-white dark:bg-gray-800 p-1.5 sm:p-2 shadow-sm;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.user-pagination-item {
    @apply relative inline-flex items-center justify-center w-8 h-8 text-sm bg-white text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-full transition-all duration-200;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.user-pagination-item:hover {
    @apply bg-light-accent text-white dark:bg-dark-accent transform scale-105;
}

.user-pagination-current {
    @apply relative inline-flex items-center justify-center w-8 h-8 text-sm font-bold bg-light-accent text-white dark:bg-dark-accent rounded-full shadow-md transform scale-110;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-pagination-disabled {
    @apply relative inline-flex items-center justify-center w-8 h-8 text-sm bg-gray-200 text-gray-500 dark:bg-gray-700 dark:text-gray-400 rounded-full cursor-not-allowed;
}

.user-pagination-ellipsis {
    @apply relative inline-flex items-center justify-center w-8 h-8 text-sm text-gray-500 dark:text-gray-400;
}

/* Animation for hover */
.user-pagination-item:hover {
    animation: user-pagination-pulse 0.3s ease-in-out;
}

@keyframes user-pagination-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1.05);
    }
}
