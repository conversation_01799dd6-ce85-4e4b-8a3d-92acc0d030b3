@if ($paginator->hasPages())
    <nav role="navigation" aria-label="Pagination Navigation" class="fixed-pagination" id="pagination-container">
        <div class="pagination-content">
            <div class="pagination-info hidden sm:block">
                <span class="font-medium"><PERSON>aman {{ $paginator->currentPage() }}</span> dari <span class="font-medium">{{ $paginator->lastPage() }}</span>
                (Total: {{ $paginator->total() }} data)
            </div>
            
            <div class="pagination-links">
                {{-- Previous Page Link --}}
                @if ($paginator->onFirstPage())
                    <span class="pagination-arrow-disabled" aria-disabled="true" aria-label="@lang('pagination.previous')">
                        <i class="fas fa-chevron-left text-xs"></i>
                    </span>
                @else
                    <a href="{{ $paginator->previousPageUrl() }}" class="pagination-arrow" rel="prev" aria-label="@lang('pagination.previous')">
                        <i class="fas fa-chevron-left text-xs"></i>
                    </a>
                @endif

                {{-- First Page Link --}}
                @if($paginator->currentPage() > 3)
                    <a href="{{ $paginator->url(1) }}" class="pagination-link" aria-label="@lang('pagination.goto_page', ['page' => 1])">
                        1
                    </a>
                    
                    @if($paginator->currentPage() > 4)
                        <span class="pagination-ellipsis">...</span>
                    @endif
                @endif

                {{-- Pagination Elements --}}
                @foreach ($elements as $element)
                    {{-- Array Of Links --}}
                    @if (is_array($element))
                        @foreach ($element as $page => $url)
                            {{-- Show pages around current page --}}
                            @if ($page == $paginator->currentPage())
                                <span class="pagination-current" aria-current="page">
                                    {{ $page }}
                                </span>
                            @elseif ($page === $paginator->currentPage() + 1 || $page === $paginator->currentPage() - 1 || $page === $paginator->currentPage() + 2 || $page === $paginator->currentPage() - 2)
                                <a href="{{ $url }}" class="pagination-link" aria-label="@lang('pagination.goto_page', ['page' => $page])">
                                    {{ $page }}
                                </a>
                            @endif
                        @endforeach
                    @endif
                @endforeach

                {{-- Last Page Link --}}
                @if($paginator->currentPage() < $paginator->lastPage() - 2)
                    @if($paginator->currentPage() < $paginator->lastPage() - 3)
                        <span class="pagination-ellipsis">...</span>
                    @endif
                    
                    <a href="{{ $paginator->url($paginator->lastPage()) }}" class="pagination-link" aria-label="@lang('pagination.goto_page', ['page' => $paginator->lastPage()])">
                        {{ $paginator->lastPage() }}
                    </a>
                @endif

                {{-- Next Page Link --}}
                @if ($paginator->hasMorePages())
                    <a href="{{ $paginator->nextPageUrl() }}" class="pagination-arrow" rel="next" aria-label="@lang('pagination.next')">
                        <i class="fas fa-chevron-right text-xs"></i>
                    </a>
                @else
                    <span class="pagination-arrow-disabled" aria-disabled="true" aria-label="@lang('pagination.next')">
                        <i class="fas fa-chevron-right text-xs"></i>
                    </span>
                @endif
            </div>
        </div>
    </nav>
@endif
