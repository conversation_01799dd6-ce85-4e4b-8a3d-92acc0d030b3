<!-- <PERSON><PERSON> Upah AHS -->
<div id="upahahsModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-3xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h2 class="text-white dark:text-dark-text text-lg font-semibold">Tambah Upah</h2>
        <button type="button" onclick="closeUpahAHSModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
    <form id="upahahsForm" method="POST" action="{{ route('upah.store') }}" onsubmit="return handleResourceSubmit(event, 'upah')">
      @csrf
      <input type="hidden" name="_token" value="{{ csrf_token() }}">
      <div class="space-y-4">
        <div>
          <label class="block font-medium">Uraian Tenaga</label>
            <input type="text" name="uraian_tenaga" class="w-full border rounded p-2 dark:bg-dark-card dark:border-gray-600" required>
        </div>
        <div>
          <label class="block font-medium">Satuan</label>
            <input type="text" name="satuan" class="w-full border rounded p-2 dark:bg-dark-card dark:border-gray-600" required>
        </div>
        <div>
          <label class="block font-medium">Harga Upah</label>
            <input type="number" step="0.01" name="harga" class="w-full border rounded p-2 dark:bg-dark-card dark:border-gray-600" required>
        </div>
        <input type="hidden" name="sumber" value="{{ auth()->user()->name ?? 'Customer' }}">
        <input type="hidden" name="alamat" value="{{ auth()->user()->currentProject->district ?? '' }}">
      </div>
      <div class="mt-6 flex justify-end space-x-3">
          <button type="button" onclick="closeUpahAHSModal()" class="bg-gray-500 hover:bg-gray-700 hover:text-blue-100 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150">
          <i class="fas fa-times-circle"></i> Batal
        </button>
          <button type="submit" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150 shadow-sm hover:shadow transform hover:scale-105">
          <i class="fas fa-save"></i> Simpan
        </button>
      </div>
    </form>
    </div>
  </div>
</div>

<!-- Modal Alat AHS -->
<div id="alatahsModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-3xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h2 class="text-white dark:text-dark-text text-lg font-semibold">Tambah Alat</h2>
        <button type="button" onclick="closeAlatAHSModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
    <form id="alatahsForm" method="POST" action="{{ route('alat.store') }}" onsubmit="return handleResourceSubmit(event, 'alat')">
      @csrf
      <input type="hidden" name="_token" value="{{ csrf_token() }}">
      <div class="space-y-4">
        <div>
          <label class="block font-medium">Uraian Alat</label>
            <input type="text" name="uraian_alat" class="w-full border rounded p-2 dark:bg-dark-card dark:border-gray-600" required>
        </div>
        <div>
          <label class="block font-medium">Satuan</label>
            <input type="text" name="satuan" class="w-full border rounded p-2 dark:bg-dark-card dark:border-gray-600" required>
        </div>
        <div>
          <label class="block font-medium">Harga Alat</label>
            <input type="number" step="0.01" name="harga_alat" class="w-full border rounded p-2 dark:bg-dark-card dark:border-gray-600" required>
        </div>
        <input type="hidden" name="sumber" value="{{ auth()->user()->name ?? 'Customer' }}">
        <input type="hidden" name="alamat" value="{{ auth()->user()->currentProject->district ?? '' }}">
      </div>
      <div class="mt-6 flex justify-end space-x-3">
          <button type="button" onclick="closeAlatAHSModal()" class="bg-gray-500 hover:bg-gray-700 hover:text-blue-100 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150">
          <i class="fas fa-times-circle"></i> Batal
        </button>
          <button type="submit" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150 shadow-sm hover:shadow transform hover:scale-105">
          <i class="fas fa-save"></i> Simpan
        </button>
      </div>
    </form>
    </div>
  </div>
</div>

<!-- Modal Bahan AHS -->
<div id="bahanahsModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-3xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h2 class="text-white dark:text-dark-text text-lg font-semibold">Tambah Bahan</h2>
        <button type="button" onclick="closeBahanAHSModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
    <form id="bahanahsForm" method="POST" action="{{ route('bahan.store') }}" onsubmit="return handleResourceSubmit(event, 'bahan')">
      @csrf
      <input type="hidden" name="_token" value="{{ csrf_token() }}">
      <div class="space-y-4">
        <div>
          <label class="block font-medium">Uraian Bahan</label>
            <input type="text" name="uraian_bahan" class="w-full border rounded p-2 dark:bg-dark-card dark:border-gray-600" required>
        </div>
        <div>
          <label class="block font-medium">Satuan</label>
            <input type="text" name="satuan" class="w-full border rounded p-2 dark:bg-dark-card dark:border-gray-600" required>
        </div>
        <div>
          <label class="block font-medium">Harga Bahan</label>
            <input type="number" step="0.01" name="harga_bahan" class="w-full border rounded p-2 dark:bg-dark-card dark:border-gray-600" required>
        </div>
        <input type="hidden" name="sumber" value="{{ auth()->user()->name ?? 'Customer' }}">
        <input type="hidden" name="alamat" value="{{ auth()->user()->currentProject->district ?? '' }}">
      </div>
      <div class="mt-6 flex justify-end space-x-3">
          <button type="button" onclick="closeBahanAHSModal()" class="bg-gray-500 hover:bg-gray-700 hover:text-blue-100 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150">
          <i class="fas fa-times-circle"></i> Batal
        </button>
          <button type="submit" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150 shadow-sm hover:shadow transform hover:scale-105">
          <i class="fas fa-save"></i> Simpan
        </button>
      </div>
    </form>
    </div>
  </div>
</div>