<?php

namespace App\Http\Controllers;

use App\Models\Alat;
use Illuminate\Http\Request;
use App\Http\Controllers\ResourceController;

class AlatController extends Controller
{
    use ResourceController;

    /**
     * Display a listing of the resources.
     */
    public function index(Request $request)
    {
        // Get search query
        $search = $request->input('search');

        // Create query
        $query = Alat::query();

        // Apply search if provided
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('uraian_alat', 'like', "%{$search}%")
                    ->orWhere('satuan', 'like', "%{$search}%")
                    ->orWhere('sumber', 'like', "%{$search}%");
            });
        }

        // Order by newest first
        $query->orderBy('created_at', 'desc');

        // Paginate results
        $alats = $query->paginate(15);

        // Append search to pagination links
        if ($search) {
            $alats->appends(['search' => $search]);
        }

        return view('alat.index', compact('alats'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        return $this->storeResource($request, Alat::class, 'Alat berhasil disimpan!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        return $this->updateResource($request, $id, Alat::class, 'Alat berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        return $this->destroyResource(request(), $id, Alat::class, 'Alat berhasil dihapus!');
    }

    /**
     * Get alat data for AJAX requests
     */
    public function getData()
    {
        return $this->getResourceData(Alat::class);
    }
}
