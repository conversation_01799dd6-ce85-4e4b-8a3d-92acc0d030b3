<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    protected $except = [
        // Exclude payment callback endpoint from CSRF verification
        'payments/callback',

        // Temporarily exclude these endpoints for testing with Postman
        'payment-gateway/*',
        'subscriptions/purchase/*'
    ];
}
