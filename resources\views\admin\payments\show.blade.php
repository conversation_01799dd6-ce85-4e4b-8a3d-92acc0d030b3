@extends('layouts.app')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2">Detail Pembayaran</h1>
                <p class="text-gray-600 dark:text-gray-400">Detail pembayaran untuk invoice {{ $payment->invoice_number }}
                </p>
            </div>
            <div>
                <a href="{{ route('admin.payments.index') }}"
                    class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                    <i class="fas fa-arrow-left mr-1"></i> Kembali
                </a>
            </div>
        </div>

        @if (session('error'))
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400"
                role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        @if (session('success'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded dark:bg-green-900/30 dark:text-green-400"
                role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Detail Pembayaran -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
                    <div class="bg-blue-600 dark:bg-blue-700 p-4">
                        <h2 class="text-white text-lg font-semibold">Detail Pembayaran</h2>
                    </div>

                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-4">Informasi
                                    Pembayaran</h3>

                                <div class="space-y-3">
                                    <div>
                                        <span class="text-gray-600 dark:text-gray-400">No. Invoice:</span>
                                        <span
                                            class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->invoice_number }}</span>
                                    </div>

                                    <div>
                                        <span class="text-gray-600 dark:text-gray-400">Tanggal:</span>
                                        <span
                                            class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->created_at->format('d F Y H:i') }}</span>
                                    </div>

                                    <div>
                                        <span class="text-gray-600 dark:text-gray-400">Jumlah:</span>
                                        <span
                                            class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->formatted_amount }}</span>
                                    </div>

                                    <div>
                                        <span class="text-gray-600 dark:text-gray-400">Metode Pembayaran:</span>
                                        <span class="font-medium text-light-text dark:text-dark-text ml-2">
                                            @if ($payment->payment_method === 'bank_transfer')
                                                Transfer Bank
                                            @elseif($payment->payment_method === 'credit_card')
                                                Kartu Kredit
                                            @elseif($payment->payment_method === 'e-wallet')
                                                E-Wallet
                                            @else
                                                {{ $payment->payment_method }}
                                            @endif
                                        </span>
                                    </div>

                                    <div>
                                        <span class="text-gray-600 dark:text-gray-400">Status:</span>
                                        @if ($payment->isCompleted())
                                            <span
                                                class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900/30 dark:text-green-400 ml-2">Selesai</span>
                                        @elseif($payment->isPending())
                                            <span
                                                class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900/30 dark:text-yellow-400 ml-2">Menunggu</span>
                                        @elseif($payment->hasFailed())
                                            <span
                                                class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-red-900/30 dark:text-red-400 ml-2">Gagal</span>
                                        @elseif($payment->wasRefunded())
                                            <span
                                                class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-purple-900/30 dark:text-purple-400 ml-2">Dikembalikan</span>
                                        @elseif($payment->status === 'processing')
                                            <span
                                                class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900/30 dark:text-blue-400 ml-2">Diproses</span>
                                        @else
                                            <span
                                                class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-gray-300 ml-2">{{ $payment->status }}</span>
                                        @endif
                                    </div>

                                    @if ($payment->paid_at)
                                        <div>
                                            <span class="text-gray-600 dark:text-gray-400">Tanggal Pembayaran:</span>
                                            <span
                                                class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->paid_at->format('d F Y H:i') }}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <div>
                                <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-4">Detail Langganan
                                </h3>

                                @if ($payment->subscription && $payment->subscription->plan)
                                    <div class="space-y-3">
                                        <div>
                                            <span class="text-gray-600 dark:text-gray-400">Paket:</span>
                                            <span
                                                class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->subscription->plan->name }}</span>
                                        </div>

                                        <div>
                                            <span class="text-gray-600 dark:text-gray-400">Durasi:</span>
                                            <span
                                                class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->subscription->plan->duration_in_months }}
                                                bulan</span>
                                        </div>

                                        <div>
                                            <span class="text-gray-600 dark:text-gray-400">Tanggal Mulai:</span>
                                            <span
                                                class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->subscription->start_date->format('d F Y') }}</span>
                                        </div>

                                        <div>
                                            <span class="text-gray-600 dark:text-gray-400">Tanggal Berakhir:</span>
                                            <span
                                                class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->subscription->end_date->format('d F Y') }}</span>
                                        </div>

                                        <div>
                                            <span class="text-gray-600 dark:text-gray-400">Status Langganan:</span>
                                            @if ($payment->subscription->isActive())
                                                <span
                                                    class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900/30 dark:text-green-400 ml-2">Aktif</span>
                                            @elseif($payment->subscription->isOnTrial())
                                                <span
                                                    class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900/30 dark:text-blue-400 ml-2">Masa
                                                    Uji Coba</span>
                                            @elseif($payment->subscription->isCancelled())
                                                <span
                                                    class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900/30 dark:text-yellow-400 ml-2">Dibatalkan</span>
                                            @elseif($payment->subscription->isExpired())
                                                <span
                                                    class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-red-900/30 dark:text-red-400 ml-2">Kadaluarsa</span>
                                            @else
                                                <span
                                                    class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-gray-300 ml-2">{{ $payment->subscription->status }}</span>
                                            @endif
                                        </div>
                                    </div>
                                @else
                                    <p class="text-gray-600 dark:text-gray-400">Tidak ada informasi langganan.</p>
                                @endif
                            </div>
                        </div>

                        @if ($payment->description)
                            <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                                <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-2">Deskripsi</h3>
                                <p class="text-gray-600 dark:text-gray-400">{{ $payment->description }}</p>
                            </div>
                        @endif

                        @if ($payment->payment_proof)
                            <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                                <h3 class="text-lg font-semibold text-light-text dark:text-dark-text mb-2">Bukti Pembayaran
                                </h3>
                                <div class="mt-2">
                                    <img src="{{ asset('storage/' . $payment->payment_proof) }}" alt="Bukti Pembayaran"
                                        class="max-w-md rounded-lg shadow-md">
                                </div>
                            </div>
                        @endif

                        @php
                            $paymentVerificationMode = \App\Models\SystemSetting::getValue(
                                'payment_verification_mode',
                                'manual',
                            );
                        @endphp

                        @if (($payment->status === 'processing') && $paymentVerificationMode === 'manual')
                            <div class="mt-8 flex flex-wrap gap-4">
                                <button type="button"
                                    class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150"
                                    onclick="confirmVerify()">
                                    <i class="fas fa-check mr-1"></i> Verifikasi Pembayaran
                                </button>

                                <button type="button"
                                    class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150"
                                    onclick="confirmReject()">
                                    <i class="fas fa-times mr-1"></i> Tolak Pembayaran
                                </button>

                                <form id="verify-form" action="{{ route('admin.payments.verify', $payment->id) }}"
                                    method="POST" class="hidden">
                                    @csrf
                                </form>

                                <form id="reject-form" action="{{ route('admin.payments.reject', $payment->id) }}"
                                    method="POST" class="hidden">
                                    @csrf
                                </form>
                            </div>
                        @elseif($payment->isPending() && $paymentVerificationMode === 'manual')
                            <div class="mt-8 bg-yellow-100 dark:bg-yellow-900/30 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-yellow-500 mt-0.5"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-300">Pembayaran Belum Diproses</h3>
                                        <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-200">
                                            <p>Pembayaran ini masih dalam status pending. Tombol verifikasi akan muncul setelah pembayaran diproses oleh payment gateway.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @elseif($payment->isPending() && $paymentVerificationMode === 'automatic')
                            <div class="mt-8 bg-blue-100 dark:bg-blue-900/30 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-blue-800 dark:text-blue-300">Verifikasi Otomatis
                                            Aktif</h3>
                                        <div class="mt-2 text-sm text-blue-700 dark:text-blue-200">
                                            <p>Pembayaran ini akan diverifikasi secara otomatis oleh sistem saat callback
                                                dari payment gateway diterima.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Informasi Pelanggan -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
                    <div class="bg-blue-600 dark:bg-blue-700 p-4">
                        <h2 class="text-white text-lg font-semibold">Informasi Pelanggan</h2>
                    </div>

                    <div class="p-6">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700 mr-4">
                                @if ($payment->user->photo)
                                    <img src="{{ asset('storage/' . $payment->user->photo) }}" alt="Profile Photo"
                                        class="w-full h-full object-cover">
                                @else
                                    <img src="@gravatar($payment->user->email, 200, 'identicon')" alt="Profile Photo" class="w-full h-full object-cover">
                                @endif
                            </div>

                            <div>
                                <h3 class="text-lg font-medium text-light-text dark:text-dark-text">
                                    {{ $payment->user->name }}</h3>
                                <p class="text-gray-600 dark:text-gray-400">{{ $payment->user->email }}</p>
                            </div>
                        </div>

                        <div class="space-y-3">
                            @if ($payment->user->phone)
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Telepon:</span>
                                    <span
                                        class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->user->phone }}</span>
                                </div>
                            @endif

                            @if ($payment->user->address)
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Alamat:</span>
                                    <span
                                        class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->user->address }}</span>
                                </div>
                            @endif

                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Tanggal Bergabung:</span>
                                <span
                                    class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->user->created_at->format('d F Y') }}</span>
                            </div>
                        </div>

                        <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-light-text dark:text-dark-text mb-3">Status Langganan</h3>

                            @if ($payment->user->currentSubscription)
                                <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
                                    <p class="text-green-800 dark:text-green-400 font-medium">Langganan Aktif</p>
                                    <p class="text-green-700 dark:text-green-500 text-sm mt-1">
                                        Paket {{ $payment->user->currentSubscription->plan->name }}
                                        <br>
                                        Berakhir pada {{ $payment->user->currentSubscription->end_date->format('d F Y') }}
                                    </p>
                                </div>
                            @elseif($payment->user->isOnTrial())
                                <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                                    <p class="text-blue-800 dark:text-blue-400 font-medium">Masa Uji Coba</p>
                                    <p class="text-blue-700 dark:text-blue-500 text-sm mt-1">
                                        Berakhir pada {{ $payment->user->trial_ends_at->format('d F Y') }}
                                    </p>
                                </div>
                            @else
                                <div class="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg">
                                    <p class="text-yellow-800 dark:text-yellow-400 font-medium">Tidak Ada Langganan Aktif
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Konfirmasi Verifikasi -->
    <div id="verifyModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden overflow-y-auto py-8 text-sm">
        <div class="bg-white dark:bg-dark-card rounded-lg shadow-lg max-w-md w-full mx-auto my-auto border-2 border-light-accent dark:border-dark-accent">
            <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10 rounded-t-lg">
                <div class="flex justify-between items-center">
                    <h3 class="text-white dark:text-dark-text text-lg font-semibold">Konfirmasi Verifikasi</h3>
                    <button type="button" 
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none"
                        onclick="closeVerifyModal()">
                        <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                </div>

            <div class="p-6 overflow-y-auto" style="scrollbar-width: thin;">
                <p class="text-gray-600 dark:text-gray-400 mb-4">Apakah Anda yakin ingin memverifikasi pembayaran ini?
                    Tindakan ini akan mengaktifkan langganan pelanggan.</p>

                <div class="flex justify-end gap-3 border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                    <button type="button"
                        class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150"
                        onclick="closeVerifyModal()">
                        Batal
                    </button>
                    <button type="button" id="confirmVerifyButton"
                        class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                        Verifikasi
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Konfirmasi Tolak -->
    <div id="rejectModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden overflow-y-auto py-8 text-sm">
        <div class="bg-white dark:bg-dark-card rounded-lg shadow-lg max-w-md w-full mx-auto my-auto border-2 border-light-accent dark:border-dark-accent">
            <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10 rounded-t-lg">
                <div class="flex justify-between items-center">
                    <h3 class="text-white dark:text-dark-text text-lg font-semibold">Konfirmasi Penolakan</h3>
                    <button type="button"
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none"
                        onclick="closeRejectModal()">
                        <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                </div>

            <div class="p-6 overflow-y-auto" style="scrollbar-width: thin;">
                <p class="text-gray-600 dark:text-gray-400 mb-4">Apakah Anda yakin ingin menolak pembayaran ini? Tindakan
                    ini akan menandai pembayaran sebagai gagal.</p>

                <div class="flex justify-end gap-3 border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                    <button type="button"
                        class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150"
                        onclick="closeRejectModal()">
                        Batal
                    </button>
                    <button type="button" id="confirmRejectButton"
                        class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                        Tolak
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function confirmVerify() {
            document.getElementById('verifyModal').classList.remove('hidden');
            document.getElementById('confirmVerifyButton').addEventListener('click', verifyPayment);
        }

        function closeVerifyModal() {
            document.getElementById('verifyModal').classList.add('hidden');
            document.getElementById('confirmVerifyButton').removeEventListener('click', verifyPayment);
        }

        function verifyPayment() {
            document.getElementById('verify-form').submit();
        }

        function confirmReject() {
            document.getElementById('rejectModal').classList.remove('hidden');
            document.getElementById('confirmRejectButton').addEventListener('click', rejectPayment);
        }

        function closeRejectModal() {
            document.getElementById('rejectModal').classList.add('hidden');
            document.getElementById('confirmRejectButton').removeEventListener('click', rejectPayment);
        }

        function rejectPayment() {
            document.getElementById('reject-form').submit();
        }
    </script>
@endsection
