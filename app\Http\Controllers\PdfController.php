<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Project;
use App\Models\KategoriPekerjaan;
use App\Models\ItemPekerjaan;
use App\Models\Ahs;
use App\Models\AhspDetail;
use App\Models\Upah;
use App\Models\Bahan;
use App\Models\Alat;
use App\Models\TimeSchedule;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PdfController extends Controller
{
    /**
     * Convert number to words in Indonesian
     */
    private function terbilang($number)
    {
        $number = abs($number);
        $words = array(
            '',
            'satu',
            'dua',
            'tiga',
            'empat',
            'lima',
            'enam',
            'tujuh',
            'delapan',
            'sembilan',
            'sepuluh',
            'sebelas'
        );

        if ($number < 12) {
            return $words[$number];
        } elseif ($number < 20) {
            return $this->terbilang($number - 10) . ' belas';
        } elseif ($number < 100) {
            return $this->terbilang(floor($number / 10)) . ' puluh ' . $this->terbilang($number % 10);
        } elseif ($number < 200) {
            return 'seratus ' . $this->terbilang($number - 100);
        } elseif ($number < 1000) {
            return $this->terbilang(floor($number / 100)) . ' ratus ' . $this->terbilang($number % 100);
        } elseif ($number < 2000) {
            return 'seribu ' . $this->terbilang($number - 1000);
        } elseif ($number < 1000000) {
            return $this->terbilang(floor($number / 1000)) . ' ribu ' . $this->terbilang($number % 1000);
        } elseif ($number < 1000000000) {
            return $this->terbilang(floor($number / 1000000)) . ' juta ' . $this->terbilang($number % 1000000);
        } elseif ($number < 1000000000000) {
            return $this->terbilang(floor($number / 1000000000)) . ' milyar ' . $this->terbilang($number % 1000000000);
        } elseif ($number < 1000000000000000) {
            return $this->terbilang(floor($number / 1000000000000)) . ' trilyun ' . $this->terbilang($number % 1000000000000);
        } else {
            return 'Angka terlalu besar';
        }
    }
    /**
     * Generate PDF for RAB and related data
     */
    public function downloadRabPdf(Request $request)
    {
        // Add debugging
        Log::info('Starting PDF download');

        // Get current project ID from session
        $projectId = session('project_id');
        Log::info('Project ID: ' . $projectId);

        if (!$projectId) {
            return redirect()->back()->with('error', 'Tidak ada proyek yang sedang dibuka');
        }

        // Get project data
        $project = Project::findOrFail($projectId);

        // Check if user has access to this project
        if ($project->user_id !== Auth::id() && Auth::user()->role !== 'admin') {
            return redirect()->back()->with('error', 'Anda tidak memiliki akses ke proyek ini');
        }

        // Get export options
        $exportOptions = $request->has('export') ? explode(',', $request->export) : ['rekap', 'rab', 'ahsp', 'upah', 'bahan', 'alat', 'time-schedule'];
        Log::info('Export options: ' . json_encode($exportOptions));

        // Get RAB data
        $kategoriPekerjaans = KategoriPekerjaan::where('project_id', $projectId)
            ->orderBy('id', 'asc')
            ->with(['items' => function ($query) {
                $query->orderBy('id', 'asc');
            }])
            ->get();

        // Log project data
        Log::info('Project data: ' . json_encode($project));

        // Get all kategori_pekerjaan_ids for this project
        $kategoriIds = KategoriPekerjaan::where('project_id', $projectId)->pluck('id')->toArray();

        // If no kategori pekerjaan found, return with error
        if (empty($kategoriIds)) {
            Log::warning('No kategori pekerjaan found for project: ' . $projectId);
            return redirect()->back()->with('error', 'Tidak ada kategori pekerjaan yang ditemukan untuk proyek ini');
        }

        // Get all AHS IDs used in this project
        $ahsIds = ItemPekerjaan::whereIn('kategori_pekerjaan_id', $kategoriIds)
            ->whereNotNull('ahs_id')
            ->pluck('ahs_id')
            ->unique()
            ->toArray();

        // If no AHS IDs found, return with error
        if (empty($ahsIds)) {
            Log::warning('No AHS IDs found for project: ' . $projectId);
            return redirect()->back()->with('error', 'Tidak ada data AHS yang ditemukan untuk proyek ini');
        }

        // Get all AHS data
        $ahsRecords = Ahs::whereIn('id', $ahsIds)->get();

        // Get all AHS details
        $ahsDetails = AhspDetail::whereIn('ahs_id', $ahsIds)->get();

        // Get all resource IDs used in AHS details
        $upahIds = $ahsDetails->filter(function ($detail) {
            return strtolower($detail->kategori) === 'upah';
        })->pluck('item_id')->unique()->toArray();

        $bahanIds = $ahsDetails->filter(function ($detail) {
            return strtolower($detail->kategori) === 'bahan';
        })->pluck('item_id')->unique()->toArray();

        $alatIds = $ahsDetails->filter(function ($detail) {
            return strtolower($detail->kategori) === 'alat';
        })->pluck('item_id')->unique()->toArray();

        // Get all resources data
        $upahItems = Upah::whereIn('id', $upahIds)->get();
        $bahanItems = Bahan::whereIn('id', $bahanIds)->get();
        $alatItems = Alat::whereIn('id', $alatIds)->get();

        // Calculate totals
        $jumlahHarga = 0;
        foreach ($kategoriPekerjaans as $kategori) {
            foreach ($kategori->items as $item) {
                $jumlahHarga += $item->harga_total;
            }
        }

        $ppn = $project->ppn;
        $ppnHarga = ($jumlahHarga * $ppn) / 100;
        $totalHarga = $jumlahHarga + $ppnHarga;
        $dibulatkanHarga = floor($totalHarga / 1000) * 1000;

        // Log before generating PDF
        Log::info('About to generate PDF with data');
        Log::info('AHS IDs: ' . json_encode($ahsIds));
        Log::info('AHS Records count: ' . $ahsRecords->count());
        Log::info('AHS Details count: ' . $ahsDetails->count());

        // Log kategori values for debugging
        $kategoriValues = $ahsDetails->pluck('kategori')->unique()->toArray();
        Log::info('Kategori values: ' . json_encode($kategoriValues));

        Log::info('Upah IDs: ' . json_encode($upahIds));
        Log::info('Bahan IDs: ' . json_encode($bahanIds));
        Log::info('Alat IDs: ' . json_encode($alatIds));

        Log::info('Upah Items count: ' . $upahItems->count());
        Log::info('Bahan Items count: ' . $bahanItems->count());
        Log::info('Alat Items count: ' . $alatItems->count());

        try {
            // Generate terbilang
            $terbilangTotal = ucwords($this->terbilang(floor($dibulatkanHarga))) . ' Rupiah';

            // Get time schedule data
            $timeSchedules = TimeSchedule::where('project_id', $projectId)
                ->orderBy('tanggal_mulai')
                ->get();

            // Generate PDF
            $pdf = PDF::loadView('pdf.rab', [
                'project' => $project,
                'kategoriPekerjaans' => $kategoriPekerjaans,
                'ahsRecords' => in_array('ahsp', $exportOptions) ? $ahsRecords : collect(),
                'ahsDetails' => in_array('ahsp', $exportOptions) ? $ahsDetails : collect(),
                'upahItems' => in_array('upah', $exportOptions) ? $upahItems : collect(),
                'bahanItems' => in_array('bahan', $exportOptions) ? $bahanItems : collect(),
                'alatItems' => in_array('alat', $exportOptions) ? $alatItems : collect(),
                'jumlahHarga' => $jumlahHarga,
                'ppn' => $ppn,
                'ppnHarga' => $ppnHarga,
                'totalHarga' => $totalHarga,
                'dibulatkanHarga' => $dibulatkanHarga,
                'terbilangTotal' => $terbilangTotal,
                'timeSchedules' => in_array('time-schedule', $exportOptions) ? $timeSchedules : collect(),
                'exportOptions' => $exportOptions
            ]);

            // Set paper to A4 landscape for better table display
            $pdf->setPaper('a4', 'landscape');

            // Return PDF for download
            Log::info('Returning PDF for download');
            return $pdf->download('RAB_' . $project->name . '.pdf');
        } catch (\Exception $e) {
            // Log error
            Log::error('Error generating PDF: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            // Return error response
            return redirect()->back()->with('error', 'Terjadi kesalahan saat menghasilkan PDF: ' . $e->getMessage());
        }
    }

    /**
     * Show preview modal for RAB PDF
     */
    public function previewRabPdf(Request $request)
    {
        // Get current project ID from session
        $projectId = session('project_id');

        if (!$projectId) {
            return redirect()->back()->with('error', 'Tidak ada proyek yang sedang dibuka');
        }

        // Get project data
        $project = Project::findOrFail($projectId);

        // Check if user has access to this project
        if ($project->user_id !== Auth::id() && Auth::user()->role !== 'admin') {
            return redirect()->back()->with('error', 'Anda tidak memiliki akses ke proyek ini');
        }

        // Get RAB data
        $kategoriPekerjaans = KategoriPekerjaan::where('project_id', $projectId)
            ->orderBy('id', 'asc')
            ->with(['items' => function ($query) {
                $query->orderBy('id', 'asc');
            }])
            ->get();

        // Get all items needed for the view
        $bahanItems = Bahan::all();
        $upahItems = Upah::all();
        $alatItems = Alat::all();

        // Get AHSP data
        $ahsIds = [];
        foreach ($kategoriPekerjaans as $kategori) {
            foreach ($kategori->items as $item) {
                if ($item->ahs_id) {
                    $ahsIds[] = $item->ahs_id;
                }
            }
        }
        $ahsRecords = Ahs::whereIn('id', $ahsIds)->get();
        $ahsDetails = AhspDetail::whereIn('ahs_id', $ahsIds)->get();

        // Calculate totals
        $jumlahHarga = 0;
        foreach ($kategoriPekerjaans as $kategori) {
            foreach ($kategori->items as $item) {
                $jumlahHarga += $item->harga_total;
            }
        }

        $ppn = $project->ppn;
        $ppnHarga = ($jumlahHarga * $ppn) / 100;
        $totalHarga = $jumlahHarga + $ppnHarga;
        $dibulatkanHarga = floor($totalHarga / 1000) * 1000;
        $terbilangTotal = ucwords($this->terbilang(floor($dibulatkanHarga))) . ' Rupiah';

        // Get time schedule data
        $timeSchedules = TimeSchedule::where('project_id', $projectId)
            ->orderBy('tanggal_mulai')
            ->get();

        // Return view with modal
        return view('rab.index', [
            'showPreviewDownload' => true,
            'project' => $project,
            'kategoriPekerjaans' => $kategoriPekerjaans,
            'bahanItems' => $bahanItems,
            'upahItems' => $upahItems,
            'alatItems' => $alatItems,
            'ahsRecords' => $ahsRecords,
            'ahsDetails' => $ahsDetails,
            'jumlahHarga' => $jumlahHarga,
            'ppn' => $ppn,
            'ppnHarga' => $ppnHarga,
            'totalHarga' => $totalHarga,
            'dibulatkanHarga' => $dibulatkanHarga,
            'terbilangTotal' => $terbilangTotal,
            'timeSchedules' => $timeSchedules
        ]);
    }
}
