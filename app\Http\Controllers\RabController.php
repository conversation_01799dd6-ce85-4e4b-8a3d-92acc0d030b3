<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Bahan;
use App\Models\Upah;
use App\Models\Alat;
use App\Models\KategoriPekerjaan;
use App\Models\ItemPekerjaan;
use Illuminate\Support\Facades\Log;
use App\Models\Project;
use App\Models\Rab;
use App\Models\Ahs;
use App\Models\AhspDetail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class RabController extends Controller
{
    /**
     * Convert number to words in Indonesian
     */
    private function terbilang($number)
    {
        $number = abs($number);
        $words = array(
            '',
            'satu',
            'dua',
            'tiga',
            'empat',
            'lima',
            'enam',
            'tujuh',
            'delapan',
            'sembilan',
            'sepuluh',
            'sebelas'
        );

        if ($number < 12) {
            return $words[$number];
        } elseif ($number < 20) {
            return $this->terbilang($number - 10) . ' belas';
        } elseif ($number < 100) {
            return $this->terbilang(floor($number / 10)) . ' puluh ' . $this->terbilang($number % 10);
        } elseif ($number < 200) {
            return 'seratus ' . $this->terbilang($number - 100);
        } elseif ($number < 1000) {
            return $this->terbilang(floor($number / 100)) . ' ratus ' . $this->terbilang($number % 100);
        } elseif ($number < 2000) {
            return 'seribu ' . $this->terbilang($number - 1000);
        } elseif ($number < 1000000) {
            return $this->terbilang(floor($number / 1000)) . ' ribu ' . $this->terbilang($number % 1000);
        } elseif ($number < 1000000000) {
            return $this->terbilang(floor($number / 1000000)) . ' juta ' . $this->terbilang($number % 1000000);
        } elseif ($number < 1000000000000) {
            return $this->terbilang(floor($number / 1000000000)) . ' milyar ' . $this->terbilang($number % 1000000000);
        } elseif ($number < 1000000000000000) {
            return $this->terbilang(floor($number / 1000000000000)) . ' trilyun ' . $this->terbilang($number % 1000000000000);
        } else {
            return 'Angka terlalu besar';
        }
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->isMethod('post')) {
            $request->validate([
                'project_id' => 'required|exists:projects,id',
            ]);
            session(['project_id' => $request->project_id]);
        }

        $projectId = session('project_id');
        if (!$projectId) {
            return redirect()->route('proyek')->with('error', 'Pilih proyek terlebih dahulu.');
        }

        Log::info('Project ID:', ['project_id' => $projectId]);

        $project = Project::findOrFail($projectId);

        // Check if user has access to this project
        $user = Auth::user();
        if ($project->user_id !== $user->id) {
            // Check if project is shared with this user
            $projectShare = $project->shares()->where('user_id', $user->id)->first();

            if (!$projectShare) {
                return redirect()->route('proyek')->with('error', 'Anda tidak memiliki akses ke proyek ini.');
            }

            // Set view-only mode if user is a viewer
            $viewOnly = ($projectShare->role === 'viewer');
            session(['view_only' => $viewOnly]);
        } else {
            // Owner has full access
            session(['view_only' => false]);
        }
        $bahanItems = Bahan::all();
        $upahItems = Upah::all();
        $alatItems = Alat::all();
        $kategoriPekerjaans = KategoriPekerjaan::with('items')
            ->where('project_id', $projectId)
            ->get();

        // Check if we need to show the preview download modal
        $showPreviewDownload = $request->has('showPreviewDownload');

        // If we need to show the preview download modal, get additional data
        if ($showPreviewDownload) {
            // Get AHSP data
            $ahsIds = [];
            foreach ($kategoriPekerjaans as $kategori) {
                foreach ($kategori->items as $item) {
                    if ($item->ahs_id) {
                        $ahsIds[] = $item->ahs_id;
                    }
                }
            }
            $ahsRecords = \App\Models\Ahs::whereIn('id', $ahsIds)->get();
            $ahsDetails = \App\Models\AhspDetail::whereIn('ahs_id', $ahsIds)->get();

            // Calculate totals
            $jumlahHarga = 0;
            foreach ($kategoriPekerjaans as $kategori) {
                foreach ($kategori->items as $item) {
                    $jumlahHarga += $item->harga_total;
                }
            }

            $ppn = $project->ppn;
            $ppnHarga = ($jumlahHarga * $ppn) / 100;
            $totalHarga = $jumlahHarga + $ppnHarga;
            $dibulatkanHarga = floor($totalHarga / 1000) * 1000;
            $terbilangTotal = ucwords($this->terbilang(floor($dibulatkanHarga))) . ' Rupiah';

            return view('rab.index', compact(
                'project',
                'bahanItems',
                'upahItems',
                'alatItems',
                'kategoriPekerjaans',
                'showPreviewDownload',
                'ahsRecords',
                'ahsDetails',
                'jumlahHarga',
                'ppn',
                'ppnHarga',
                'totalHarga',
                'dibulatkanHarga',
                'terbilangTotal'
            ));
        }

        return view('rab.index', compact('project', 'bahanItems', 'upahItems', 'alatItems', 'kategoriPekerjaans'));
    }

    public function destroy($id)
    {
        // Cari item berdasarkan ID
        $item = ItemPekerjaan::findOrFail($id);

        // Hapus volume_calculations yang terkait dengan item ini
        $item->volumeCalculations()->delete();

        // Hapus item pekerjaan
        $item->delete();

        return redirect()->back()->with('success', 'Item dan volume terkait berhasil dihapus.');
    }

    /**
     * Update totals for the RAB.
     */
    public function updateTotals(Request $request)
    {
        $request->validate([
            'jumlah_harga' => 'required|numeric',
            'total_harga' => 'required|numeric',
            'dibulatkan' => 'required|numeric'
        ]);

        $projectId = session('project_id');
        if (!$projectId) {
            return response()->json(['success' => false, 'message' => 'Project ID not found in session.'], 400);
        }

        try {
            $project = Project::findOrFail($projectId);

            DB::beginTransaction();

            // Create or update RAB record
            $rab = Rab::updateOrCreate(
                ['project_id' => $projectId],
                [
                    'judul' => $project->name,
                    'jumlah_harga' => $request->jumlah_harga,
                    'total_harga' => $request->total_harga,
                    'dibulatkan' => $request->dibulatkan
                ]
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Totals updated successfully.',
                'data' => [
                    'jumlah_harga' => number_format($rab->jumlah_harga, 2, ',', '.'),
                    'total_harga' => number_format($rab->total_harga, 2, ',', '.'),
                    'dibulatkan' => number_format($rab->dibulatkan, 2, ',', '.')
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Error updating totals: ' . $e->getMessage()], 500);
        }
    }
}
