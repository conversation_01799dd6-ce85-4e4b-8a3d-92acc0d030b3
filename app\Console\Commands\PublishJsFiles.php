<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class PublishJsFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'publish:js';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Publish JavaScript files from resources to public directory';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Ensure the public/js directory exists
        if (!File::exists(public_path('js'))) {
            File::makeDirectory(public_path('js'), 0755, true);
        }

        // We no longer copy modal.js since we're using it directly from resources
        // through Vite

        // Copy detail-modal.js
        if (File::exists(resource_path('js/detail-modal.js'))) {
            File::copy(resource_path('js/detail-modal.js'), public_path('js/detail-modal.js'));
            $this->info('Published: detail-modal.js');
        } else {
            $this->error('File not found: resources/js/detail-modal.js');
        }

        $this->info('JavaScript files published successfully!');
    }
}
