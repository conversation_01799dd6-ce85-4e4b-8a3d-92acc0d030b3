<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ahs_upah', function (Blueprint $table) {
            $table->unsignedBigInteger('ahs_id');
            $table->unsignedBigInteger('upah_id');
            $table->decimal('koefisien', 10, 4)->default(0);
            $table->timestamps();

            $table->primary(['ahs_id', 'upah_id']);
            $table->foreign('ahs_id')->references('id')->on('ahs')->onDelete('cascade');
            $table->foreign('upah_id')->references('id')->on('upahs')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ahs_upah');
    }
};
