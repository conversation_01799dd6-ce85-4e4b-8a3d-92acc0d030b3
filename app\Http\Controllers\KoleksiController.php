<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Upah;
use App\Models\Bahan;
use App\Models\Alat;
use App\Models\Ahs;

class KoleksiController extends Controller
{
    public function index()
    {
        return view('visitor.koleksi.index');
    }

    public function upah(Request $request)
    {
        // Get search query
        $search = $request->input('search');

        // Create query
        $query = Upah::query();

        // Apply search if provided
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('uraian_tenaga', 'like', "%{$search}%")
                    ->orWhere('satuan', 'like', "%{$search}%")
                    ->orWhere('sumber', 'like', "%{$search}%");
            });
        }

        // Paginate results
        $upahItems = $query->orderBy('uraian_tenaga')->paginate(10);

        return view('visitor.koleksi.upah', compact('upahItems', 'search'));
    }

    public function upahDetail($id)
    {
        $upah = Upah::findOrFail($id);
        return view('visitor.koleksi.upah-detail', compact('upah'));
    }

    public function bahan(Request $request)
    {
        // Get search query
        $search = $request->input('search');

        // Create query
        $query = Bahan::query();

        // Apply search if provided
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('uraian_bahan', 'like', "%{$search}%")
                    ->orWhere('satuan', 'like', "%{$search}%")
                    ->orWhere('sumber', 'like', "%{$search}%");
            });
        }

        // Paginate results
        $bahanItems = $query->orderBy('uraian_bahan')->paginate(10);

        return view('visitor.koleksi.bahan', compact('bahanItems', 'search'));
    }

    public function bahanDetail($id)
    {
        $bahan = Bahan::findOrFail($id);
        return view('visitor.koleksi.bahan-detail', compact('bahan'));
    }

    public function alat(Request $request)
    {
        // Get search query
        $search = $request->input('search');

        // Create query
        $query = Alat::query();

        // Apply search if provided
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('uraian_alat', 'like', "%{$search}%")
                    ->orWhere('satuan', 'like', "%{$search}%")
                    ->orWhere('sumber', 'like', "%{$search}%");
            });
        }

        // Paginate results
        $alatItems = $query->orderBy('uraian_alat')->paginate(10);

        return view('visitor.koleksi.alat', compact('alatItems', 'search'));
    }

    public function alatDetail($id)
    {
        $alat = Alat::findOrFail($id);
        return view('visitor.koleksi.alat-detail', compact('alat'));
    }

    public function ahsp(Request $request)
    {
        // Get search query
        $search = $request->input('search');

        // Create query for AHSP items created by admin only
        $query = Ahs::query();

        // Filter to show only AHSP created by admin
        $query->where(function ($q) {
            $q->where('creator_role', 'admin')
                ->orWhereNull('creator_role'); // Include items without creator_role (legacy data)
        });

        // Apply search if provided
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('kode', 'like', "%{$search}%")
                    ->orWhere('judul', 'like', "%{$search}%")
                    ->orWhere('satuan', 'like', "%{$search}%")
                    ->orWhere('sumber', 'like', "%{$search}%");
            });
        }

        // Paginate results
        $ahspItems = $query->orderBy('kode')->paginate(10);

        return view('visitor.koleksi.ahsp', compact('ahspItems', 'search'));
    }

    public function ahspDetail($id)
    {
        $ahsp = Ahs::with('details')->findOrFail($id);

        $bahan = [];
        $upah = [];
        $alat = [];

        foreach ($ahsp->details as $detail) {
            $kategori = strtolower($detail->kategori);
            if ($kategori === 'bahan') {
                if (is_null($detail->item_text) || is_null($detail->satuan) || is_null($detail->harga_dasar)) {
                    $item = \App\Models\Bahan::find($detail->item_id);
                    if ($item) {
                        $detail->item_text = $item->uraian_bahan;
                        $detail->satuan = $item->satuan;
                        $detail->harga_dasar = $item->harga_bahan;
                    }
                }
                $bahan[] = $detail;
            } elseif ($kategori === 'upah') {
                if (is_null($detail->item_text) || is_null($detail->satuan) || is_null($detail->harga_dasar)) {
                    $item = \App\Models\Upah::find($detail->item_id);
                    if ($item) {
                        $detail->item_text = $item->uraian_tenaga;
                        $detail->satuan = $item->satuan;
                        $detail->harga_dasar = $item->harga;
                    }
                }
                $upah[] = $detail;
            } elseif ($kategori === 'alat') {
                if (is_null($detail->item_text) || is_null($detail->satuan) || is_null($detail->harga_dasar)) {
                    $item = \App\Models\Alat::find($detail->item_id);
                    if ($item) {
                        $detail->item_text = $item->uraian_alat;
                        $detail->satuan = $item->satuan;
                        $detail->harga_dasar = $item->harga_alat;
                    }
                }
                $alat[] = $detail;
            }
        }

        return view('visitor.koleksi.ahsp-detail', compact('ahsp', 'bahan', 'upah', 'alat'));
    }
}
