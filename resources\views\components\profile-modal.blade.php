<!-- Modal Profil -->
<div id="profileModal" data-modal-backdrop="static" data-modal-keyboard="false"
    class="fixed inset-0 flex items-center justify-center hidden z-50 overflow-y-auto py-8 text-sm">
    <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-md relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
        <!-- Header -->
        <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
            <div class="flex justify-between items-center">
                <h2 class="text-white dark:text-dark-text text-lg font-semibold">Profil <PERSON></h2>
        <button type="button" onclick="window.profileModal.hide()"
                    class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                    <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
            </div>
        </div>

        <!-- Content -->
        <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
            <!-- Modal body -->
            <form id="profile-form" enctype="multipart/form-data" class="space-y-4">
                @csrf

                <!-- Foto Profil -->
                <div class="mb-4 text-center">
                    <div class="mb-2 flex justify-center">
                        <div class="relative w-24 h-24 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600">
                            <img id="profile-photo-preview" class="w-full h-full object-cover"
                                src="https://www.gravatar.com/avatar/{{ strtolower(md5(strtolower(trim(Auth::user()->email)))) }}?d=identicon&s=200"
                                alt="Profile Photo">
                            <svg id="profile-default-icon"
                                class="w-12 h-12 text-gray-400 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 hidden"
                                fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                    </div>
                    <div class="flex justify-center gap-3">
                        <label for="profile-photo"
                            class="cursor-pointer text-sm font-medium text-blue-600 dark:text-blue-400 hover:underline transition-all duration-200">
                            Ubah Foto
                        </label>
                        <span class="text-gray-300 dark:text-gray-600">|</span>
                        <button type="button" id="reset-photo-button" onclick="resetProfilePhoto()"
                            class="text-sm font-medium text-red-600 dark:text-red-400 hover:underline transition-all duration-200">
                            Reset Foto
                        </button>
                    </div>
                    <input type="file" id="profile-photo" name="photo" class="hidden" accept="image/*"
                        onchange="previewProfilePhoto(this)">
                </div>

                <!-- Nama -->
                <div class="mb-4">
                    <label for="profile-name"
                        class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Nama</label>
                    <input type="text" id="profile-name" name="name"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                        required>
                </div>

                <!-- Email -->
                <div class="mb-4">
                    <label for="profile-email"
                        class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                    <input type="email" id="profile-email" name="email"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                        required>
                </div>

                <!-- Password -->
                <div class="mb-4">
                    <label for="profile-password"
                        class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Password</label>
                    <input type="password" id="profile-password" name="password"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                        placeholder="Kosongkan jika tidak ingin mengubah">
                </div>

                <!-- Alamat -->
                <div class="mb-4">
                    <label for="profile-address"
                        class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Alamat</label>
                    <input type="text" id="profile-address" name="address"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                </div>

                <!-- No. Telepon -->
                <div class="mb-4">
                    <label for="profile-phone" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">No.
                        Telepon</label>
                    <div class="flex">
                        <div
                            class="flex-shrink-0 z-10 inline-flex items-center py-2.5 px-4 text-sm font-medium text-center text-gray-500 bg-gray-100 border border-gray-300 rounded-l-lg dark:bg-gray-600 dark:text-gray-400 dark:border-gray-600">
                            +62
                        </div>
                        <input type="text" id="profile-phone" name="phone"
                            class="bg-gray-50 border border-gray-300 border-l-0 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                            placeholder="8xxxxxxxxxx" pattern="8[0-9]{8,11}"
                            title="Masukkan nomor telepon Indonesia yang valid (tanpa +62 di awal)">
                    </div>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Format: +62 8xxxxxxxxxx (tanpa spasi)</p>
                </div>

                <!-- Modal footer -->
                <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4 flex justify-end space-x-3">
                    <button onclick="window.profileModal.hide()" type="button"
                        class="bg-gray-500 hover:bg-gray-700 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-colors duration-150">
                        <i class="fas fa-times-circle mr-1"></i> Batal
                    </button>
                    <x-loading-button type="button" id="profile-save-button" onclick="saveProfile()"
                        class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105"
                        color="light-accent">
                        <i class="fas fa-save mr-1"></i> Simpan
                    </x-loading-button>
                </div>
            </form>
        </div>
    </div>
</div>
