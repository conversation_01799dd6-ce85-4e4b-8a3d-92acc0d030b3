<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_id')->constrained()->onDelete('cascade');
            $table->string('payment_id')->unique(); // ID eksternal dari payment gateway
            $table->string('payment_method'); // xendit, mayar, bank_transfer, dll
            $table->decimal('amount', 12, 2);
            $table->string('currency')->default('IDR');
            $table->string('status'); // pending, completed, failed, refunded, processing
            $table->timestamp('paid_at')->nullable();
            $table->json('payment_details')->nullable(); // Menyimpan detail dari payment gateway
            $table->string('invoice_number')->unique();
            $table->text('description')->nullable();
            $table->string('payment_proof')->nullable(); // Untuk upload bukti pembayaran manual
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
