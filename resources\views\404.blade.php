@extends('layouts.error')

@section('content')
<div class="min-h-screen flex items-center justify-center relative overflow-hidden">
    <div class="text-center relative z-10">
        <!-- 404 Number with 3D Effect -->
        <div class="text-9xl md:text-[12rem] font-bold mb-8">
            <span class="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent [text-shadow:4px_4px_0_#bfdbfe,8px_8px_0_#93c5fd]">
                404
            </span>
        </div>

        <!-- Construction Icons -->
        <div class="relative mb-8">
            <div class="absolute -top-16 left-1/2 -translate-x-1/2">
                <!-- Wrench Icon -->
                <svg class="w-24 h-24 text-amber-600 animate-wrench mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z"/>
                </svg>

                <!-- Gears -->
                <svg class="w-16 h-16 text-blue-500 animate-spin-slow absolute -top-8 -left-8" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0L8 4l2 2-4 4-2-2-4 4 4 4 2-2 4 4-4 4 4 4 4-4-2-2 4-4 2 2 4-4-4-4-2 2-4-4zm0 8l-4 4 2 2-4 4-2-2-4 4 4 4 2-2 4 4-4 4 4 4 4-4-2-2 4-4 2 2 4-4-4-4-2 2-4-4z"/>
                </svg>

                <svg class="w-12 h-12 text-emerald-500 animate-spin-reverse absolute -top-4 -right-8" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0L8 4l2 2-4 4-2-2-4 4 4 4 2-2 4 4-4 4 4 4 4-4-2-2 4-4 2 2 4-4-4-4-2 2-4-4zm0 8l-4 4 2 2-4 4-2-2-4 4 4 4 2-2 4 4-4 4 4 4 4-4-2-2 4-4 2 2 4-4-4-4-2 2-4-4z"/>
                </svg>
            </div>
        </div>

        <!-- Text Content -->
        <h2 class="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-700 to-emerald-700 bg-clip-text text-transparent mb-4">
            Halaman Sedang Dalam Perbaikan
        </h2>
        <p class="text-gray-600 text-lg mb-8 max-w-2xl mx-auto">
            Mohon maaf, halaman yang Anda tuju sedang dalam proses pengembangan. Silakan kembali ke halaman utama dan coba lagi nanti.
        </p>

        <!-- Home Button -->
        <a href="{{ route('proyek') }}" class="inline-flex items-center px-8 py-3 border-2 border-blue-600 text-blue-600 text-lg font-semibold rounded-xl hover:bg-blue-50 hover:border-blue-700 hover:text-blue-700 transition-all duration-300 shadow-sm hover:shadow-md">
            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
            </svg>
            Kembali ke Proyek
        </a>
    </div>
</div>

<style>
    /* Custom Animations */
    @keyframes wrench {
        0% { transform: rotate(-15deg); }
        50% { transform: rotate(15deg); }
        100% { transform: rotate(-15deg); }
    }

    .animate-wrench {
        animation: wrench 2s ease-in-out infinite;
    }

    .animate-spin-slow {
        animation: spin 8s linear infinite;
    }

    .animate-spin-reverse {
        animation: spin 10s linear infinite reverse;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>
@endsection