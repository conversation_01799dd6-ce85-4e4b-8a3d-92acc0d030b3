<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

trait ResourceController
{
    /**
     * Store a newly created resource via AJAX
     * 
     * @param Request $request
     * @return JsonResponse
     */
    protected function storeResource(Request $request, $modelClass, $successMessage = 'Data berhasil disimpan!')
    {
        // Set the Accept header to ensure JSON response
        if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            $request->headers->set('Accept', 'application/json');
        }

        try {
            Log::info('Storing resource', ['model' => $modelClass, 'data' => $request->all()]);

            $data = $request->all();

            // Check if this is a request to always create a new record
            $createNew = isset($data['create_new']) && $data['create_new'] === true;

            // Remove create_new flag from data before saving
            if (isset($data['create_new'])) {
                unset($data['create_new']);
            }

            // Add current user as the source if not provided
            if (!isset($data['sumber']) || empty($data['sumber'])) {
                $data['sumber'] = Auth::check() ? Auth::user()->name : 'Customer';
            }

            // Add project district as alamat if not provided
            if (!isset($data['alamat']) || empty($data['alamat'])) {
                if (Auth::check() && Auth::user()->currentProject) {
                    $data['alamat'] = Auth::user()->currentProject->district ?? '';
                } else {
                    $data['alamat'] = '';
                }
            }

            // Always create a new record
            // Ignore any provided ID in the request to avoid updating existing records
            if (isset($data['id'])) {
                unset($data['id']);
            }

            $resource = $modelClass::create($data);
            Log::info('Resource created successfully', ['resource' => $resource]);

            // If it's an AJAX request, return JSON response
            if ($request->wantsJson() || $request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
                return response()->json([
                    'success' => true,
                    'message' => $successMessage,
                    'resource' => $resource
                ]);
            }

            // Otherwise redirect with success message
            return redirect()->back()->with('success', $successMessage);
        } catch (\Exception $e) {
            Log::error('Error storing resource', [
                'model' => $modelClass,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->wantsJson() || $request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
                return response()->json([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified resource via AJAX
     * 
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    protected function updateResource(Request $request, $id, $modelClass, $successMessage = 'Data berhasil diperbarui!')
    {
        try {
            $resource = $modelClass::findOrFail($id);
            $resource->update($request->all());

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $successMessage,
                    'resource' => $resource
                ]);
            }

            return redirect()->back()->with('success', $successMessage);
        } catch (\Exception $e) {
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource via AJAX
     * 
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    protected function destroyResource(Request $request, $id, $modelClass, $successMessage = 'Data berhasil dihapus!')
    {
        try {
            $resource = $modelClass::findOrFail($id);
            $resource->delete();

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $successMessage
                ]);
            }

            return redirect()->back()->with('success', $successMessage);
        } catch (\Exception $e) {
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * Get resource data for AJAX requests
     * 
     * @param Request $request
     * @return JsonResponse
     */
    protected function getResourceData($modelClass)
    {
        try {
            $resources = $modelClass::all();
            return response()->json($resources);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Error fetching data: ' . $e->getMessage()
            ], 500);
        }
    }
}
