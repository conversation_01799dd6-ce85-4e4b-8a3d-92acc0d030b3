<?php

namespace App\Http\Controllers;

use App\Models\KategoriPekerjaan;
use App\Models\Project;
use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;


class ExcelController extends Controller
{
    // Property declarations for storing rows
    private $upahRows = [];
    private $bahanRows = [];
    private $alatRows = [];
    private $ahspFinalPriceRows = [];
    private $rabKategoriRows = [];

    /**
     * Convert number to words in Indonesian
     */
    private function terbilang($number)
    {
        $number = abs($number);
        $words = array(
            '',
            'satu',
            'dua',
            'tiga',
            'empat',
            'lima',
            'enam',
            'tujuh',
            'delapan',
            'sembilan',
            'sepuluh',
            'sebelas'
        );

        if ($number < 12) {
            return $words[$number];
        } elseif ($number < 20) {
            return $this->terbilang($number - 10) . ' belas';
        } elseif ($number < 100) {
            return $this->terbilang(floor($number / 10)) . ' puluh ' . $this->terbilang($number % 10);
        } elseif ($number < 200) {
            return 'seratus ' . $this->terbilang($number - 100);
        } elseif ($number < 1000) {
            return $this->terbilang(floor($number / 100)) . ' ratus ' . $this->terbilang($number % 100);
        } elseif ($number < 2000) {
            return 'seribu ' . $this->terbilang($number - 1000);
        } elseif ($number < 1000000) {
            return $this->terbilang(floor($number / 1000)) . ' ribu ' . $this->terbilang($number % 1000);
        } elseif ($number < 1000000000) {
            return $this->terbilang(floor($number / 1000000)) . ' juta ' . $this->terbilang($number % 1000000);
        } elseif ($number < 1000000000000) {
            return $this->terbilang(floor($number / 1000000000)) . ' milyar ' . $this->terbilang($number % 1000000000);
        } elseif ($number < 1000000000000000) {
            return $this->terbilang(floor($number / 1000000000000)) . ' trilyun ' . $this->terbilang($number % 1000000000000);
        } else {
            return 'Angka terlalu besar';
        };
    }

    public function downloadRabExcelFormula(Request $request)
    {
        // Get current project ID from session
        $projectId = session('project_id');

        if (!$projectId) {
            return redirect()->back()->with('error', 'Tidak ada proyek yang sedang dibuka');
        }

        // Get project data
        $project = Project::findOrFail($projectId);

        // Get export options
        $exportOptions = $request->has('export') ? explode(',', $request->export) : ['rekap', 'rab', 'ahsp', 'upah', 'bahan', 'alat', 'time-schedule', 'volume-calculations'];

        // Create new spreadsheet
        $spreadsheet = new Spreadsheet();

        // Remove default worksheet
        $spreadsheet->removeSheetByIndex(0);

        // Get RAB data
        $kategoriPekerjaans = KategoriPekerjaan::where('project_id', $projectId)
            ->with(['items', 'items.ahs', 'items.volumeCalculations', 'items.timeSchedule'])
            ->get();

        // Create sheets in the desired order

        // Variable untuk menyimpan referensi volume
        $volumeRefRows = [];

        // 1. Upah sheet if needed
        if (in_array('upah', $exportOptions)) {
            $this->createUpahSheet($spreadsheet);
        }

        // 2. Bahan sheet if needed
        if (in_array('bahan', $exportOptions)) {
            $this->createBahanSheet($spreadsheet);
        }

        // 3. Alat sheet if needed
        if (in_array('alat', $exportOptions)) {
            $this->createAlatSheet($spreadsheet);
        }

        // 4. Volume Calculations Sheet
        if (in_array('volume-calculations', $exportOptions)) {
            $volumeCalculationsSheet = $spreadsheet->createSheet();
            $volumeCalculationsSheet->setTitle('Volume Calculations');
            $volumeRefRows = $this->createVolumeCalculationsSheet($volumeCalculationsSheet, $project, $kategoriPekerjaans);
        }

        // 5. AHSP sheet if needed
        if (in_array('ahsp', $exportOptions)) {
            $ahspSheet = $spreadsheet->createSheet();
            $ahspSheet->setTitle('AHSP');
            $this->createAhspSheet($ahspSheet, $kategoriPekerjaans);
        }

        // 6. RAB Sheet
        $rabSheet = $spreadsheet->createSheet();
        $rabSheet->setTitle('RAB');
        $this->createRabSheet($rabSheet, $project, $kategoriPekerjaans, $volumeRefRows);

        // 7. REKAP Sheet
        $rekapSheet = $spreadsheet->createSheet();
        $rekapSheet->setTitle('REKAP');
        $this->createRekapSheet($rekapSheet, $project, $kategoriPekerjaans);

        // 8. Time Schedule Sheet
        if (in_array('time-schedule', $exportOptions)) {
            $timeScheduleSheet = $spreadsheet->createSheet();
            $timeScheduleSheet->setTitle('Time Schedule');
            $this->createTimeScheduleSheet($timeScheduleSheet, $project);
        }

        // Set REKAP as the active sheet
        if (in_array('rekap', $exportOptions)) {
            $spreadsheet->setActiveSheetIndexByName('REKAP');
        } else {
            // Jika REKAP tidak ada, gunakan sheet pertama
            $spreadsheet->setActiveSheetIndex(0);
        }

        // Format and styling
        $this->applyFormatting($spreadsheet);

        // Download
        $writer = new Xlsx($spreadsheet);
        $filename = 'RAB-' . $project->name . '-dengan-rumus.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    private function createRabSheet($sheet, $project, $kategoriPekerjaans, $volumeRefRows = [])
    {
        // Warna untuk header tabel dan kategori
        $headerFillColor = 'D9E1F2'; // Light blue
        $categoryFillColor = 'E0E0E0'; // Light gray

        // Set header
        $sheet->setCellValue('A1', 'RENCANA ANGGARAN BIAYA (RAB)');
        $sheet->mergeCells('A1:F1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Project info
        $sheet->setCellValue('A3', 'Nama Proyek:');
        $sheet->setCellValue('B3', $project->name);
        $sheet->setCellValue('A4', 'Lokasi:');
        $sheet->setCellValue('B4', $project->location);

        // Hapus border pada baris informasi proyek (row 3, 4, dan 5)
        $sheet->getStyle('A3:F5')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_NONE);
        // Untuk row 5, hanya hapus border bagian samping dan atas
        $sheet->getStyle('A5:F5')->getBorders()->getBottom()->setBorderStyle(Border::BORDER_THIN);

        // RAB Table headers
        $headers = ['No', 'Uraian Pekerjaan', 'Volume', 'Satuan', 'Harga Satuan', 'Jumlah Harga'];
        foreach (array_values($headers) as $col => $header) {
            $sheet->setCellValue(chr(65 + $col) . '6', $header);
        }

        // Menebalkan font pada header tabel
        $sheet->getStyle('A6:F6')->getFont()->setBold(true);
        $sheet->getStyle('A6:F6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Menambahkan warna latar untuk header tabel
        $sheet->getStyle('A6:F6')->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB($headerFillColor);

        $row = 7;
        $headerRow = 6;

        // Store kategori row ranges untuk referensi dari sheet REKAP
        $this->rabKategoriRows = [];

        foreach ($kategoriPekerjaans as $kategoriIndex => $kategori) {
            // Add kategori header with letter
            $kategoriLetter = chr(65 + $kategoriIndex);
            $sheet->setCellValue('A' . $row, $kategoriLetter);
            $sheet->setCellValue('B' . $row, $kategori->nama_kategori);
            $sheet->mergeCells('B' . $row . ':F' . $row);
            $sheet->getStyle('A' . $row . ':F' . $row)->getFont()->setBold(true);
            $sheet->getStyle('A' . $row . ':F' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB($categoryFillColor);
            $row++;

            // Catat baris awal item untuk kategori ini
            $kategoriItemStartRow = $row;

            // Reset item number for each category
            $itemNumber = 1;

            foreach ($kategori->items as $item) {
                $sheet->setCellValue('A' . $row, $itemNumber);
                $sheet->setCellValue('B' . $row, $item->uraian_item);

                // Referensikan Volume dari Volume Calculations jika tersedia
                if (isset($volumeRefRows[$item->id])) {
                    $volRef = $volumeRefRows[$item->id];
                    $sheet->setCellValue('C' . $row, "='Volume Calculations'!" . $volRef['column'] . $volRef['row']);
                } else {
                    $sheet->setCellValue('C' . $row, $item->volume);
                }

                $sheet->setCellValue('D' . $row, $item->satuan);

                // If item has AHSP, reference it using direct row reference
                if ($item->ahs_id) {
                    // Find the row in AHSP sheet where this AHSP's final price is
                    $ahspRow = $this->findAhspFinalPriceRow($item->ahs_id);
                    if ($ahspRow > 0) {
                        $sheet->setCellValue('E' . $row, "=AHSP!F{$ahspRow}");
                    } else {
                        $sheet->setCellValue('E' . $row, $item->harga_satuan);
                    }
                } else {
                    $sheet->setCellValue('E' . $row, $item->harga_satuan);
                }

                $sheet->setCellValue('F' . $row, "=C{$row}*E{$row}");

                // Hilangkan warna latar belakang pada baris item
                $sheet->getStyle('A' . $row . ':F' . $row)->getFill()->setFillType(Fill::FILL_NONE);

                $row++;
                $itemNumber++;
            }

            // Catat baris akhir item untuk kategori ini
            $kategoriItemEndRow = $row - 1;

            // Tambahkan total per kategori
            $sheet->setCellValue('A' . $row, '');
            $sheet->setCellValue('B' . $row, 'TOTAL ' . strtoupper($kategori->nama_kategori));
            $sheet->mergeCells('B' . $row . ':E' . $row);
            $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $sheet->setCellValue('F' . $row, "=SUM(F{$kategoriItemStartRow}:F{$kategoriItemEndRow})");
            $sheet->getStyle('B' . $row . ':F' . $row)->getFont()->setBold(true);

            // Hilangkan warna latar belakang pada baris total
            $sheet->getStyle('A' . $row . ':F' . $row)->getFill()->setFillType(Fill::FILL_NONE);

            // Catat baris total kategori untuk referensi dari sheet REKAP
            $kategoriTotalRow = $row;

            // Simpan range baris untuk kategori ini
            $this->rabKategoriRows[$kategori->id] = [
                'start' => $kategoriItemStartRow,
                'end' => $kategoriItemEndRow,
                'total' => $kategoriTotalRow
            ];

            // Add space between categories
            $row++;
        }

        // Total keseluruhan - sum dari total per kategori
        $sheet->setCellValue('A' . $row, 'TOTAL JUMLAH HARGA:');
        $sheet->mergeCells('A' . $row . ':E' . $row);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        // Buat formula untuk menjumlahkan semua total kategori
        $totalFormula = "=";
        $totalCount = 0;

        // Loop melalui semua kategori untuk membuat formula SUM
        foreach ($this->rabKategoriRows as $rowInfo) {
            if (isset($rowInfo['total'])) {
                if ($totalCount > 0) {
                    $totalFormula .= "+";
                }
                $totalFormula .= "F{$rowInfo['total']}";
                $totalCount++;
            }
        }

        // Jika tidak ada kategori, gunakan 0
        if ($totalCount == 0) {
            $totalFormula = "=0";
        }

        $sheet->setCellValue('F' . $row, $totalFormula);
        $sheet->getStyle('A' . $row . ':F' . $row)->getFont()->setBold(true);

        // Hilangkan warna latar belakang pada baris total keseluruhan
        $sheet->getStyle('A' . $row . ':F' . $row)->getFill()->setFillType(Fill::FILL_NONE);

        // Auto size columns for better readability
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Menerapkan border tebal pada pinggir tabel RAB
        $sheet->getStyle('A' . $headerRow . ':F' . $row)->applyFromArray([
            'borders' => [
                'outline' => [
                    'borderStyle' => Border::BORDER_THICK,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Memberikan border pada tabel
        $sheet->getStyle('A' . $headerRow . ':F' . $row)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Header border lebih tebal
        $sheet->getStyle('A' . $headerRow . ':F' . $headerRow)->applyFromArray([
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);
    }

    private function createAhspSheet($sheet, $kategoriPekerjaans)
    {
        // Set header
        $sheet->setCellValue('A1', 'ANALISA HARGA SATUAN PEKERJAAN (AHSP)');
        $sheet->mergeCells('A1:F1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Tidak ada header pada row 3
        $row = 3;

        // Collect all AHSP used in RAB
        $ahsIds = [];
        foreach ($kategoriPekerjaans as $kategori) {
            foreach ($kategori->items as $item) {
                if ($item->ahs_id && !in_array($item->ahs_id, $ahsIds)) {
                    $ahsIds[] = $item->ahs_id;
                }
            }
        }

        // Get AHSP data
        $ahsRecords = \App\Models\Ahs::whereIn('id', $ahsIds)
            ->with(['details' => function ($query) {
                $query->orderBy('kategori');
            }])
            ->get();

        foreach ($ahsRecords as $ahs) {
            // AHSP header tanpa nomor
            $sheet->setCellValue('A' . $row, $ahs->kode);
            $sheet->setCellValue('B' . $row, $ahs->judul);
            $sheet->mergeCells('B' . $row . ':F' . $row);
            $sheet->getStyle('A' . $row . ':F' . $row)->getFont()->setBold(true);
            // Hapus baris untuk menerapkan warna latar belakang abu-abu

            // Hilangkan border dari AHSP header
            $sheet->getStyle('A' . $row . ':F' . $row)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE);
            $row++;

            // Tambahkan spasi setelah AHSP header
            $sheet->getRowDimension($row - 1)->setRowHeight(20);

            // Tambahkan header kolom untuk setiap AHSP
            $headers = ['No', 'Uraian', 'Koefisien', 'Satuan', 'Harga Satuan', 'Jumlah Harga'];
            foreach (array_values($headers) as $col => $header) {
                $sheet->setCellValue(chr(65 + $col) . $row, $header);
            }
            $sheet->getStyle('A' . $row . ':F' . $row)->getFont()->setBold(true);
            $sheet->getStyle('A' . $row . ':F' . $row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            $row++;

            // Group by kategori
            $detailsByKategori = $ahs->details->groupBy('kategori');

            // Store subtotal rows for each category
            $categorySubtotalRows = [];

            // Process each kategori
            foreach (['upah', 'bahan', 'alat'] as $kategori) {
                // Kategori header - always show
                $kategoriLabel = strtoupper($kategori == 'upah' ? 'TENAGA KERJA' : $kategori);
                $kategoriNumber = $kategori == 'upah' ? 'A' : ($kategori == 'bahan' ? 'B' : 'C');
                $sheet->setCellValue('A' . $row, $kategoriNumber);
                $sheet->setCellValue('B' . $row, $kategoriLabel);
                $sheet->mergeCells('B' . $row . ':F' . $row);
                $sheet->getStyle('A' . $row . ':F' . $row)->getFont()->setBold(true);
                $sheet->getStyle('A' . $row . ':F' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('F2F2F2');
                $row++;

                // Details
                $startDetailRow = $row;

                if ($detailsByKategori->has($kategori)) {
                    foreach ($detailsByKategori[$kategori] as $detailIndex => $detail) {
                        $sheet->setCellValue('A' . $row, $kategoriNumber . '.' . ($detailIndex + 1));

                        // Get item details based on kategori
                        if ($kategori == 'upah') {
                            $item = \App\Models\Upah::find($detail->item_id);
                            $itemName = $item ? $item->uraian_tenaga : 'Upah tidak ditemukan';

                            // Reference to Upah sheet using INDEX-MATCH instead of direct cell reference
                            if ($item && $item->id) {
                                // Format: =INDEX(Upah!C:C, MATCH(item_id, Upah!E:E, 0))
                                $hargaRef = "=INDEX(Upah!C:C, MATCH({$item->id}, Upah!E:E, 0))";
                            } else {
                                // If item not found, use the item's price directly
                                $hargaRef = $item->harga ?? 0;
                            }
                        } elseif ($kategori == 'bahan') {
                            $item = \App\Models\Bahan::find($detail->item_id);
                            $itemName = $item ? $item->uraian_bahan : 'Bahan tidak ditemukan';

                            // Reference to Bahan sheet using INDEX-MATCH
                            if ($item && $item->id) {
                                // Format: =INDEX(Bahan!C:C, MATCH(item_id, Bahan!E:E, 0))
                                $hargaRef = "=INDEX(Bahan!C:C, MATCH({$item->id}, Bahan!E:E, 0))";
                            } else {
                                // If item not found, use the item's price directly
                                $hargaRef = $item->harga_bahan ?? 0;
                            }
                        } else { // alat
                            $item = \App\Models\Alat::find($detail->item_id);
                            $itemName = $item ? $item->uraian_alat : 'Alat tidak ditemukan';

                            // Reference to Alat sheet using INDEX-MATCH
                            if ($item && $item->id) {
                                // Format: =INDEX(Alat!C:C, MATCH(item_id, Alat!E:E, 0))
                                $hargaRef = "=INDEX(Alat!C:C, MATCH({$item->id}, Alat!E:E, 0))";
                            } else {
                                // If item not found, use the item's price directly
                                $hargaRef = $item->harga_alat ?? 0;
                            }
                        }

                        $sheet->setCellValue('B' . $row, $itemName);
                        $sheet->setCellValue('C' . $row, $detail->koefisien);
                        $sheet->setCellValue('D' . $row, $item ? $item->satuan : '-');
                        $sheet->setCellValue('E' . $row, $hargaRef);
                        $sheet->setCellValue('F' . $row, "=C{$row}*E{$row}");

                        $row++;
                    }
                } else {
                    // If no items in this category, show a message
                    $sheet->setCellValue('A' . $row, '');
                    $sheet->setCellValue('B' . $row, 'Tidak ada data ' . strtolower($kategoriLabel));
                    $sheet->mergeCells('B' . $row . ':F' . $row);
                    $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                    $row++;
                }

                $endDetailRow = $row - 1;

                // Subtotal for kategori - always show - merge cell A sampai E
                $sheet->setCellValue('A' . $row, 'Subtotal ' . $kategoriLabel);
                $sheet->mergeCells('A' . $row . ':E' . $row);
                $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

                if ($startDetailRow <= $endDetailRow && $detailsByKategori->has($kategori)) {
                    $sheet->setCellValue('F' . $row, "=SUM(F{$startDetailRow}:F{$endDetailRow})");
                } else {
                    $sheet->setCellValue('F' . $row, 0);
                }
                $sheet->getStyle('A' . $row . ':F' . $row)->getFont()->setBold(true);

                // Store the subtotal row for this category
                $categorySubtotalRows[$kategori] = $row;

                $row++;
            }

            // Add SUBTOTAL row - using the stored subtotal rows for each category - merge cell A sampai E
            $sheet->setCellValue('A' . $row, 'SUBTOTAL');
            $sheet->mergeCells('A' . $row . ':E' . $row);
            $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            $subtotalFormula = "F{$categorySubtotalRows['upah']}+F{$categorySubtotalRows['bahan']}+F{$categorySubtotalRows['alat']}";
            $sheet->setCellValue('F' . $row, "={$subtotalFormula}");
            $sheet->getStyle('A' . $row . ':F' . $row)->getFont()->setBold(true);
            $subtotalRow = $row;
            $row++;

            // Calculate overhead - merge cell A sampai E
            $sheet->setCellValue('A' . $row, 'OVERHEAD ' . $ahs->overhead . '%');
            $sheet->mergeCells('A' . $row . ':E' . $row);
            $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            // Calculate overhead as a percentage of the subtotal
            $sheet->setCellValue('F' . $row, "=F{$subtotalRow}*" . ($ahs->overhead / 100));
            $overheadRow = $row;
            $row++;

            // Grand total - HARGA SATUAN PEKERJAAN - merge cell A sampai E
            $sheet->setCellValue('A' . $row, 'HARGA SATUAN PEKERJAAN');
            $sheet->mergeCells('A' . $row . ':E' . $row);
            $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            // Sum of subtotal + overhead
            $sheet->setCellValue('F' . $row, "=F{$subtotalRow}+F{$overheadRow}");
            $sheet->getStyle('A' . $row . ':F' . $row)->getFont()->setBold(true);

            // Store the row number for this AHSP's final price
            $this->ahspFinalPriceRows[$ahs->id] = $row;

            $row++;

            // Add space between AHSP
            $row++;
            $sheet->getRowDimension($row - 1)->setRowHeight(20);
            // Remove border from space row
            $sheet->getStyle('A' . ($row - 1) . ':F' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE);
        }

        // Apply borders selectively to AHSP content
        $this->applyAhspBorders($sheet);

        // Auto size visible columns
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }

    private function createUpahSheet($spreadsheet)
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Upah');

        // Set header
        $sheet->setCellValue('A1', 'DAFTAR HARGA SATUAN UPAH');
        $sheet->mergeCells('A1:E1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Upah Table headers
        $headers = ['No', 'Uraian Upah', 'Harga Satuan', 'Satuan', 'ID'];
        foreach (array_values($headers) as $col => $header) {
            $sheet->setCellValue(chr(65 + $col) . '3', $header);
        }

        // Style the header row
        $sheet->getStyle('A3:D3')->getFont()->setBold(true);
        $sheet->getStyle('A3:D3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Get upah items used in the project
        $usedUpahIds = $this->getUsedUpahIds();
        $upahItems = \App\Models\Upah::whereIn('id', $usedUpahIds)->get();

        // Store upah rows for reference
        $this->upahRows = [];

        $row = 4;
        foreach ($upahItems as $index => $upah) {
            $this->upahRows[$upah->id] = $row; // Store actual row number

            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $upah->uraian_tenaga);
            $sheet->setCellValue('C' . $row, $upah->harga);
            $sheet->setCellValue('D' . $row, $upah->satuan);
            $sheet->setCellValue('E' . $row, $upah->id);
            $row++;
        }
        $lastDataRow = $row - 1;

        // Auto size columns
        foreach (range('A', 'E') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Hide the ID column
        $sheet->getColumnDimension('E')->setVisible(false);

        // Apply borders to the table
        $headerRow = 3;

        // Apply medium border to the table outline
        $sheet->getStyle('A' . $headerRow . ':D' . $lastDataRow)->applyFromArray([
            'borders' => [
                'outline' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Apply thin borders to all cells
        $sheet->getStyle('A' . $headerRow . ':D' . $lastDataRow)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Apply medium border to the bottom of the header row
        $sheet->getStyle('A' . $headerRow . ':D' . $headerRow)->applyFromArray([
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);
    }

    private function createBahanSheet($spreadsheet)
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Bahan');

        // Set header
        $sheet->setCellValue('A1', 'DAFTAR HARGA SATUAN BAHAN');
        $sheet->mergeCells('A1:E1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Bahan Table headers
        $headers = ['No', 'Uraian Bahan', 'Harga Satuan', 'Satuan', 'ID'];
        foreach (array_values($headers) as $col => $header) {
            $sheet->setCellValue(chr(65 + $col) . '3', $header);
        }

        // Style the header row
        $sheet->getStyle('A3:D3')->getFont()->setBold(true);
        $sheet->getStyle('A3:D3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Get bahan items used in the project
        $usedBahanIds = $this->getUsedBahanIds();
        $bahanItems = \App\Models\Bahan::whereIn('id', $usedBahanIds)->get();

        // Store bahan rows for reference
        $this->bahanRows = [];

        $row = 4;
        foreach ($bahanItems as $index => $bahan) {
            $this->bahanRows[$bahan->id] = $row; // Store actual row number

            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $bahan->uraian_bahan);
            $sheet->setCellValue('C' . $row, $bahan->harga_bahan);
            $sheet->setCellValue('D' . $row, $bahan->satuan);
            $sheet->setCellValue('E' . $row, $bahan->id);
            $row++;
        }
        $lastDataRow = $row - 1;

        // Auto size columns
        foreach (range('A', 'E') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Hide the ID column
        $sheet->getColumnDimension('E')->setVisible(false);

        // Apply borders to the table
        $headerRow = 3;

        // Apply medium border to the table outline
        $sheet->getStyle('A' . $headerRow . ':D' . $lastDataRow)->applyFromArray([
            'borders' => [
                'outline' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Apply thin borders to all cells
        $sheet->getStyle('A' . $headerRow . ':D' . $lastDataRow)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Apply medium border to the bottom of the header row
        $sheet->getStyle('A' . $headerRow . ':D' . $headerRow)->applyFromArray([
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);
    }

    private function createAlatSheet($spreadsheet)
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Alat');

        // Set header
        $sheet->setCellValue('A1', 'DAFTAR HARGA SATUAN ALAT');
        $sheet->mergeCells('A1:E1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Alat Table headers
        $headers = ['No', 'Uraian Alat', 'Harga Satuan', 'Satuan', 'ID'];
        foreach (array_values($headers) as $col => $header) {
            $sheet->setCellValue(chr(65 + $col) . '3', $header);
        }

        // Style the header row
        $sheet->getStyle('A3:D3')->getFont()->setBold(true);
        $sheet->getStyle('A3:D3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Get alat items used in the project
        $usedAlatIds = $this->getUsedAlatIds();
        $alatItems = \App\Models\Alat::whereIn('id', $usedAlatIds)->get();

        // Store alat rows for reference
        $this->alatRows = [];

        $row = 4;
        foreach ($alatItems as $index => $alat) {
            $this->alatRows[$alat->id] = $row; // Store actual row number

            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $alat->uraian_alat);
            $sheet->setCellValue('C' . $row, $alat->harga_alat);
            $sheet->setCellValue('D' . $row, $alat->satuan);
            $sheet->setCellValue('E' . $row, $alat->id);
            $row++;
        }
        $lastDataRow = $row - 1;

        // Auto size columns
        foreach (range('A', 'E') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Hide the ID column
        $sheet->getColumnDimension('E')->setVisible(false);

        // Apply borders to the table
        $headerRow = 3;

        // Apply medium border to the table outline
        $sheet->getStyle('A' . $headerRow . ':D' . $lastDataRow)->applyFromArray([
            'borders' => [
                'outline' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Apply thin borders to all cells
        $sheet->getStyle('A' . $headerRow . ':D' . $lastDataRow)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Apply medium border to the bottom of the header row
        $sheet->getStyle('A' . $headerRow . ':D' . $headerRow)->applyFromArray([
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);
    }

    private function applyFormatting($spreadsheet)
    {
        // Warna untuk header tabel
        $headerFillColor = 'D9E1F2'; // Light blue

        foreach ($spreadsheet->getAllSheets() as $sheet) {
            // Get all cells with data
            $highestRow = $sheet->getHighestRow();
            $highestColumn = $sheet->getHighestColumn();

            // Apply borders to all data cells
            $borderStyle = [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
            ];

            // Apply to data rows - but exclude AHSP headers and spaces between AHSP
            if ($highestRow > 3 && $sheet->getTitle() == 'AHSP') {
                // For AHSP sheet, borders are applied in the applyAhspBorders method
                // Hapus semua fill yang mungkin ada pada baris tanpa border
                for ($row = 3; $row <= $highestRow; $row++) {
                    $cellValue = $sheet->getCell('A' . $row)->getValue();
                    $cellBValue = $sheet->getCell('B' . $row)->getValue();

                    // Cek apakah ini baris header tabel (No, Uraian, dst)
                    if ($cellValue === 'No' && (in_array($cellBValue, ['Uraian', 'Uraian Pekerjaan', 'Keterangan']))) {
                        // Terapkan warna latar untuk header tabel
                        $sheet->getStyle('A' . $row . ':' . $highestColumn . $row)->getFill()
                            ->setFillType(Fill::FILL_SOLID)
                            ->getStartColor()->setRGB($headerFillColor);
                    }
                }
            } else if ($highestRow > 3 && $sheet->getTitle() == 'Volume Calculations') {
                // For Volume Calculations sheet, borders are applied in the applyVolumeCalculationsBorders method
                // Hapus semua fill yang mungkin ada pada baris tanpa border
                for ($row = 3; $row <= $highestRow; $row++) {
                    $cellValue = $sheet->getCell('A' . $row)->getValue();
                    $cellBValue = $sheet->getCell('B' . $row)->getValue();

                    // Kecuali untuk header kategori yang diberi warna abu-abu terang
                    $isCategoryHeader = (is_string($cellValue) && strlen($cellValue) == 1 && ctype_alpha($cellValue));

                    if (!$isCategoryHeader) {
                        // Hapus semua fill kecuali item header (sudah diatur di createVolumeCalculationsSheet)
                        if (!(is_string($cellValue) && preg_match('/^[A-Z]\.[0-9]+$/', $cellValue))) {
                            $sheet->getStyle('A' . $row . ':' . $highestColumn . $row)->getFill()
                                ->setFillType(Fill::FILL_NONE);
                        }

                        // Cek apakah ini baris header tabel (No, Keterangan, dst)
                        if ($cellValue === 'No' && $cellBValue === 'Keterangan') {
                            // Terapkan warna latar untuk header tabel
                            $sheet->getStyle('A' . $row . ':' . $highestColumn . $row)->getFill()
                                ->setFillType(Fill::FILL_SOLID)
                                ->getStartColor()->setRGB($headerFillColor);
                        }
                    }
                }
            } else if ($highestRow > 3 && !in_array($sheet->getTitle(), ['Upah', 'Bahan', 'Alat', 'Volume Calculations'])) {
                // For RAB and REKAP sheets, apply borders to all data rows except rows 2, 3, 4, and 5
                $sheet->getStyle('A6:' . $highestColumn . $highestRow)->applyFromArray($borderStyle);

                // Remove borders from rows 2, 3, 4, and 5
                $sheet->getStyle('A2:' . $highestColumn . '2')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_NONE);
                $sheet->getStyle('A3:' . $highestColumn . '3')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_NONE);
                $sheet->getStyle('A4:' . $highestColumn . '4')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_NONE);
                $sheet->getStyle('A5:' . $highestColumn . '5')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_NONE);

                // For row 5, only remove side and top borders, keep bottom border
                if ($sheet->getTitle() == 'RAB' || $sheet->getTitle() == 'REKAP') {
                    $sheet->getStyle('A5:' . $highestColumn . '5')->getBorders()->getBottom()->setBorderStyle(Border::BORDER_THIN);
                }

                // Terapkan warna latar untuk header tabel (baris 6)
                $sheet->getStyle('A6:' . $highestColumn . '6')->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setRGB($headerFillColor);

                // Hapus warna latar untuk baris lain (kecuali header kategori)
                for ($row = 7; $row <= $highestRow; $row++) {
                    $cellValue = $sheet->getCell('A' . $row)->getValue();
                    $cellBValue = $sheet->getCell('B' . $row)->getValue();

                    // Kecuali untuk header kategori yang diberi warna abu-abu terang
                    $isCategoryHeader = (is_string($cellValue) && strlen($cellValue) == 1 && ctype_alpha($cellValue));

                    if (!$isCategoryHeader) {
                        $sheet->getStyle('A' . $row . ':' . $highestColumn . $row)->getFill()
                            ->setFillType(Fill::FILL_NONE);
                    }
                }
            } else if (in_array($sheet->getTitle(), ['Upah', 'Bahan', 'Alat'])) {
                // For Upah, Bahan, and Alat sheets

                // Terapkan warna latar untuk header tabel (baris 3)
                $sheet->getStyle('A3:' . $highestColumn . '3')->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setRGB($headerFillColor);

                // Hapus warna latar untuk baris data
                for ($row = 4; $row <= $highestRow; $row++) {
                    $sheet->getStyle('A' . $row . ':' . $highestColumn . $row)->getFill()
                        ->setFillType(Fill::FILL_NONE);
                }
            }

            // Apply Indonesian Rupiah Accounting format to currency columns
            if ($sheet->getTitle() == 'RAB') {
                $sheet->getStyle('E4:F' . $highestRow)->getNumberFormat()->setFormatCode('_-[$Rp-421]* #,##0.00_-;-[$Rp-421]* #,##0.00_-;_-[$Rp-421]* "-"??_-;_-@_-');
            } elseif ($sheet->getTitle() == 'AHSP') {
                $sheet->getStyle('E4:F' . $highestRow)->getNumberFormat()->setFormatCode('_-[$Rp-421]* #,##0.00_-;-[$Rp-421]* #,##0.00_-;_-[$Rp-421]* "-"??_-;_-@_-');
            } elseif (in_array($sheet->getTitle(), ['Upah', 'Bahan', 'Alat'])) {
                $sheet->getStyle('C4:C' . $highestRow)->getNumberFormat()->setFormatCode('_-[$Rp-421]* #,##0.00_-;-[$Rp-421]* #,##0.00_-;_-[$Rp-421]* "-"??_-;_-@_-');
            }

            // Center align specific columns
            if ($sheet->getTitle() == 'RAB') {
                $sheet->getStyle('A4:A' . $highestRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                $sheet->getStyle('C4:D' . $highestRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            } elseif (in_array($sheet->getTitle(), ['Upah', 'Bahan', 'Alat'])) {
                $sheet->getStyle('A4:A' . $highestRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                $sheet->getStyle('D4:D' . $highestRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            } elseif ($sheet->getTitle() == 'Time Schedule') {
                // Terapkan warna latar untuk header tabel (baris 6)
                $sheet->getStyle('A6:G6')->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setRGB($headerFillColor);

                // Hapus warna latar untuk baris data
                for ($row = 7; $row <= $highestRow; $row++) {
                    $sheet->getStyle('A' . $row . ':G' . $row)->getFill()
                        ->setFillType(Fill::FILL_NONE);
                }
            }
        }
    }

    // We no longer need these helper methods as we're using VLOOKUP

    private function findAhspFinalPriceRow($ahsId)
    {
        // If we've already calculated this AHSP's row, return it
        if (isset($this->ahspFinalPriceRows[$ahsId])) {
            return $this->ahspFinalPriceRows[$ahsId];
        }

        // Otherwise, return 0 (will be populated during AHSP sheet creation)
        return 0;
    }

    // Helper methods to get used item IDs
    private function getUsedUpahIds()
    {
        $projectId = session('project_id');

        // Get all AHS IDs used in the project through kategori_pekerjaans
        $ahsIds = \App\Models\KategoriPekerjaan::where('project_id', $projectId)
            ->join('item_pekerjaans', 'kategori_pekerjaans.id', '=', 'item_pekerjaans.kategori_pekerjaan_id')
            ->whereNotNull('item_pekerjaans.ahs_id')
            ->pluck('item_pekerjaans.ahs_id')
            ->toArray();

        // Get all upah IDs used in these AHS
        $upahIds = \App\Models\AhspDetail::whereIn('ahs_id', $ahsIds)
            ->where('kategori', 'upah')
            ->pluck('item_id')
            ->toArray();

        return array_unique($upahIds);
    }

    private function getUsedBahanIds()
    {
        $projectId = session('project_id');

        // Get all AHS IDs used in the project through kategori_pekerjaans
        $ahsIds = \App\Models\KategoriPekerjaan::where('project_id', $projectId)
            ->join('item_pekerjaans', 'kategori_pekerjaans.id', '=', 'item_pekerjaans.kategori_pekerjaan_id')
            ->whereNotNull('item_pekerjaans.ahs_id')
            ->pluck('item_pekerjaans.ahs_id')
            ->toArray();

        // Get all bahan IDs used in these AHS
        $bahanIds = \App\Models\AhspDetail::whereIn('ahs_id', $ahsIds)
            ->where('kategori', 'bahan')
            ->pluck('item_id')
            ->toArray();

        return array_unique($bahanIds);
    }

    private function createRekapSheet($sheet, $project, $kategoriPekerjaans)
    {
        // Warna untuk header tabel
        $headerFillColor = 'D9E1F2'; // Light blue

        // Set header
        $sheet->setCellValue('A1', 'REKAPITULASI RENCANA ANGGARAN BIAYA');
        $sheet->mergeCells('A1:C1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Project info
        $sheet->setCellValue('A3', 'Nama Proyek:');
        $sheet->setCellValue('B3', $project->name);
        $sheet->setCellValue('A4', 'Lokasi:');
        $sheet->setCellValue('B4', $project->location);

        // Hapus border pada baris informasi proyek (row 3, 4, dan 5)
        $sheet->getStyle('A3:C5')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_NONE);
        // Untuk row 5, hanya hapus border bagian samping dan atas
        $sheet->getStyle('A5:C5')->getBorders()->getBottom()->setBorderStyle(Border::BORDER_THIN);

        // REKAP Table headers
        $headers = ['No', 'Uraian Pekerjaan', 'Jumlah Harga'];
        foreach (array_values($headers) as $col => $header) {
            $sheet->setCellValue(chr(65 + $col) . '6', $header);
        }

        // Menebalkan font pada header tabel
        $sheet->getStyle('A6:C6')->getFont()->setBold(true);
        $sheet->getStyle('A6:C6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Tambahkan warna latar untuk header tabel
        $sheet->getStyle('A6:C6')->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB($headerFillColor);

        $row = 7;
        $headerRow = 6;
        $firstDataRow = $row;

        foreach ($kategoriPekerjaans as $kategoriIndex => $kategori) {
            // Add kategori to REKAP - using SUM formula from RAB sheet
            $kategoriLetter = chr(65 + $kategoriIndex);
            $sheet->setCellValue('A' . $row, $kategoriLetter);
            $sheet->setCellValue('B' . $row, strtoupper($kategori->nama_kategori));

            // Create formula to reference the total per kategori from RAB sheet
            // Format: =RAB!F30 - dynamically find the total row
            $rabItemRows = $this->findRabItemRowsByKategori($kategori->id);
            if (!empty($rabItemRows) && isset($rabItemRows['total'])) {
                $sheet->setCellValue('C' . $row, "=RAB!F{$rabItemRows['total']}");
            } else {
                $sheet->setCellValue('C' . $row, 0);
            }

            // Hapus warna latar belakang pada baris item
            $sheet->getStyle('A' . $row . ':C' . $row)->getFill()->setFillType(Fill::FILL_NONE);

            $row++;
        }

        $lastDataRow = $row - 1;

        // Add JUMLAH row - sum all kategori totals
        $sheet->setCellValue('A' . $row, 'JUMLAH');
        $sheet->mergeCells('A' . $row . ':B' . $row);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->setCellValue('C' . $row, "=SUM(C{$firstDataRow}:C{$lastDataRow})");
        $sheet->getStyle('A' . $row . ':C' . $row)->getFont()->setBold(true);

        // Hapus warna latar belakang pada baris jumlah
        $sheet->getStyle('A' . $row . ':C' . $row)->getFill()->setFillType(Fill::FILL_NONE);

        $jumlahRow = $row;
        $row++;

        // Add PPN row
        $ppnPercentage = $project->ppn;
        // Calculate PPN amount using formula
        $sheet->setCellValue('A' . $row, 'PPN ' . $ppnPercentage . '%');
        $sheet->mergeCells('A' . $row . ':B' . $row);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->setCellValue('C' . $row, "=C{$jumlahRow}*{$ppnPercentage}/100");
        $sheet->getStyle('A' . $row . ':C' . $row)->getFont()->setBold(true);

        // Hapus warna latar belakang pada baris PPN
        $sheet->getStyle('A' . $row . ':C' . $row)->getFill()->setFillType(Fill::FILL_NONE);

        $ppnRow = $row;
        $row++;

        // Add TOTAL row
        $sheet->setCellValue('A' . $row, 'TOTAL');
        $sheet->mergeCells('A' . $row . ':B' . $row);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->setCellValue('C' . $row, "=C{$jumlahRow}+C{$ppnRow}");
        $sheet->getStyle('A' . $row . ':C' . $row)->getFont()->setBold(true);

        // Hapus warna latar belakang pada baris total
        $sheet->getStyle('A' . $row . ':C' . $row)->getFill()->setFillType(Fill::FILL_NONE);

        $totalRow = $row;
        $row++;

        // Add DIBULATKAN row
        $sheet->setCellValue('A' . $row, 'DIBULATKAN');
        $sheet->mergeCells('A' . $row . ':B' . $row);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->setCellValue('C' . $row, "=FLOOR(C{$totalRow}/1000,1)*1000");
        $sheet->getStyle('A' . $row . ':C' . $row)->getFont()->setBold(true);

        // Hapus warna latar belakang pada baris dibulatkan
        $sheet->getStyle('A' . $row . ':C' . $row)->getFill()->setFillType(Fill::FILL_NONE);

        $row++;

        // Add Terbilang row with the spelled-out amount
        // Add an empty row below for the merge
        $terbilangRow = $row;

        // Get the dibulatkan value and convert it to terbilang
        // Calculate the dibulatkan value (rounded to nearest 1000)
        $dibulatkanValue = floor(($sheet->getCell('C' . $jumlahRow)->getCalculatedValue() +
            ($sheet->getCell('C' . $jumlahRow)->getCalculatedValue() * $ppnPercentage / 100)) / 1000) * 1000;

        // Convert to terbilang
        $terbilangText = 'Terbilang: ' . ucwords($this->terbilang($dibulatkanValue)) . ' Rupiah';

        // Set the terbilang text in the merged cell
        $sheet->setCellValue('A' . $terbilangRow, $terbilangText);

        // Add an empty row below
        $row++;
        $sheet->setCellValue('A' . $row, '');

        // Merge the terbilang row with the row below
        $sheet->mergeCells('A' . $terbilangRow . ':C' . $row);
        $sheet->getStyle('A' . $terbilangRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A' . $terbilangRow)->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A' . $terbilangRow)->getAlignment()->setWrapText(true);
        $sheet->getStyle('A' . $terbilangRow)->getFont()->setBold(true);

        // Set row height untuk baris terbilang agar teks yang panjang dapat ditampilkan dengan baik
        $sheet->getRowDimension($terbilangRow)->setRowHeight(40);
        $sheet->getRowDimension($row)->setRowHeight(40);

        // Hapus warna latar belakang pada baris terbilang
        $sheet->getStyle('A' . $terbilangRow . ':C' . $row)->getFill()->setFillType(Fill::FILL_NONE);

        // Increase row for next content
        $row++;

        // Format currency columns with Indonesian Rupiah Accounting format
        $sheet->getStyle('C7:C' . $row)->getNumberFormat()->setFormatCode('_-[$Rp-421]* #,##0.00_-;-[$Rp-421]* #,##0.00_-;_-[$Rp-421]* "-"??_-;_-@_-');

        // Auto size columns
        foreach (range('A', 'C') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Menerapkan border tebal pada pinggir tabel REKAP
        $sheet->getStyle('A' . $headerRow . ':C' . ($row - 1))->applyFromArray([
            'borders' => [
                'outline' => [
                    'borderStyle' => Border::BORDER_THICK,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Memberikan border pada tabel
        $sheet->getStyle('A' . $headerRow . ':C' . ($row - 1))->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Memberikan border pada sel terbilang yang di-merge, tapi hapus border pada baris di bawahnya
        $sheet->getStyle('A' . $terbilangRow . ':C' . ($terbilangRow + 1))->applyFromArray([
            'borders' => [
                'outline' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Hapus border pada baris di bawah baris terbilang
        $sheet->getStyle('A' . ($terbilangRow + 2) . ':C' . ($terbilangRow + 2))->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_NONE);

        // Header border lebih tebal
        $sheet->getStyle('A' . $headerRow . ':C' . $headerRow)->applyFromArray([
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);
    }

    // Method untuk mendapatkan range baris item di RAB berdasarkan kategori
    private function findRabItemRowsByKategori($kategoriId)
    {
        if (isset($this->rabKategoriRows[$kategoriId])) {
            return $this->rabKategoriRows[$kategoriId];
        }
        return [];
    }

    private function applyAhspBorders($sheet)
    {
        // Define border styles
        $thinBorderStyle = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ];

        $headerBorderStyle = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ];

        // Warna untuk header tabel
        $headerFillColor = 'D9E1F2'; // Light blue

        // Get all rows in the sheet
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

        // Pertama, hapus semua border dan warna latar
        $sheet->getStyle('A1:' . $highestColumn . $highestRow)
            ->getBorders()
            ->getAllBorders()
            ->setBorderStyle(Border::BORDER_NONE);

        // Hapus semua warna latar belakang
        for ($row = 1; $row <= $highestRow; $row++) {
            $sheet->getStyle('A' . $row . ':' . $highestColumn . $row)->getFill()
                ->setFillType(Fill::FILL_NONE);
        }

        // Variables to track AHSP table ranges
        $tableStartRow = 0;
        $tableEndRow = 0;
        $inAhspTable = false;

        // Iterate through all rows
        for ($row = 3; $row <= $highestRow; $row++) {
            // Check if this is a header row or space row
            $cellValue = $sheet->getCell('A' . $row)->getValue();
            $cellBValue = $sheet->getCell('B' . $row)->getValue();

            // Check if this is a table header row (has 'No', 'Uraian', etc.)
            $isHeaderTableRow = false;
            if ($cellValue === 'No' && in_array($cellBValue, ['Uraian', 'Uraian Pekerjaan'])) {
                $isHeaderTableRow = true;
                $tableStartRow = $row;
                $inAhspTable = true;

                // Terapkan warna latar untuk header tabel
                $sheet->getStyle('A' . $row . ':F' . $row)->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setRGB($headerFillColor);
            }

            // Check if this is the HARGA SATUAN PEKERJAAN row (end of table)
            if (is_string($cellValue) && strpos($cellValue, 'HARGA SATUAN PEKERJAAN') === 0) {
                if ($inAhspTable && $tableStartRow > 0) {
                    $tableEndRow = $row;

                    // Apply borders to the complete table
                    if ($tableEndRow >= $tableStartRow) {
                        // Apply thin borders to all cells in the table
                        $sheet->getStyle('A' . $tableStartRow . ':F' . $tableEndRow)->applyFromArray($thinBorderStyle);

                        // Apply medium border to the bottom of the header row
                        $sheet->getStyle('A' . $tableStartRow . ':F' . $tableStartRow)->applyFromArray($headerBorderStyle);

                        // Reset table tracking
                        $tableStartRow = 0;
                        $tableEndRow = 0;
                        $inAhspTable = false;
                    }
                }
            }

            // Khusus untuk kategori headers (A, B, C)
            if (($cellValue === 'A' && $cellBValue === 'TENAGA KERJA') ||
                ($cellValue === 'B' && $cellBValue === 'BAHAN') ||
                ($cellValue === 'C' && $cellBValue === 'ALAT')
            ) {
                // Tetap berikan warna abu-abu muda
                $sheet->getStyle('A' . $row . ':F' . $row)->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('F2F2F2');
            }

            // Dihapus kode untuk memberikan warna pada judul AHSP dan kode AHSP
        }

        // Handle any remaining AHSP table (in case the last table wasn't properly closed)
        if ($inAhspTable && $tableStartRow > 0) {
            $sheet->getStyle('A' . $tableStartRow . ':F' . $highestRow)->applyFromArray($thinBorderStyle);
            $sheet->getStyle('A' . $tableStartRow . ':F' . $tableStartRow)->applyFromArray($headerBorderStyle);
        }
    }

    private function getUsedAlatIds()
    {
        $projectId = session('project_id');

        // Get all AHS IDs used in the project through kategori_pekerjaans
        $ahsIds = \App\Models\KategoriPekerjaan::where('project_id', $projectId)
            ->join('item_pekerjaans', 'kategori_pekerjaans.id', '=', 'item_pekerjaans.kategori_pekerjaan_id')
            ->whereNotNull('item_pekerjaans.ahs_id')
            ->pluck('item_pekerjaans.ahs_id')
            ->toArray();

        // Get all alat IDs used in these AHS
        $alatIds = \App\Models\AhspDetail::whereIn('ahs_id', $ahsIds)
            ->where('kategori', 'alat')
            ->pluck('item_id')
            ->toArray();

        return array_unique($alatIds);
    }

    /**
     * Membuat sheet Time Schedule
     */
    private function createTimeScheduleSheet($sheet, $project)
    {
        // Set header
        $sheet->setCellValue('A1', 'TIME SCHEDULE (KURVA S)');
        $sheet->mergeCells('A1:G1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Project info
        $sheet->setCellValue('A3', 'Nama Proyek:');
        $sheet->setCellValue('B3', $project->name);
        $sheet->setCellValue('A4', 'Lokasi:');
        $sheet->setCellValue('B4', $project->location);

        // Hapus border pada baris informasi proyek
        $sheet->getStyle('A3:G5')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_NONE);
        // Untuk row 5, hanya hapus border bagian samping dan atas
        $sheet->getStyle('A5:G5')->getBorders()->getBottom()->setBorderStyle(Border::BORDER_THIN);

        // Time Schedule Table headers
        $headers = ['No', 'Uraian Pekerjaan', 'Bobot (%)', 'Durasi (hari)', 'Tanggal Mulai', 'Tanggal Selesai', 'Progress (%)'];
        foreach (array_values($headers) as $col => $header) {
            $sheet->setCellValue(chr(65 + $col) . '6', $header);
        }

        // Menebalkan font pada header tabel
        $sheet->getStyle('A6:G6')->getFont()->setBold(true);
        $sheet->getStyle('A6:G6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $row = 7;
        $headerRow = 6;
        $itemNumber = 1;
        $totalBobot = 0;

        // Ambil semua time schedule untuk proyek ini
        $timeSchedules = \App\Models\TimeSchedule::where('project_id', $project->id)
            ->orderBy('tanggal_mulai')
            ->get();

        foreach ($timeSchedules as $schedule) {
            $sheet->setCellValue('A' . $row, $itemNumber++);
            $sheet->setCellValue('B' . $row, $schedule->nama_kegiatan);
            $sheet->setCellValue('C' . $row, $schedule->bobot);
            $sheet->setCellValue('D' . $row, $schedule->durasi);
            $sheet->setCellValue('E' . $row, $schedule->tanggal_mulai->format('Y-m-d'));
            $sheet->setCellValue('F' . $row, $schedule->tanggal_selesai->format('Y-m-d'));
            $sheet->setCellValue('G' . $row, $schedule->progress);

            $totalBobot += $schedule->bobot;
            $row++;
        }

        // Add Total row
        $sheet->setCellValue('A' . $row, '');
        $sheet->setCellValue('B' . $row, 'TOTAL');
        $sheet->setCellValue('C' . $row, $totalBobot);
        $sheet->getStyle('A' . $row . ':G' . $row)->getFont()->setBold(true);
        $row++;

        // Auto size columns
        foreach (range('A', 'G') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Menerapkan border tebal pada pinggir tabel
        $sheet->getStyle('A' . $headerRow . ':G' . ($row - 1))->applyFromArray([
            'borders' => [
                'outline' => [
                    'borderStyle' => Border::BORDER_THICK,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Memberikan border pada tabel
        $sheet->getStyle('A' . $headerRow . ':G' . ($row - 1))->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Header border lebih tebal
        $sheet->getStyle('A' . $headerRow . ':G' . $headerRow)->applyFromArray([
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Format persentase untuk kolom bobot dan progress
        $sheet->getStyle('C7:C' . ($row - 1))->getNumberFormat()->setFormatCode('0.00%');
        $sheet->getStyle('G7:G' . ($row - 1))->getNumberFormat()->setFormatCode('0.00%');

        // Konversi nilai bobot dan progress dari desimal ke persentase
        for ($i = 7; $i < $row; $i++) {
            $bobotValue = $sheet->getCell('C' . $i)->getValue();
            if (is_numeric($bobotValue)) {
                $sheet->setCellValue('C' . $i, $bobotValue / 100);
            }

            $progressValue = $sheet->getCell('G' . $i)->getValue();
            if (is_numeric($progressValue)) {
                $sheet->setCellValue('G' . $i, $progressValue / 100);
            }
        }
    }

    /**
     * Membuat sheet Volume Calculations
     */
    private function createVolumeCalculationsSheet($sheet, $project, $kategoriPekerjaans)
    {
        // Set header
        $sheet->setCellValue('A1', 'PERHITUNGAN VOLUME');
        $sheet->mergeCells('A1:I1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Project info
        $sheet->setCellValue('A3', 'Nama Proyek:');
        $sheet->setCellValue('B3', $project->name);
        $sheet->setCellValue('A4', 'Lokasi:');
        $sheet->setCellValue('B4', $project->location);

        // Hapus semua border pada informasi proyek
        $sheet->getStyle('A3:I5')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_NONE);

        $row = 7;
        $itemNumber = 1;
        $kategoriNumber = 'A';

        // Buat array untuk menyimpan referensi baris total volume untuk setiap item
        $volumeRefRows = [];

        // Loop melalui semua kategori
        foreach ($kategoriPekerjaans as $kategoriIndex => $kategori) {
            // Tambahkan header kategori
            $sheet->setCellValue('A' . $row, $kategoriNumber);
            $sheet->setCellValue('B' . $row, strtoupper($kategori->nama_kategori));
            $sheet->mergeCells('B' . $row . ':I' . $row);
            $sheet->getStyle('A' . $row . ':I' . $row)->getFont()->setBold(true);
            // Hapus penerapan warna latar belakang untuk header kategori
            $row++;

            // Reset item number untuk setiap kategori
            $itemNumber = 1;

            // Loop melalui item pekerjaan dalam kategori ini
            foreach ($kategori->items as $item) {
                // Ambil perhitungan volume untuk item ini
                $volumeCalculations = \App\Models\VolumeCalculation::where('item_pekerjaan_id', $item->id)->get();

                if ($volumeCalculations->count() > 0) {
                    // Tambahkan header item pekerjaan
                    $sheet->setCellValue('A' . $row, $kategoriNumber . '.' . $itemNumber);
                    $sheet->setCellValue('B' . $row, $item->uraian_item);
                    $sheet->mergeCells('B' . $row . ':I' . $row);
                    $sheet->getStyle('A' . $row . ':I' . $row)->getFont()->setBold(true);
                    // Hapus penerapan warna latar belakang untuk header item
                    $row++;

                    // Ambil satuan dari item pertama
                    $satuan = $item->satuan;

                    // Tentukan kolom yang akan ditampilkan berdasarkan satuan
                    $headerColumns = ['A' => 'No', 'B' => 'Keterangan', 'C' => 'Jumlah'];

                    // Layout untuk semua jenis satuan
                    if (in_array($satuan, ['unit', 'bh', 'ls'])) {
                        // Layout untuk unit, bh, ls
                        $headerColumns['D'] = 'Satuan';
                        // Kolom E-H akan di-merge untuk Hasil
                        $headerColumns['E'] = 'Hasil';
                        // Tipe diletakkan di kolom I
                        $headerColumns['I'] = 'Tipe';
                    } elseif ($satuan === 'kg') {
                        // Layout untuk kg
                        $headerColumns['D'] = 'Panjang';
                        $headerColumns['E'] = 'Berat Jenis';
                        $headerColumns['F'] = 'Satuan';
                        // Kolom G-H akan di-merge untuk Hasil
                        $headerColumns['G'] = 'Hasil';
                        // Tipe diletakkan di kolom I
                        $headerColumns['I'] = 'Tipe';
                    } elseif ($satuan === 'ltr') {
                        // Layout untuk ltr
                        $headerColumns['D'] = 'Liter';
                        $headerColumns['E'] = 'Satuan';
                        // Kolom F-H akan di-merge untuk Hasil
                        $headerColumns['F'] = 'Hasil';
                        // Tipe diletakkan di kolom I
                        $headerColumns['I'] = 'Tipe';
                    } else {
                        // Layout untuk m3, m2, m
                        if (in_array($satuan, ['m3', 'm2', 'm'])) {
                            $headerColumns['D'] = 'Panjang';

                            if (in_array($satuan, ['m3', 'm2'])) {
                                $headerColumns['E'] = 'Lebar';
                            }

                            if ($satuan === 'm3') {
                                $headerColumns['F'] = 'Tinggi';
                            }
                        }

                        // Selalu tampilkan kolom satuan, hasil, dan tipe
                        $lastColumn = chr(65 + count($headerColumns));
                        $headerColumns[$lastColumn] = 'Satuan';
                        $lastColumn = chr(65 + count($headerColumns));
                        $headerColumns[$lastColumn] = 'Hasil';
                        // Tipe diletakkan di kolom I
                        $headerColumns['I'] = 'Tipe';
                    }

                    // Jumlah kolom yang digunakan
                    $columnCount = count($headerColumns);
                    $lastColumnLetter = chr(64 + $columnCount);

                    // Tambahkan header tabel untuk perhitungan volume
                    foreach ($headerColumns as $col => $header) {
                        $sheet->setCellValue($col . $row, $header);
                    }

                    // Styling header
                    $sheet->getStyle('A' . $row . ':I' . $row)->getFont()->setBold(true);
                    $sheet->getStyle('A' . $row . ':I' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

                    // Lakukan merge pada kolom hasil berdasarkan jenis satuan
                    if (in_array($satuan, ['unit', 'bh', 'ls'])) {
                        $sheet->mergeCells('E' . $row . ':H' . $row);
                    } elseif ($satuan === 'kg') {
                        $sheet->mergeCells('G' . $row . ':H' . $row);
                    } elseif ($satuan === 'ltr') {
                        $sheet->mergeCells('F' . $row . ':H' . $row);
                    }

                    $headerRow = $row;
                    $row++;
                    $dataStartRow = $row;

                    // Tambahkan data perhitungan volume
                    $calcNumber = 1;
                    foreach ($volumeCalculations as $calc) {
                        if (in_array($satuan, ['unit', 'bh', 'ls'])) {
                            // Layout untuk unit, bh, ls
                            $sheet->setCellValue('A' . $row, $calcNumber++);
                            $sheet->setCellValue('B' . $row, $calc->keterangan);
                            $sheet->setCellValue('C' . $row, $calc->jumlah);
                            $sheet->setCellValue('D' . $row, $calc->satuan);
                            // Merge kolom E-H untuk hasil
                            $sheet->mergeCells('E' . $row . ':H' . $row);
                            $sheet->setCellValue('E' . $row, $calc->hasil);
                            $sheet->setCellValue('I' . $row, ucfirst($calc->calculation_type));

                            // Styling untuk merged cell hasil
                            $sheet->getStyle('E' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                        } elseif ($satuan === 'kg') {
                            // Layout untuk kg
                            $sheet->setCellValue('A' . $row, $calcNumber++);
                            $sheet->setCellValue('B' . $row, $calc->keterangan);
                            $sheet->setCellValue('C' . $row, $calc->jumlah);
                            $sheet->setCellValue('D' . $row, $calc->panjang);
                            $sheet->setCellValue('E' . $row, $calc->berat_jenis);
                            $sheet->setCellValue('F' . $row, $calc->satuan);
                            // Merge kolom G-H untuk hasil
                            $sheet->mergeCells('G' . $row . ':H' . $row);
                            $sheet->setCellValue('G' . $row, $calc->hasil);
                            $sheet->setCellValue('I' . $row, ucfirst($calc->calculation_type));

                            // Styling untuk merged cell hasil
                            $sheet->getStyle('G' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                        } elseif ($satuan === 'ltr') {
                            // Layout untuk ltr
                            $sheet->setCellValue('A' . $row, $calcNumber++);
                            $sheet->setCellValue('B' . $row, $calc->keterangan);
                            $sheet->setCellValue('C' . $row, $calc->jumlah);
                            $sheet->setCellValue('D' . $row, $calc->liter);
                            $sheet->setCellValue('E' . $row, $calc->satuan);
                            // Merge kolom F-H untuk hasil
                            $sheet->mergeCells('F' . $row . ':H' . $row);
                            $sheet->setCellValue('F' . $row, $calc->hasil);
                            $sheet->setCellValue('I' . $row, ucfirst($calc->calculation_type));

                            // Styling untuk merged cell hasil
                            $sheet->getStyle('F' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                        } else {
                            // Layout untuk m3, m2, m (standard layout)
                            $dataColumns = [];
                            $dataColumns['A'] = $calcNumber++;
                            $dataColumns['B'] = $calc->keterangan;
                            $dataColumns['C'] = $calc->jumlah;

                            if (in_array($satuan, ['m3', 'm2', 'm'])) {
                                $dataColumns['D'] = $calc->panjang;
                            }

                            if (in_array($satuan, ['m3', 'm2'])) {
                                $dataColumns['E'] = $calc->lebar;
                            }

                            if ($satuan === 'm3') {
                                $dataColumns['F'] = $calc->tinggi;
                            }

                            // Kolom yang selalu ditampilkan
                            $lastColumn = chr(65 + count($dataColumns));
                            $dataColumns[$lastColumn] = $calc->satuan;
                            $lastColumn = chr(65 + count($dataColumns));
                            $dataColumns[$lastColumn] = $calc->hasil;
                            // Tipe selalu di kolom I
                            $dataColumns['I'] = ucfirst($calc->calculation_type);

                            // Masukkan data ke sheet
                            foreach ($dataColumns as $col => $value) {
                                $sheet->setCellValue($col . $row, $value);
                            }
                        }

                        $row++;
                    }

                    $dataEndRow = $row - 1;

                    // Tambahkan baris total hasil
                    $sheet->setCellValue('A' . $row, '');
                    $sheet->setCellValue('B' . $row, 'TOTAL VOLUME');

                    // Tentukan kolom hasil dan kolom tipe berdasarkan jenis satuan
                    $hasilColumn = '';
                    if (in_array($satuan, ['unit', 'bh', 'ls'])) {
                        // Layout untuk unit, bh, ls
                        $sheet->mergeCells('B' . $row . ':D' . $row);
                        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

                        // Merge kolom E-H untuk hasil total
                        $sheet->mergeCells('E' . $row . ':H' . $row);

                        // Rumus: SUM(Hasil Plus) - SUM(Hasil Minus)
                        $hasilColumn = 'E';
                        $sheet->setCellValue(
                            $hasilColumn . $row,
                            "=SUMIFS(E{$dataStartRow}:E{$dataEndRow},I{$dataStartRow}:I{$dataEndRow},\"Plus\") - SUMIFS(E{$dataStartRow}:E{$dataEndRow},I{$dataStartRow}:I{$dataEndRow},\"Minus\")"
                        );

                        $sheet->setCellValue('I' . $row, $item->satuan);
                        $sheet->getStyle('A' . $row . ':I' . $row)->getFont()->setBold(true);

                        // Styling untuk merged cell hasil total
                        $sheet->getStyle('E' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                    } elseif ($satuan === 'kg') {
                        // Layout untuk kg
                        $sheet->mergeCells('B' . $row . ':F' . $row);
                        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

                        // Merge kolom G-H untuk hasil total
                        $sheet->mergeCells('G' . $row . ':H' . $row);

                        // Rumus: SUM(Hasil Plus) - SUM(Hasil Minus)
                        $hasilColumn = 'G';
                        $sheet->setCellValue(
                            $hasilColumn . $row,
                            "=SUMIFS(G{$dataStartRow}:G{$dataEndRow},I{$dataStartRow}:I{$dataEndRow},\"Plus\") - SUMIFS(G{$dataStartRow}:G{$dataEndRow},I{$dataStartRow}:I{$dataEndRow},\"Minus\")"
                        );

                        $sheet->setCellValue('I' . $row, $item->satuan);
                        $sheet->getStyle('A' . $row . ':I' . $row)->getFont()->setBold(true);

                        // Styling untuk merged cell hasil total
                        $sheet->getStyle('G' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                    } elseif ($satuan === 'ltr') {
                        // Layout untuk ltr
                        $sheet->mergeCells('B' . $row . ':E' . $row);
                        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

                        // Merge kolom F-H untuk hasil total
                        $sheet->mergeCells('F' . $row . ':H' . $row);

                        // Rumus: SUM(Hasil Plus) - SUM(Hasil Minus)
                        $hasilColumn = 'F';
                        $sheet->setCellValue(
                            $hasilColumn . $row,
                            "=SUMIFS(F{$dataStartRow}:F{$dataEndRow},I{$dataStartRow}:I{$dataEndRow},\"Plus\") - SUMIFS(F{$dataStartRow}:F{$dataEndRow},I{$dataStartRow}:I{$dataEndRow},\"Minus\")"
                        );

                        $sheet->setCellValue('I' . $row, $item->satuan);
                        $sheet->getStyle('A' . $row . ':I' . $row)->getFont()->setBold(true);

                        // Styling untuk merged cell hasil total
                        $sheet->getStyle('F' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                    } else {
                        // Layout untuk m3, m2, m (standard layout)
                        // Gabungkan sel dari B sampai sebelum kolom Hasil
                        $totalColumnIndex = 0;
                        $hasilColumnIndex = 0;

                        // Cari indeks kolom hasil
                        for ($i = 0; $i < count($headerColumns); $i++) {
                            if (array_values($headerColumns)[$i] === 'Hasil') {
                                $hasilColumnIndex = $i;
                                $totalColumnIndex = $i - 1;
                                break;
                            }
                        }

                        $totalColumnBefore = chr(64 + $totalColumnIndex);
                        $sheet->mergeCells('B' . $row . ':' . $totalColumnBefore . $row);
                        $sheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

                        // Letakkan hasil dan satuan di kolom yang tepat
                        $hasilColumn = chr(64 + $hasilColumnIndex + 1);

                        // Rumus: SUM(Hasil Plus) - SUM(Hasil Minus)
                        $sheet->setCellValue(
                            $hasilColumn . $row,
                            "=SUMIFS({$hasilColumn}{$dataStartRow}:{$hasilColumn}{$dataEndRow},I{$dataStartRow}:I{$dataEndRow},\"Plus\") - SUMIFS({$hasilColumn}{$dataStartRow}:{$hasilColumn}{$dataEndRow},I{$dataStartRow}:I{$dataEndRow},\"Minus\")"
                        );

                        $sheet->setCellValue('I' . $row, $item->satuan);
                        $sheet->getStyle('A' . $row . ':I' . $row)->getFont()->setBold(true);
                    }

                    // Simpan referensi baris total volume untuk item ini
                    $volumeRefRows[$item->id] = [
                        'row' => $row,
                        'column' => $hasilColumn
                    ];

                    $row++;

                    // Tambahkan baris kosong setelah setiap item
                    $sheet->getRowDimension($row)->setRowHeight(10);
                    $row++;

                    // Increment item number hanya jika ada perhitungan volume
                    $itemNumber++;
                }
            }

            // Increment kategori letter
            $kategoriNumber++;

            // Tambahkan baris kosong setelah setiap kategori
            if ($kategoriIndex < count($kategoriPekerjaans) - 1) {
                $sheet->getRowDimension($row)->setRowHeight(15);
                $row++;
            }
        }

        // Auto size columns
        foreach (range('A', 'I') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Format angka untuk kolom numerik
        $sheet->getStyle('C7:H' . ($row - 1))->getNumberFormat()->setFormatCode('#,##0.00');

        // Terapkan border untuk tabel-tabel perhitungan volume
        $this->applyVolumeCalculationsBorders($sheet);

        // Simpan referensi volume untuk digunakan di sheet RAB
        return $volumeRefRows;
    }

    /**
     * Menerapkan border untuk sheet Volume Calculations
     */
    private function applyVolumeCalculationsBorders($sheet)
    {
        // Define border styles
        $thinBorderStyle = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ];

        // Warna untuk header tabel
        $headerFillColor = 'D9E1F2'; // Light blue

        // Get all rows in the sheet
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

        // Pertama, hapus semua border dan warna latar belakang
        $sheet->getStyle('A1:' . $highestColumn . $highestRow)
            ->getBorders()
            ->getAllBorders()
            ->setBorderStyle(Border::BORDER_NONE);

        // Hapus semua warna latar belakang
        $sheet->getStyle('A1:' . $highestColumn . $highestRow)->getFill()
            ->setFillType(Fill::FILL_NONE);

        // Variables to track table ranges
        $tableStartRow = 0;
        $tableEndRow = 0;
        $tableType = ''; // 'unit', 'kg', 'ltr', or 'other'

        // Iterate through all rows
        for ($row = 7; $row <= $highestRow; $row++) {
            // Check if this is a header row
            $cellValue = $sheet->getCell('A' . $row)->getValue();
            $cellBValue = $sheet->getCell('B' . $row)->getValue();
            $cellEValue = $sheet->getCell('E' . $row)->getValue();
            $cellGValue = $sheet->getCell('G' . $row)->getValue();
            $cellFValue = $sheet->getCell('F' . $row)->getValue();

            // Check if this is a table header row (has 'No' in column A and 'Keterangan' in column B)
            $isTableHeader = ($cellValue === 'No' && $cellBValue === 'Keterangan');

            // Check if this is a total row (has 'TOTAL VOLUME' in column B)
            $isTotalRow = ($cellBValue === 'TOTAL VOLUME');

            if ($isTableHeader) {
                // Start of a new table
                $tableStartRow = $row;

                // Deteksi tipe tabel berdasarkan header
                if ($cellEValue === 'Hasil') {
                    $tableType = 'unit'; // unit, bh, ls
                } else if ($cellGValue === 'Hasil') {
                    $tableType = 'kg'; // kg
                } else if ($cellFValue === 'Hasil') {
                    $tableType = 'ltr'; // ltr
                } else {
                    $tableType = 'other'; // m3, m2, m
                }

                // Terapkan warna latar untuk header tabel
                $sheet->getStyle('A' . $row . ':I' . $row)->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setRGB($headerFillColor);
            } else if ($isTotalRow) {
                // End of a table
                $tableEndRow = $row;

                // Apply borders to the table - HANYA dari header sampai total volume
                if ($tableEndRow >= $tableStartRow) {
                    // Apply thin borders to all cells in the table
                    $sheet->getStyle('A' . $tableStartRow . ':I' . $tableEndRow)->applyFromArray($thinBorderStyle);

                    // Tambahkan border khusus untuk sel yang di-merge berdasarkan tipe tabel
                    if ($tableType === 'unit') {
                        // Untuk unit, bh, ls (merge E-H)
                        for ($r = $tableStartRow + 1; $r <= $tableEndRow; $r++) {
                            $sheet->getStyle('E' . $r . ':H' . $r)->applyFromArray($thinBorderStyle);
                        }
                    } else if ($tableType === 'kg') {
                        // Untuk kg (merge G-H)
                        for ($r = $tableStartRow + 1; $r <= $tableEndRow; $r++) {
                            $sheet->getStyle('G' . $r . ':H' . $r)->applyFromArray($thinBorderStyle);
                        }
                    } else if ($tableType === 'ltr') {
                        // Untuk ltr (merge F-H)
                        for ($r = $tableStartRow + 1; $r <= $tableEndRow; $r++) {
                            $sheet->getStyle('F' . $r . ':H' . $r)->applyFromArray($thinBorderStyle);
                        }
                    }

                    // Reset table tracking
                    $tableStartRow = 0;
                    $tableEndRow = 0;
                    $tableType = '';
                }
            }
        }
    }
}
