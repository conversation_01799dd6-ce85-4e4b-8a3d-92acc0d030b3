<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Project;
use App\Models\Upah;
use App\Models\Bahan;
use App\Models\Alat;
use App\Models\Ahs;
use App\Models\KategoriPekerjaan;
use App\Models\ItemPekerjaan;

class DashboardController extends Controller
{
    public function index()
    {
        // Hitung jumlah customer (user dengan role 'customer')
        $customerCount = User::where('role', 'customer')->count();

        // Hitung jumlah proyek
        $projectCount = Project::count();

        // Hitung jumlah data master
        $upahCount = Upah::count();
        $bahanCount = Bahan::count();
        $alatCount = Alat::count();
        $ahsCount = Ahs::count();

        // Hitung total kategori pekerjaan dan item pekerjaan
        $kategoriCount = KategoriPekerjaan::count();
        $itemPekerjaanCount = ItemPekerjaan::count();

        // Hitung rata-rata harga untuk setiap kategori
        $avgUpahPrice = Upah::avg('harga');
        $avgBahanPrice = Bahan::avg('harga_bahan');
        $avgAlatPrice = Alat::avg('harga_alat');

        // Ambil 5 proyek terbaru
        $latestProjects = Project::with('user')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Ambil 5 customer terbaru
        $latestCustomers = User::where('role', 'customer')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return view('admin.dashboard', [
            'customerCount' => $customerCount,
            'projectCount' => $projectCount,
            'upahCount' => $upahCount,
            'bahanCount' => $bahanCount,
            'alatCount' => $alatCount,
            'ahsCount' => $ahsCount,
            'kategoriCount' => $kategoriCount,
            'itemPekerjaanCount' => $itemPekerjaanCount,
            'avgUpahPrice' => $avgUpahPrice,
            'avgBahanPrice' => $avgBahanPrice,
            'avgAlatPrice' => $avgAlatPrice,
            'latestProjects' => $latestProjects,
            'latestCustomers' => $latestCustomers,
        ]);
    }
}
