/* Loading Animation Styles */

/* Hide loading for pagination */
html.pagination-navigation #preloader,
html.pagination-navigation #loading-overlay {
    display: none !important;
    opacity: 0 !important;
}

/* Preloader */
#preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(107, 114, 128, 0.9); /* gray-500 with opacity */
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.preloader-spinner {
    width: 80px;
    height: 80px;
    position: relative;
}

.preloader-spinner:before,
.preloader-spinner:after {
    content: "";
    border-radius: 50%;
    position: absolute;
    inset: 0;
    box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.3);
}

.preloader-spinner:before {
    border: 4px solid transparent;
    border-top-color: #3498db;
    border-bottom-color: #1abc9c;
    animation: spin 1s linear infinite;
}

.preloader-spinner:after {
    border: 4px solid transparent;
    border-left-color: #e74c3c;
    border-right-color: #f1c40f;
    animation: spin 1.5s linear infinite reverse;
}

/* Elegant Loading Animation Container */
.chart-loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f9fafb; /* gray-50 */
    backdrop-filter: none;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 280px;
    width: 100%;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(59, 130, 246, 0.15);
}

.loading-brand {
    font-weight: 600;
    font-size: 0.9rem;
    color: #2563eb; /* blue-600 */
    text-transform: uppercase;
    letter-spacing: 3px;
    margin-bottom: 1.5rem;
    text-shadow: none;
}

/* Bar Chart Animation */
.bar-chart {
    width: 200px;
    height: 100px;
    position: relative;
    margin: 0 auto;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.chart-baseline {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: rgba(59, 130, 246, 0.3);
}

/* Chart Baseline - Dark Mode */
.dark .chart-baseline {
    background-color: rgba(237, 137, 54, 0.3);
}

.chart-bar {
    width: 20px;
    margin: 0 6px;
    background: #3b82f6; /* blue-500 */
    border-radius: 4px 4px 0 0;
    position: relative;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.4);
    z-index: 2;
}

/* Chart Bar - Dark Mode */
.dark .chart-bar {
    background: #ed8936; /* Amber-500 */
    box-shadow: 0 0 10px rgba(237, 137, 54, 0.4);
}

.chart-bar:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30%;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 4px 4px 0 0;
}

.chart-bar:nth-child(1) {
    height: 40px;
    animation: barHeight 1.8s ease-in-out infinite;
    animation-delay: 0.1s;
}

.chart-bar:nth-child(2) {
    height: 70px;
    animation: barHeight 1.8s ease-in-out infinite;
    animation-delay: 0.3s;
}

.chart-bar:nth-child(3) {
    height: 50px;
    animation: barHeight 1.8s ease-in-out infinite;
    animation-delay: 0.5s;
}

.chart-bar:nth-child(4) {
    height: 80px;
    animation: barHeight 1.8s ease-in-out infinite;
    animation-delay: 0.7s;
}

.chart-bar:nth-child(5) {
    height: 60px;
    animation: barHeight 1.8s ease-in-out infinite;
    animation-delay: 0.9s;
}

@keyframes barHeight {
    0%,
    100% {
        height: 40px;
        opacity: 0.8;
    }
    50% {
        height: 70px;
        opacity: 1;
    }
}

.loading-text {
    color: #2563eb; /* blue-600 */
    font-weight: 500;
    font-size: 0.9rem;
    margin-top: 1.5rem;
    letter-spacing: 0.5px;
}

.loading-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    width: 100%;
    background: linear-gradient(
        90deg,
        rgba(59, 130, 246, 0.7),
        rgba(37, 99, 235, 0.9)
    );
    transform-origin: left;
    animation: progress 2s infinite;
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
}

/* Loading Progress - Dark Mode */
.dark .loading-progress {
    background: linear-gradient(
        90deg,
        rgba(237, 137, 54, 0.7),
        rgba(221, 107, 32, 0.9)
    );
    box-shadow: 0 0 8px rgba(237, 137, 54, 0.4);
}

@keyframes progress {
    0% {
        transform: scaleX(0);
        opacity: 0.7;
    }
    50% {
        transform: scaleX(0.5);
        opacity: 1;
    }
    100% {
        transform: scaleX(1);
        opacity: 0.7;
    }
}

/* Floating Particles */
.loading-particle {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    pointer-events: none;
}

.loading-particle:nth-child(1) {
    top: 20%;
    left: 10%;
    animation: floatingParticle 4s infinite linear;
    background-color: rgba(59, 130, 246, 0.5);
}

.loading-particle:nth-child(2) {
    top: 70%;
    left: 20%;
    animation: floatingParticle 6s infinite linear;
    background-color: rgba(16, 185, 129, 0.5);
}

.loading-particle:nth-child(3) {
    top: 40%;
    right: 10%;
    animation: floatingParticle 5s infinite linear;
    background-color: rgba(245, 158, 11, 0.5);
}

.loading-particle:nth-child(4) {
    bottom: 30%;
    right: 20%;
    animation: floatingParticle 7s infinite linear;
    background-color: rgba(236, 72, 153, 0.5);
}

@keyframes floatingParticle {
    0% {
        transform: translate(0, 0);
        opacity: 0.5;
    }
    25% {
        transform: translate(10px, 10px);
        opacity: 0.8;
    }
    50% {
        transform: translate(0, 20px);
        opacity: 0.5;
    }
    75% {
        transform: translate(-10px, 10px);
        opacity: 0.8;
    }
    100% {
        transform: translate(0, 0);
        opacity: 0.5;
    }
}

/* Toast Notification */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.toast {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
    margin-bottom: 10px;
    animation: toastIn 0.3s ease forwards;
    position: relative;
    display: flex;
    align-items: flex-start;
    overflow: hidden;
    border-left: 5px solid;
}

/* Toast - Dark Mode */
.dark .toast {
    background-color: #2c2c2c;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.toast-success {
    border-left-color: #10b981;
}

.toast-success .toast-icon {
    color: #10b981;
}

/* Toast Success Icon - Dark Mode */
.dark .toast-success .toast-icon {
    color: #10b981;
    text-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
}

.toast-error {
    border-left-color: #ef4444;
}

.toast-error .toast-icon {
    color: #ef4444;
}

/* Toast Error Icon - Dark Mode */
.dark .toast-error .toast-icon {
    color: #ef4444;
    text-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
}

.toast-info {
    border-left-color: #3b82f6;
}

.toast-info .toast-icon {
    color: #3b82f6;
}

/* Toast Info Icon - Dark Mode */
.dark .toast-info .toast-icon {
    color: #3b82f6;
    text-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
}

.toast-warning {
    border-left-color: #f59e0b;
}

.toast-warning .toast-icon {
    color: #f59e0b;
}

/* Toast Warning Icon - Dark Mode */
.dark .toast-warning .toast-icon {
    color: #f59e0b;
    text-shadow: 0 0 5px rgba(245, 158, 11, 0.5);
}

.toast-icon {
    margin-right: 12px;
    font-size: 20px;
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 16px;
    color: #1f2937;
}

/* Toast Title - Dark Mode */
.dark .toast-title {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.toast-message {
    color: #4b5563;
    font-size: 14px;
}

/* Toast Message - Dark Mode */
.dark .toast-message {
    color: #e5e5e5;
}

.toast-timer {
    font-size: 12px;
    color: #9ca3af;
    margin-left: 8px;
    min-width: 24px;
    text-align: center;
}

/* Toast Timer - Dark Mode */
.dark .toast-timer {
    color: #9ca3af;
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #1abc9c, #f1c40f, #e74c3c);
    background-size: 300% 100%;
    width: 100%;
    transform-origin: left;
}

/* Toast Progress - Dark Mode */
.dark .toast-progress {
    background: linear-gradient(90deg, #ed8936, #dd6b20, #c05621, #9c4221);
    background-size: 300% 100%;
}

.toast-progress {
    animation: gradientShift 2s ease infinite,
        toast-progress var(--duration, 5s) linear forwards;
}

.toast-success .toast-progress {
    background: linear-gradient(90deg, #10b981, #059669, #047857);
    background-size: 200% 100%;
}

/* Toast Success Progress - Dark Mode */
.dark .toast-success .toast-progress {
    background: linear-gradient(90deg, #10b981, #059669, #047857);
    background-size: 200% 100%;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.toast-error .toast-progress {
    background: linear-gradient(90deg, #ef4444, #dc2626, #b91c1c);
    background-size: 200% 100%;
}

/* Toast Error Progress - Dark Mode */
.dark .toast-error .toast-progress {
    background: linear-gradient(90deg, #ef4444, #dc2626, #b91c1c);
    background-size: 200% 100%;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

.toast-warning .toast-progress {
    background: linear-gradient(90deg, #f59e0b, #d97706, #b45309);
    background-size: 200% 100%;
}

/* Toast Warning Progress - Dark Mode */
.dark .toast-warning .toast-progress {
    background: linear-gradient(90deg, #f59e0b, #d97706, #b45309);
    background-size: 200% 100%;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

.toast-info .toast-progress {
    background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
    background-size: 200% 100%;
}

/* Toast Info Progress - Dark Mode */
.dark .toast-info .toast-progress {
    background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
    background-size: 200% 100%;
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #9ca3af;
    margin-left: 10px;
    padding: 0;
    line-height: 1;
}

.toast-close:hover {
    color: #4b5563;
}

/* Toast Close Button - Dark Mode */
.dark .toast-close {
    color: #9ca3af;
}

.dark .toast-close:hover {
    color: #ffffff;
}

.toast-close.hidden {
    display: none;
}

@keyframes toast-progress {
    from {
        transform: scaleX(1);
    }
    to {
        transform: scaleX(0);
    }
}

@keyframes toastIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes toastOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Loading Overlay */
#loading-overlay {
    background-color: rgba(
        107,
        114,
        128,
        0.9
    ) !important; /* gray-500 with opacity */
    backdrop-filter: blur(8px);
    transition: all 0.3s ease-in-out;
}

#loading-overlay .loading-content {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
}

.loading-spinner {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: #3498db;
    border-bottom-color: #3498db;
    animation: spin 1.5s linear infinite;
    box-shadow: 0 0 30px rgba(52, 152, 219, 0.4);
}

/* Loading Spinner - Dark Mode */
.dark .loading-spinner {
    border-top-color: #ed8936;
    border-bottom-color: #ed8936;
    box-shadow: 0 0 30px rgba(237, 137, 54, 0.4);
}

.dark .loading-spinner:before {
    border-left-color: #dd6b20;
    border-right-color: #dd6b20;
}

.dark .loading-spinner:after {
    border-top-color: #c05621;
    border-bottom-color: #c05621;
}

.loading-spinner:before {
    content: "";
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-left-color: #1abc9c;
    border-right-color: #1abc9c;
    animation: spin 1s linear infinite reverse;
}

.loading-spinner:after {
    content: "";
    position: absolute;
    top: 25px;
    left: 25px;
    right: 25px;
    bottom: 25px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: #e74c3c;
    border-bottom-color: #e74c3c;
    animation: spin 2s linear infinite;
}

.loading-message {
    position: absolute;
    bottom: -40px;
    left: 0;
    right: 0;
    text-align: center;
    color: #fff;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Progress Bar */
#progress-bar {
    height: 3px;
    transition: width 0.3s cubic-bezier(0.1, 0.7, 1, 0.1);
}

/* Progress Bar - Light Mode */
#progress-bar {
    background: linear-gradient(90deg, #3498db, #1abc9c, #f1c40f, #e74c3c);
    background-size: 300% 100%;
    animation: gradientShift 2s ease infinite;
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.7);
}

/* Progress Bar - Dark Mode */
.dark #progress-bar {
    background: linear-gradient(90deg, #ed8936, #dd6b20, #c05621, #9c4221);
    background-size: 300% 100%;
    animation: gradientShift 2s ease infinite;
    box-shadow: 0 0 10px rgba(237, 137, 54, 0.7);
}

/* Skeleton Loader */
.skeleton-pulse {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeletonPulse 1.5s ease-in-out infinite;
    border-radius: 8px;
}

/* Loading Button */
.loading-btn {
    position: relative;
    overflow: hidden;
}

.loading-btn:before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: btnShine 2s infinite;
    z-index: 1;
}

.loading-btn .spinner {
    animation: spinnerRotate 1s linear infinite;
    display: inline-block;
}

/* Animations */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes spinnerRotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes skeletonPulse {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}

@keyframes btnShine {
    0% {
        left: -100%;
    }
    20% {
        left: 100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes spinnerPulse {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.15);
    }
}

/* Loading particles */
.loading-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

.particle {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    animation: particleFall linear infinite;
}

@keyframes particleFall {
    0% {
        transform: translateY(-20px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}
