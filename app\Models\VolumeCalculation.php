<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VolumeCalculation extends Model
{
    protected $fillable = [
        'item_pekerjaan_id',
        'satuan',
        'keterangan',
        'jumlah',
        'panjang',
        'lebar',
        'tinggi',
        'luas',
        'volume',
        'berat_jenis',
        'liter',
        'hasil',
        'rab_id',
        'item_pekerjaan_id',
        'uraian_volume',
        'satuan_volume',
        'nilai_volume',
        'calculation_type',
    ];

    protected static function booted()
    {
        static::saved(function ($calculation) {
            $calculation->updateItemPekerjaanVolume();
        });

        static::deleted(function ($calculation) {
            $calculation->updateItemPekerjaanVolume();
        });
    }

    public function updateItemPekerjaanVolume()
    {
        $itemPekerjaan = $this->itemPekerjaan;
        if ($itemPekerjaan) {
            $plusSum = $itemPekerjaan->volumeCalculations()->where('calculation_type', 'plus')->sum('hasil');
            $minusSum = $itemPekerjaan->volumeCalculations()->where('calculation_type', 'minus')->sum('hasil');
            $newVolume = $plusSum - $minusSum;

            $itemPekerjaan->update([
                'volume' => $newVolume,
                'harga_total' => $newVolume * $itemPekerjaan->harga_satuan,
            ]);
        }
    }

    public function itemPekerjaan()
    {
        return $this->belongsTo(ItemPekerjaan::class, 'item_pekerjaan_id');
    }
}
