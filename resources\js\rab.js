document.addEventListener("DOMContentLoaded", function () {
    console.log("window.ahsBaseUrl:", window.ahsBaseUrl);

    // ===================== Variabel Global =====================
    window.currentAhsId = null;
    window.detailBahan = [];
    window.detailUpah = [];
    window.detailAlat = [];
    window.currentDetailCategory = null;
    window.currentKategoriId = null;
    window.ahspData = [];

    // Pagination for Kategori Modal
    window.allKategoriData = [];
    window.kategoriPagination = {
        currentPage: 1,
        itemsPerPage: 10,
        totalPages: 1,
        filteredData: [], // To store currently filtered/searched data
    };

    // Inisialisasi update real-time untuk RAB Kategori
    // Set interval untuk refresh data setiap 3 detik (3000ms)
    let lastKategoriData = null;
    let lastHargaSatuanData = null;
    let isUpdating = false;

    // Throttle function to prevent too many updates
    function throttledUpdate() {
        if (isUpdating) return;
        isUpdating = true;

        Promise.all([
            updateRABKategoriRealtime(),
            updateHargaSatuanRealtime(),
        ]).finally(() => {
            isUpdating = false;
        });
    }

    // Call the throttled update initially
    throttledUpdate();

    // Set interval for updates with a longer delay
    setInterval(throttledUpdate, 3000);

    function updateRABKategoriRealtime() {
        return new Promise((resolve) => {
            // Ambil project_id dari URL atau hidden input
            const urlParams = new URLSearchParams(window.location.search);
            let projectId = urlParams.get("project_id");

            if (!projectId) {
                const projectIdInput =
                    document.getElementById("projectIdInput");
                if (projectIdInput) {
                    projectId = projectIdInput.value.trim();
                }
            }

            if (!projectId) {
                console.error("Project ID tidak ditemukan untuk update RAB.");
                resolve();
                return;
            }

            // Fetch data kategori dan item-nya dari server
            fetch(
                `/kategori-pekerjaan/with-items?project_id=${encodeURIComponent(
                    projectId
                )}`
            )
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Gagal memuat data kategori dan item.");
                    }
                    return response.json();
                })
                .then((data) => {
                    // Check if data has changed
                    const currentData = JSON.stringify(data);
                    if (currentData === lastKategoriData) {
                        resolve(); // No changes, skip update
                        return;
                    }
                    lastKategoriData = currentData;

                    // Get the RAB table body
                    const tableBody = document.querySelector("table tbody");
                    if (!tableBody) {
                        console.error("RAB table body tidak ditemukan.");
                        resolve();
                        return;
                    }

                    // Clear existing rows
                    tableBody.innerHTML = "";

                    // Render updated kategori and items
                    data.forEach((kategori, catIndex) => {
                        // Create kategori row (row with letter index)
                        const kategoriRow = document.createElement("tr");
                        kategoriRow.className = "bg-blue-100 font-semibold";
                        kategoriRow.innerHTML = `
                            <td class="py-2 px-4 border">${String.fromCharCode(
                                65 + catIndex
                            )}</td>
                            <td colspan="5" class="py-2 px-4 border">${
                                kategori.nama_kategori
                            }</td>
                            <td class="py-2 px-4 border text-center">
                                <button type="button" onclick="tambahItemPekerjaan(${
                                    kategori.id
                                })" class="bg-light-accent hover:bg-light-accent/80 text-white px-2 py-1 rounded">
                                    <i class="fas fa-plus-circle"></i>
                                </button>
                            </td>
                        `;
                        tableBody.appendChild(kategoriRow);

                        // Create item rows if exists
                        if (kategori.items && kategori.items.length > 0) {
                            kategori.items.forEach((item, itemIndex) => {
                                const itemRow = document.createElement("tr");
                                itemRow.dataset.itemId = item.id;
                                itemRow.dataset.ahsId = item.ahs_id;
                                itemRow.dataset.kode = item.kode || "";
                                itemRow.dataset.sumber = item.sumber || "";

                                itemRow.innerHTML = `
                                    <td class="py-2 px-4 border pl-8">${
                                        itemIndex + 1
                                    }</td>
                                    <td class="py-2 px-4 border pl-8">${
                                        item.uraian_item
                                    }</td>
                                    <td class="py-2 px-4 border volume-cell" data-volume="${
                                        item.volume
                                    }">${parseFloat(item.volume).toFixed(
                                    2
                                )}</td>
                                    <td class="py-2 px-4 border">${
                                        item.satuan
                                    }</td>
                                    <td class="py-2 px-4 border harga-satuan" data-hargasatuan="${
                                        item.harga_satuan
                                    }">
                                        <span class="float-left">Rp.</span>
                                        <span class="float-right">${parseFloat(
                                            item.harga_satuan
                                        ).toLocaleString("id-ID", {
                                            minimumFractionDigits: 2,
                                            maximumFractionDigits: 2,
                                        })}</span>
                                    </td>
                                    <td class="py-2 px-4 border harga-total-cell" data-harga-total="${
                                        item.harga_total
                                    }">
                                        <span class="float-left">Rp.</span>
                                        <span class="float-right">${parseFloat(
                                            item.harga_total
                                        ).toLocaleString("id-ID", {
                                            minimumFractionDigits: 2,
                                            maximumFractionDigits: 2,
                                        })}</span>
                                    </td>
                                    <td class="py-2 px-4 border text-center">
                                        <button type="button" class="action-btn-item px-4 text-blue-500 hover:text-blue-700 p-1 rounded-full hover:bg-blue-200 transition-colors duration-150"
                                            data-item-id="${item.id}"
                                            data-kategori-id="${kategori.id}"
                                            data-harga-satuan="${
                                                item.harga_satuan
                                            }"
                                            data-satuan="${item.satuan}"
                                            data-ahs-id="${item.ahs_id}"
                                            onclick="handleItemContextMenu(event, this)">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </td>
                                `;
                                tableBody.appendChild(itemRow);
                            });
                        }
                    });

                    // Recalculate totals after updating the table
                    if (typeof calculateTotals === "function") {
                        calculateTotals();
                    }

                    // Pastikan tombol-tombol tetap tergembok setelah pembaruan
                    if (window.viewOnly) {
                        setupLockedButtons();
                    }

                    resolve();
                })
                .catch((error) => {
                    console.error("Error updating RAB table:", error);
                    showCustomAlert(
                        "Gagal memperbarui data RAB secara real-time."
                    );
                    resolve();
                });
        });
    }

    // Function to specifically update Harga Satuan in real-time
    function updateHargaSatuanRealtime() {
        return new Promise((resolve) => {
            // Get all item rows
            const itemRows = document.querySelectorAll("tr[data-item-id]");

            if (itemRows.length === 0) {
                console.log("Tidak ada item untuk diperbarui.");
                resolve();
                return;
            }

            // Collect all item IDs
            const itemIds = Array.from(itemRows).map(
                (row) => row.dataset.itemId
            );

            if (itemIds.length === 0) {
                resolve();
                return;
            }

            // Fetch updated harga satuan data from server
            fetch(`/item-pekerjaan/harga-satuan?ids=${itemIds.join(",")}`)
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Gagal memuat data harga satuan.");
                    }
                    return response.json();
                })
                .then((data) => {
                    // Check if data has changed
                    const currentData = JSON.stringify(data);
                    if (currentData === lastHargaSatuanData) {
                        resolve(); // No changes, skip update
                        return;
                    }
                    lastHargaSatuanData = currentData;

                    // Update harga satuan cells in the table
                    data.forEach((item) => {
                        const row = document.querySelector(
                            `tr[data-item-id="${item.id}"]`
                        );
                        if (row) {
                            // Update harga satuan cell
                            const hargaSatuanCell =
                                row.querySelector(".harga-satuan");
                            if (hargaSatuanCell) {
                                hargaSatuanCell.setAttribute(
                                    "data-hargasatuan",
                                    item.harga_satuan
                                );
                                const hargaSpan =
                                    hargaSatuanCell.querySelector(
                                        ".float-right"
                                    );
                                if (hargaSpan) {
                                    hargaSpan.textContent = parseFloat(
                                        item.harga_satuan
                                    ).toLocaleString("id-ID", {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2,
                                    });
                                }
                            }

                            // Update harga total cell if available
                            const hargaTotalCell = row.querySelector(
                                ".harga-total .float-right, .harga-total-cell .float-right"
                            );
                            if (hargaTotalCell) {
                                hargaTotalCell.textContent = parseFloat(
                                    item.harga_total
                                ).toLocaleString("id-ID", {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                });
                            }

                            // Update button data attribute
                            const actionButton =
                                row.querySelector(".action-btn-item");
                            if (actionButton) {
                                actionButton.setAttribute(
                                    "data-harga-satuan",
                                    item.harga_satuan
                                );
                            }
                        }
                    });

                    // Recalculate totals after updating
                    if (typeof calculateTotals === "function") {
                        calculateTotals();
                    }
                    resolve();
                })
                .catch((error) => {
                    console.error("Error updating harga satuan:", error);
                    resolve();
                });
        });
    }

    // Function to update grand total without reloading the page
    window.updateGrandTotal = function () {
        const rows = document.querySelectorAll("tr[data-item-id]");
        let totalHarga = 0;

        rows.forEach((row) => {
            // Coba cari sel harga total dengan class .harga-total dan .harga-total-cell
            const hargaTotalCell = row.querySelector(
                ".harga-total, .harga-total-cell"
            );
            if (hargaTotalCell) {
                // Coba ambil dari data-attribute terlebih dahulu
                if (hargaTotalCell.hasAttribute("data-harga-total")) {
                    const hargaTotal = parseFloat(
                        hargaTotalCell.getAttribute("data-harga-total") || 0
                    );
                    totalHarga += hargaTotal;
                } else {
                    // Jika tidak ada data-attribute, ambil dari text content
                    const rightSpan =
                        hargaTotalCell.querySelector(".float-right");
                    if (rightSpan) {
                        const value = rightSpan.textContent
                            .replace(/\./g, "") // Hapus titik sebagai pemisah ribuan
                            .replace(/,/g, "."); // Ganti koma dengan titik untuk desimal
                        totalHarga += parseFloat(value) || 0;
                    } else {
                        const value = hargaTotalCell.textContent
                            .replace(/[^\d,-]/g, "")
                            .replace(",", ".");
                        totalHarga += parseFloat(value) || 0;
                    }
                }
            }
        });

        const jumlahHarga = totalHarga;
        const ppn = parseFloat(document.getElementById("ppnInput").value) || 0;
        const ppnHarga = (jumlahHarga * ppn) / 100;
        const totalHargaFinal = jumlahHarga + ppnHarga;
        const dibulatkanHarga = Math.floor(totalHargaFinal / 1000) * 1000;

        // Update semua elemen HTML termasuk Terbilang
        const jumlahHargaEl = document.getElementById("jumlahHarga");
        if (jumlahHargaEl) {
            jumlahHargaEl.innerHTML = `<span class="float-left">Rp.</span><span class="float-right">${formatRupiahWithComma(
                jumlahHarga
            )}</span>`;
        }

        const ppnHargaEl = document.getElementById("ppnHarga");
        if (ppnHargaEl) {
            ppnHargaEl.innerHTML = `<span class="float-left">Rp.</span><span class="float-right">${formatRupiahWithComma(
                ppnHarga
            )}</span>`;
        }

        const totalHargaEl = document.getElementById("totalHarga");
        if (totalHargaEl) {
            totalHargaEl.innerHTML = `<span class="float-left">Rp.</span><span class="float-right">${formatRupiahWithComma(
                totalHargaFinal
            )}</span>`;
        }

        const dibulatkanHargaEl = document.getElementById("dibulatkanHarga");
        if (dibulatkanHargaEl) {
            dibulatkanHargaEl.innerHTML = `<span class="float-left">Rp.</span><span class="float-right">${formatRupiahWithComma(
                dibulatkanHarga
            )}</span>`;
        }

        // Update terbilang
        const terbilangHargaEl = document.getElementById("terbilangHarga");
        if (terbilangHargaEl && typeof angkaTerbilang === "function") {
            terbilangHargaEl.textContent =
                angkaTerbilang(dibulatkanHarga) + " Rupiah";
        }

        // Simpan total ke database jika fungsi tersedia
        if (typeof updateTotalsInDatabase === "function") {
            updateTotalsInDatabase(
                jumlahHarga,
                totalHargaFinal,
                dibulatkanHarga
            );
        }
    };

    // Helper function to format currency with comma as decimal separator
    function formatRupiahWithComma(number) {
        return new Intl.NumberFormat("id-ID", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        })
            .format(number)
            .replace(".", ",");
    }

    // ===================== Fungsi Modal Kategori =====================
    function toggleModal(modalId, show) {
        document.getElementById(modalId).classList.toggle("hidden", !show);
    }

    function openKategoriModal() {
        // Jika dalam mode view-only, tampilkan toast dan hentikan
        if (window.viewOnly) {
            if (typeof showLockedToast === "function") {
                showLockedToast();
            }
            return;
        }

        loadKategoriData();
        toggleModal("rabKategoriModal", true);
    }
    window.openKategoriModal = openKategoriModal;

    function closeKategoriModal() {
        toggleModal("rabKategoriModal", false);
        document
            .getElementById("kategori-context-menu")
            .classList.add("hidden");
    }
    window.closeKategoriModal = closeKategoriModal;

    function openInputKategoriModal() {
        document.getElementById("newKategoriInput").value = "";
        document.getElementById("inputKategoriModalTitle").innerText =
            "Tambah Kategori";
        toggleModal("inputKategoriModal", true);
        document.getElementById("btnTambahKategori").classList.remove("hidden");
        document
            .getElementById("btnSimpanPerubahanKategori")
            .classList.add("hidden");
        // Reset search when opening a sub-modal from Kategori Modal if necessary
        // document.getElementById('searchKategori').value = '';
        // loadKategoriData(); // Reload to reset view if any filter was active
    }
    window.openInputKategoriModal = openInputKategoriModal;

    function closeInputKategoriModal() {
        toggleModal("inputKategoriModal", false);
    }
    window.closeInputKategoriModal = closeInputKategoriModal;

    function loadKategoriData() {
        // Ambil project_id dari URL
        const urlParams = new URLSearchParams(window.location.search);
        let projectId = urlParams.get("project_id");

        // Jika project_id tidak ditemukan di URL, coba ambil dari elemen hidden input
        if (!projectId) {
            const projectIdInput = document.getElementById("projectIdInput");
            if (projectIdInput) {
                projectId = projectIdInput.value.trim();
            }
        }

        // Jika project_id tetap tidak ditemukan, tampilkan alert
        if (!projectId) {
            showCustomAlert(
                "Project ID tidak ditemukan. Pastikan halaman memiliki project ID."
            );
            return;
        }

        // Lakukan fetch data kategori berdasarkan project_id
        fetch(
            `/kategori-pekerjaan/all?project_id=${encodeURIComponent(
                projectId
            )}`
        )
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Gagal memuat data kategori.");
                }
                return response.json();
            })
            .then((data) => {
                const tbody = document.getElementById("kategoriTableBody");
                tbody.innerHTML = "";

                window.allKategoriData = data; // Store all data
                window.kategoriPagination.filteredData = data; // Initially, filtered data is all data
                window.kategoriPagination.totalPages = Math.ceil(
                    window.allKategoriData.length /
                        window.kategoriPagination.itemsPerPage
                );
                if (window.kategoriPagination.totalPages === 0)
                    window.kategoriPagination.totalPages = 1;
                window.kategoriPagination.currentPage = 1; // Reset to page 1

                displayKategoriDataPage(window.kategoriPagination.currentPage);
                updateKategoriPaginationControls();
            })
            .catch((error) => {
                console.error("Error loading kategori data:", error);
                showCustomAlert("Error loading kategori data.");
            });
    }
    window.loadKategoriData = loadKategoriData;

    function handleKategoriContextMenu(event, button) {
        event.preventDefault();
        event.stopPropagation();
        const contextMenu = document.getElementById("kategori-context-menu");
        if (!contextMenu) return;
        if (contextMenu.parentElement !== document.body) {
            document.body.appendChild(contextMenu);
        }
        const kategori = {
            id: button.getAttribute("data-id"),
            nama_kategori: button.getAttribute("data-nama"),
        };
        window.currentKategori = kategori;
        window.currentKategoriId = kategori.id;
        contextMenu.classList.remove("hidden");
        const menuWidth = contextMenu.offsetWidth;
        const menuHeight = contextMenu.offsetHeight;
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        let x = event.pageX;
        let y = event.pageY;
        if (x + menuWidth > viewportWidth) x = viewportWidth - menuWidth;
        if (y + menuHeight > viewportHeight) y = viewportHeight - menuHeight;
        contextMenu.style.left = `${x}px`;
        contextMenu.style.top = `${y}px`;
        contextMenu.style.zIndex = "60"; // Ensure context menu is in front
    }
    window.handleKategoriContextMenu = handleKategoriContextMenu;

    function saveKategori(url, method, body) {
        fetch(url, {
            method: method,
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: JSON.stringify(body),
        })
            .then((response) => response.json())
            .then((data) => {
                showCustomAlert(data.message, () => {
                    closeInputKategoriModal();
                    loadKategoriData(); // This will re-fetch and reset pagination
                    updateRABKategoriRealtime();
                });
            })
            .catch((error) => {
                console.error(
                    `Error ${
                        method === "POST" ? "saving" : "updating"
                    } kategori:`,
                    error
                );
                showCustomAlert(
                    `Terjadi kesalahan saat ${
                        method === "POST" ? "menyimpan" : "mengupdate"
                    } kategori.`
                );
            });
    }

    function saveNewKategori() {
        const newKategoriInput = document.getElementById("newKategoriInput");
        const projectIdInput = document.getElementById("projectIdInput");
        const btnTambahKategori = document.getElementById("btnTambahKategori");

        if (!projectIdInput) {
            showCustomAlert(
                "Project ID tidak ditemukan. Pastikan halaman memiliki project ID."
            );
            return;
        }

        // Validasi form sederhana dengan HTML5 validation
        if (!newKategoriInput.checkValidity()) {
            newKategoriInput.reportValidity();
            return;
        }

        // Menampilkan animasi loading pada tombol
        if (btnTambahKategori) {
            btnTambahKategori.innerHTML =
                '<span class="inline-flex items-center"><i class="fas fa-spinner spinner mr-2"></i><span>Memproses...</span></span>';
            btnTambahKategori.disabled = true;
            btnTambahKategori.classList.add("loading-btn");
        }

        const newKategori = newKategoriInput.value.trim();
        const projectId = projectIdInput.value.trim();

        fetch("/kategori-pekerjaan", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: JSON.stringify({
                nama_kategori: newKategori,
                project_id: projectId,
            }),
        })
            .then((response) => response.json())
            .then((data) => {
                // Simpan pesan sukses untuk ditampilkan setelah loading selesai
                const successMessage = data.message;

                // Mengembalikan tombol ke kondisi normal setelah beberapa detik
                setTimeout(() => {
                    if (btnTambahKategori) {
                        btnTambahKategori.innerHTML =
                            '<i class="fas fa-save"></i> Tambah Kategori';
                        btnTambahKategori.disabled = false;
                        btnTambahKategori.classList.remove("loading-btn");
                    }
                    // Tutup modal secara otomatis setelah loading selesai
                    closeInputKategoriModal();

                    // Update data kategori dan RAB terlebih dahulu
                    loadKategoriData();
                    updateRABKategoriRealtime();

                    // Tampilkan alert sukses setelah data diperbarui
                    setTimeout(() => {
                        showCustomAlert(successMessage);
                    }, 500); // Tunggu 500ms setelah update data
                }, 2000);
            })
            .catch((error) => {
                console.error("Error saving kategori:", error);
                showCustomAlert("Terjadi kesalahan saat menyimpan kategori.");

                // Mengembalikan tombol ke kondisi normal jika terjadi error
                if (btnTambahKategori) {
                    btnTambahKategori.innerHTML =
                        '<i class="fas fa-save"></i> Tambah Kategori';
                    btnTambahKategori.disabled = false;
                    btnTambahKategori.classList.remove("loading-btn");
                }
            });
    }
    window.saveNewKategori = saveNewKategori;

    function updateKategori() {
        const newKategoriInput = document.getElementById("newKategoriInput");
        const btnSimpanPerubahanKategori = document.getElementById(
            "btnSimpanPerubahanKategori"
        );

        // Validasi form sederhana dengan HTML5 validation
        if (!newKategoriInput.checkValidity()) {
            newKategoriInput.reportValidity();
            return;
        }

        if (!window.editKategoriId) {
            showCustomAlert("ID kategori tidak ditemukan untuk update.");
            return;
        }

        // Menampilkan animasi loading pada tombol
        if (btnSimpanPerubahanKategori) {
            btnSimpanPerubahanKategori.innerHTML =
                '<span class="inline-flex items-center"><i class="fas fa-spinner spinner mr-2"></i><span>Memproses...</span></span>';
            btnSimpanPerubahanKategori.disabled = true;
            btnSimpanPerubahanKategori.classList.add("loading-btn");
        }

        const newKategori = newKategoriInput.value.trim();

        fetch(`/kategori-pekerjaan/${window.editKategoriId}`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: JSON.stringify({
                nama_kategori: newKategori,
            }),
        })
            .then((response) => response.json())
            .then((data) => {
                // Simpan pesan sukses untuk ditampilkan setelah loading selesai
                const successMessage = data.message;

                // Mengembalikan tombol ke kondisi normal setelah beberapa detik
                setTimeout(() => {
                    if (btnSimpanPerubahanKategori) {
                        btnSimpanPerubahanKategori.innerHTML =
                            '<i class="fas fa-save"></i> Simpan Perubahan';
                        btnSimpanPerubahanKategori.disabled = false;
                        btnSimpanPerubahanKategori.classList.remove(
                            "loading-btn"
                        );
                    }
                    // Tutup modal secara otomatis setelah loading selesai
                    closeInputKategoriModal();

                    // Update data kategori dan RAB terlebih dahulu
                    loadKategoriData(); // This will re-fetch and reset pagination
                    updateRABKategoriRealtime();

                    // Tampilkan alert sukses setelah data diperbarui
                    setTimeout(() => {
                        showCustomAlert(successMessage);
                    }, 500); // Tunggu 500ms setelah update data
                }, 2000);
            })
            .catch((error) => {
                console.error("Error updating kategori:", error);
                showCustomAlert("Terjadi kesalahan saat mengupdate kategori.");

                // Mengembalikan tombol ke kondisi normal jika terjadi error
                if (btnSimpanPerubahanKategori) {
                    btnSimpanPerubahanKategori.innerHTML =
                        '<i class="fas fa-save"></i> Simpan Perubahan';
                    btnSimpanPerubahanKategori.disabled = false;
                    btnSimpanPerubahanKategori.classList.remove("loading-btn");
                }
            });
    }
    window.updateKategori = updateKategori;

    function editKategori() {
        if (window.currentKategori) {
            window.editKategoriId = window.currentKategori.id;
            document.getElementById("newKategoriInput").value =
                window.currentKategori.nama_kategori;
            document.getElementById("inputKategoriModalTitle").innerText =
                "Edit Kategori";
            toggleModal("inputKategoriModal", true);
            document
                .getElementById("btnTambahKategori")
                .classList.add("hidden");
            document
                .getElementById("btnSimpanPerubahanKategori")
                .classList.remove("hidden");
        } else {
            showCustomAlert("Tidak ada kategori yang dipilih untuk diedit.");
        }
        document
            .getElementById("kategori-context-menu")
            .classList.add("hidden");
    }
    window.editKategori = editKategori;

    function deleteKategoriContext() {
        if (window.currentKategori && window.currentKategori.id) {
            showCustomConfirm(
                "Anda yakin ingin menghapus kategori ini?",
                () => {
                    fetch(`/kategori-pekerjaan/${window.currentKategori.id}`, {
                        method: "DELETE",
                        headers: {
                            "X-CSRF-TOKEN": document
                                .querySelector('meta[name="csrf-token"]')
                                .getAttribute("content"),
                        },
                    })
                        .then((response) => response.json())
                        .then((result) => {
                            // Simpan pesan sukses
                            const successMessage = result.message;

                            // Perbarui data terlebih dahulu
                            loadKategoriData(); // This will re-fetch and reset pagination
                            updateRABKategoriRealtime();

                            // Tampilkan alert sukses setelah data diperbarui dengan sedikit delay
                            setTimeout(() => {
                                showCustomAlert(successMessage);
                            }, 500);
                        })
                        .catch((error) =>
                            console.error("Error deleting kategori:", error)
                        );
                },
                () => {
                    console.log("Hapus kategori dibatalkan.");
                }
            );
        }
        document
            .getElementById("kategori-context-menu")
            .classList.add("hidden");
    }
    window.deleteKategoriContext = deleteKategoriContext;

    // Function to initialize tooltips for kategori buttons only
    function initKategoriTooltips() {
        const kategoriButtons = document.querySelectorAll(
            ".action-btn-kategori[data-tooltip-target]"
        );

        kategoriButtons.forEach((button) => {
            // Remove existing event listeners
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            const targetId = newButton.getAttribute("data-tooltip-target");
            const tooltipEl = document.getElementById(targetId);

            if (tooltipEl) {
                // Mark as initialized to prevent default tooltip behavior
                newButton.dataset.tooltipInitialized = "true";

                newButton.addEventListener("mouseenter", () => {
                    // Move tooltip to body for better positioning
                    document.body.appendChild(tooltipEl);

                    tooltipEl.classList.remove("invisible", "opacity-0");
                    tooltipEl.classList.add("visible", "opacity-100");

                    // Position the tooltip ABOVE the button
                    const triggerRect = newButton.getBoundingClientRect();

                    tooltipEl.style.position = "fixed";
                    tooltipEl.style.zIndex = "9999";
                    // Position above the button with 10px gap
                    tooltipEl.style.top = `${
                        triggerRect.top - tooltipEl.offsetHeight - 10
                    }px`;
                    tooltipEl.style.left = `${
                        triggerRect.left +
                        triggerRect.width / 2 -
                        tooltipEl.offsetWidth / 2
                    }px`;

                    // Adjust arrow to point down
                    const tooltipArrow =
                        tooltipEl.querySelector(".tooltip-arrow");
                    if (tooltipArrow) {
                        tooltipArrow.style.left = "50%";
                        tooltipArrow.style.top = "auto";
                        tooltipArrow.style.bottom = "-4px";
                        tooltipArrow.style.transform =
                            "translateX(-50%) rotate(225deg)";
                    }
                });

                newButton.addEventListener("mouseleave", () => {
                    tooltipEl.classList.remove("visible", "opacity-100");
                    tooltipEl.classList.add("invisible", "opacity-0");

                    // Reset tooltip position and styles
                    tooltipEl.style.position = "";
                    tooltipEl.style.top = "";
                    tooltipEl.style.left = "";
                    tooltipEl.style.zIndex = "";

                    // Reset arrow position
                    const tooltipArrow =
                        tooltipEl.querySelector(".tooltip-arrow");
                    if (tooltipArrow) {
                        tooltipArrow.style.top = "";
                        tooltipArrow.style.bottom = "";
                        tooltipArrow.style.transform = "";
                    }
                });

                // Add click event to handle context menu
                newButton.addEventListener("click", (event) => {
                    // Make sure the original click handler is called
                    handleKategoriContextMenu(event, newButton);
                });
            }
        });
    }
    window.initKategoriTooltips = initKategoriTooltips;

    // ===================== Fungsi Modal Input Item Pekerjaan =====================
    function openItemModal() {
        toggleModal("inputItemModal", true);
        loadAhspData();
    }
    window.openItemModal = openItemModal;

    function closeItemModal() {
        toggleModal("inputItemModal", false);
    }
    window.closeItemModal = closeItemModal;

    function loadAhspData() {
        fetch("/ahsp/all")
            .then((response) => response.json())
            .then((data) => {
                window.ahspData = data;
                // Inisialisasi variabel paginasi jika belum ada
                if (!window.pagination) {
                    window.pagination = {
                        currentPage: 1,
                        itemsPerPage: 10,
                        totalPages: Math.ceil(data.length / 10),
                    };
                } else {
                    // Update total pages jika data berubah
                    window.pagination.totalPages = Math.ceil(data.length / 10);
                    // Reset ke halaman 1 jika total halaman berkurang
                    if (
                        window.pagination.currentPage >
                        window.pagination.totalPages
                    ) {
                        window.pagination.currentPage = 1;
                    }
                }

                displayAhspDataPage(window.pagination.currentPage);
                updatePaginationControls();
            })
            .catch((error) => {
                console.error("Error fetching AHS data:", error);
                showCustomAlert("Error fetching AHS data: " + error.message);
            });
    }
    window.loadAhspData = loadAhspData;

    // Fungsi untuk menampilkan data AHSP sesuai halaman yang dipilih
    function displayAhspDataPage(page) {
        if (!window.ahspData) return;

        const startIndex = (page - 1) * window.pagination.itemsPerPage;
        const endIndex = startIndex + window.pagination.itemsPerPage;
        const pageData = window.ahspData.slice(startIndex, endIndex);

        const tableBody = document.getElementById("ahspTableBody");
        tableBody.innerHTML = "";

        pageData.forEach((item, index) => {
            const row = `
                <tr>
                    <td class="p-2 border text-center">
                        <input type="checkbox" class="ahsp-checkbox"
                            data-id="${item.id}"
                            data-judul="${item.judul}"
                            data-satuan="${item.satuan}"
                            data-harga="${item.grand_total}"
                            data-sumber="${item.sumber}">
                    </td>
                    <td class="p-2 border">${startIndex + index + 1}</td>
                    <td class="p-2 border max-w-[300px] truncate hover:max-w-none">${
                        item.judul
                    }</td>
                    <td class="p-2 border">${item.satuan}</td>
                    <td class="py-2 px-4 border">
                        <span class="float-left">Rp.</span>
                        <span class="float-right">${parseFloat(
                            item.grand_total
                        ).toLocaleString("id-ID", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                        })}</span>
                    </td>
                    <td class="p-2 border">${item.sumber}</td>
                </tr>
            `;
            tableBody.innerHTML += row;
        });
    }

    // Fungsi untuk update controls paginasi
    function updatePaginationControls() {
        const paginationContainer = document.getElementById("ahspPagination");
        if (!paginationContainer) return;

        const { currentPage, totalPages } = window.pagination;

        paginationContainer.innerHTML = `
            <div class="flex items-center justify-between mt-4">
                <span class="text-sm text-gray-700 dark:text-gray-400">
                    Halaman <span class="font-semibold">${currentPage}</span> dari <span class="font-semibold">${totalPages}</span>
                </span>
                <div class="flex space-x-2">
                    <button 
                        ${currentPage === 1 ? "disabled" : ""}
                        onclick="navigateToPage(${currentPage - 1})"
                        class="px-3 py-1 ${
                            currentPage === 1
                                ? "bg-gray-300 cursor-not-allowed"
                                : "bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80"
                        } text-white rounded-md transition-all duration-200">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button 
                        ${currentPage === totalPages ? "disabled" : ""}
                        onclick="navigateToPage(${currentPage + 1})"
                        class="px-3 py-1 ${
                            currentPage === totalPages
                                ? "bg-gray-300 cursor-not-allowed"
                                : "bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80"
                        } text-white rounded-md transition-all duration-200">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        `;
    }

    // Fungsi untuk pindah halaman
    function navigateToPage(page) {
        if (page < 1 || page > window.pagination.totalPages) return;

        window.pagination.currentPage = page;
        displayAhspDataPage(page);
        updatePaginationControls();

        // Reset filter ketika pindah halaman
        document.getElementById("searchAhsp").value = "";
    }
    window.navigateToPage = navigateToPage;

    // Fungsi untuk menyegarkan data AHSP tanpa reload halaman
    // Fungsi ini akan dipanggil oleh ahsp.js setelah penyimpanan analisa
    function refreshAHSPData() {
        // Periksa apakah modal inputItemModal sedang terbuka
        const inputItemModal = document.getElementById("inputItemModal");
        const isInputItemModalOpen =
            inputItemModal && !inputItemModal.classList.contains("hidden");

        // Jika modal inputItemModal sedang terbuka, refresh data di dalamnya
        if (isInputItemModalOpen) {
            loadAhspData(); // Gunakan kembali fungsi loadAhspData untuk mendapatkan data terbaru
        }

        // Jika perubahan terjadi dalam konteks kategori tertentu, perbarui juga daftar item
        if (window.currentKategoriId) {
            updateItemList(window.currentKategoriId);
        }

        // Jika terdapat fungsi untuk memperbarui RAB, panggil juga
        if (typeof updateRABKategoriRealtime === "function") {
            updateRABKategoriRealtime();
        }
    }
    window.refreshAHSPData = refreshAHSPData;

    function filterAhspTable() {
        const input = document.getElementById("searchAhsp").value.toLowerCase();

        if (input === "") {
            // Jika search kosong, tampilkan paginated data
            displayAhspDataPage(window.pagination.currentPage);
            return;
        }

        // Filter dari seluruh data, bukan hanya current page
        const filteredData = window.ahspData.filter((item) => {
            const judul = item.judul.toLowerCase();
            const satuan = item.satuan.toLowerCase();
            const sumber = item.sumber.toLowerCase();

            return (
                judul.includes(input) ||
                satuan.includes(input) ||
                sumber.includes(input)
            );
        });

        // Tampilkan hasil filter tanpa paginasi
        const tableBody = document.getElementById("ahspTableBody");
        tableBody.innerHTML = "";

        if (filteredData.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="p-4 text-center">
                        Tidak ada data yang sesuai dengan pencarian.
                    </td>
                </tr>
            `;
            return;
        }

        filteredData.forEach((item, index) => {
            const row = `
                <tr>
                    <td class="p-2 border text-center">
                        <input type="checkbox" class="ahsp-checkbox"
                            data-id="${item.id}"
                            data-judul="${item.judul}"
                            data-satuan="${item.satuan}"
                            data-harga="${item.grand_total}"
                            data-sumber="${item.sumber}">
                    </td>
                    <td class="p-2 border">${index + 1}</td>
                    <td class="p-2 border max-w-[300px] truncate hover:max-w-none">${
                        item.judul
                    }</td>
                    <td class="p-2 border">${item.satuan}</td>
                    <td class="py-2 px-4 border">
                        <span class="float-left">Rp.</span>
                        <span class="float-right">${parseFloat(
                            item.grand_total
                        ).toLocaleString("id-ID", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                        })}</span>
                    </td>
                    <td class="p-2 border">${item.sumber}</td>
                </tr>
            `;
            tableBody.innerHTML += row;
        });
    }
    window.filterAhspTable = filterAhspTable;

    function saveSelectedItems() {
        const checkedItems = document.querySelectorAll(
            ".ahsp-checkbox:checked"
        );
        const btnTambahItem = document.querySelector(
            'button[onclick*="saveSelectedItems"]'
        );

        // Menampilkan animasi loading pada tombol sejak awal
        if (btnTambahItem) {
            btnTambahItem.innerHTML =
                '<span class="inline-flex items-center"><i class="fas fa-spinner spinner mr-2"></i><span>Memproses...</span></span>';
            btnTambahItem.disabled = true;
            btnTambahItem.classList.add("loading-btn");
        }

        // Validasi sederhana: periksa apakah ada item yang dipilih
        if (checkedItems.length === 0) {
            showCustomAlert("Pilih minimal satu item!");

            // Kembalikan tombol ke kondisi normal jika validasi gagal
            if (btnTambahItem) {
                btnTambahItem.innerHTML =
                    '<i class="fas fa-plus-circle"></i> Tambah';
                btnTambahItem.disabled = false;
                btnTambahItem.classList.remove("loading-btn");
            }
            return;
        }

        const selectedItems = Array.from(checkedItems).map((checkbox) => ({
            ahs_id: checkbox.getAttribute("data-id"),
            uraian_item: checkbox.getAttribute("data-judul"),
            satuan: checkbox.getAttribute("data-satuan"),
            harga_satuan: parseFloat(checkbox.getAttribute("data-harga")),
            kategori_pekerjaan_id: window.currentKategoriId,
            volume: 0,
            harga_total: 0,
        }));

        console.log("Data yang akan dikirim:", selectedItems);

        fetch("/item-pekerjaan", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: JSON.stringify({ items: selectedItems }),
        })
            .then((response) => {
                if (!response.ok) {
                    return response.text().then((text) => {
                        throw new Error(text);
                    });
                }
                return response.json();
            })
            .then((result) => {
                // Simpan pesan sukses untuk ditampilkan setelah loading selesai
                const successMessage = result.message;

                // Mengembalikan tombol ke kondisi normal dan tutup modal setelah beberapa detik
                setTimeout(() => {
                    if (btnTambahItem) {
                        btnTambahItem.innerHTML =
                            '<i class="fas fa-plus-circle"></i> Tambah';
                        btnTambahItem.disabled = false;
                        btnTambahItem.classList.remove("loading-btn");
                    }
                    // Tutup modal secara otomatis
                    closeItemModal();

                    // Update data item dan RAB terlebih dahulu
                    updateItemList(window.currentKategoriId);
                    updateRABKategoriRealtime();

                    // Tampilkan alert sukses setelah data diperbarui
                    setTimeout(() => {
                        showCustomAlert(successMessage);
                    }, 500); // Tunggu 500ms setelah update data
                }, 2000);
            })
            .catch((error) => {
                console.error("Error saving items:", error);
                showCustomAlert(
                    "Terjadi kesalahan saat menyimpan item pekerjaan."
                );

                // Mengembalikan tombol ke kondisi normal jika terjadi error
                if (btnTambahItem) {
                    btnTambahItem.innerHTML =
                        '<i class="fas fa-plus-circle"></i> Tambah';
                    btnTambahItem.disabled = false;
                    btnTambahItem.classList.remove("loading-btn");
                }
            });
    }
    window.saveSelectedItems = saveSelectedItems;

    function updateItemList(kategoriId) {
        console.log("Memperbarui daftar item untuk kategori ID: " + kategoriId);
    }
    window.updateItemList = updateItemList;

    // ===================== Tombol AHSP (Simpan Data Baru/Update) =====================
    // Fungsi computeGrandTotal telah dihapus karena tidak digunakan

    // ===================== Fungsi Tambah Item Pekerjaan =====================
    function tambahItemPekerjaan(kategoriId) {
        // Jika dalam mode view-only, tampilkan toast dan hentikan
        if (window.viewOnly) {
            showLockedToast();
            return;
        }

        window.currentKategoriId = kategoriId;
        openItemModal();
    }
    window.tambahItemPekerjaan = tambahItemPekerjaan;

    // Fungsi showCustomConfirm telah dipindahkan ke file confirm.js
    // dan tersedia secara global sebagai window.showCustomConfirm

    function deleteItem(itemId) {
        showCustomConfirm(
            "Anda yakin ingin menghapus item ini?",
            () => {
                fetch(`/item-pekerjaan/${itemId}`, {
                    method: "DELETE",
                    headers: {
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                })
                    .then((response) => response.json())
                    .then((result) => {
                        showCustomAlert(result.message, () => {
                            updateRABKategoriRealtime();
                        });
                    })
                    .catch((error) => {
                        console.error("Error deleting item:", error);
                        showCustomAlert(
                            "Terjadi kesalahan saat menghapus item."
                        );
                    });
            },
            () => {
                console.log("Hapus dibatalkan.");
            }
        );
    }
    window.deleteItem = deleteItem;

    function handleItemContextMenu(event, button) {
        event.preventDefault();
        event.stopPropagation();

        // Jika dalam mode view-only, tampilkan toast dan hentikan
        if (window.viewOnly) {
            showLockedToast();
            return;
        }

        const contextMenu = document.getElementById("item-context-menu");
        if (!contextMenu) return;
        if (contextMenu.parentElement !== document.body) {
            document.body.appendChild(contextMenu);
        }
        const itemId = button.getAttribute("data-item-id");
        const kategoriId = button.getAttribute("data-kategori-id");
        const hargaSatuan = button.getAttribute("data-harga-satuan");
        const satuan = button.getAttribute("data-satuan");
        const ahsId = button.getAttribute("data-ahs-id");

        window.currentItem = { itemId, kategoriId, hargaSatuan, satuan, ahsId };

        contextMenu.classList.remove("hidden");
        const menuWidth = contextMenu.offsetWidth;
        const menuHeight = contextMenu.offsetHeight;
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        let x = event.pageX;
        let y = event.pageY;
        if (x + menuWidth > viewportWidth) x = viewportWidth - menuWidth;
        if (y + menuHeight > viewportHeight) y = viewportHeight - menuHeight;
        contextMenu.style.left = `${x}px`;
        contextMenu.style.top = `${y}px`;
        contextMenu.style.zIndex = "60"; // Ensure context menu is in front
    }
    window.handleItemContextMenu = handleItemContextMenu;

    document.addEventListener("click", () => {
        const contextMenu = document.getElementById("item-context-menu");
        if (contextMenu) contextMenu.classList.add("hidden");
    });

    function showCustomAlert(message, callback) {
        // Gunakan window.showInfoToast yang dibuat di notifications.js
        window.showInfoToast(message, null, callback, 3000);
    }

    document.addEventListener("click", () => {
        const contextMenu = document.getElementById("kategori-context-menu");
        if (contextMenu) contextMenu.classList.add("hidden");
    });

    // Fungsi untuk menangani tombol terkunci (mode view-only)
    function setupLockedButtons() {
        // Hanya jalankan jika dalam mode view-only
        if (!window.viewOnly) return;

        // Temukan semua tombol action-btn-item dan tambahkan kelas locked-btn
        const actionButtons = document.querySelectorAll(".action-btn-item");
        actionButtons.forEach((btn) => {
            btn.classList.add("locked-btn");
            // Hapus event handler asli dan ganti dengan handler untuk mode view-only
            const originalOnClick = btn.getAttribute("onclick");
            btn.removeAttribute("onclick");
            btn.addEventListener("click", function (e) {
                e.preventDefault();
                e.stopPropagation();
                showLockedToast();
            });
        });

        // Temukan semua tombol tambah item pekerjaan dan tambahkan kelas locked-btn
        const addItemButtons = document.querySelectorAll(
            'button[onclick*="tambahItemPekerjaan"]'
        );
        addItemButtons.forEach((btn) => {
            btn.classList.add("locked-btn");
            // Hapus event handler asli dan ganti dengan handler untuk mode view-only
            const originalOnClick = btn.getAttribute("onclick");
            btn.removeAttribute("onclick");
            btn.addEventListener("click", function (e) {
                e.preventDefault();
                e.stopPropagation();
                showLockedToast();
            });
        });
    }

    // Fungsi untuk menampilkan toast saat tombol terkunci diklik
    function showLockedToast() {
        if (window.showInfoToast) {
            window.showInfoToast(
                "Anda hanya memiliki akses untuk melihat. Tidak dapat melakukan perubahan pada proyek ini. Silahkan hubungi pemilik proyek untuk mendapatkan akses sebagai Editor.",
                null,
                null,
                3000
            );
        } else {
            console.warn(
                "Anda hanya memiliki akses untuk melihat. Tidak dapat melakukan perubahan pada proyek ini. Silahkan hubungi pemilik proyek untuk mendapatkan akses sebagai Editor."
            );
        }
    }
    // Pastikan fungsi tersedia secara global
    window.showLockedToast = showLockedToast;

    // Jalankan setup tombol terkunci saat dokumen siap
    document.addEventListener("DOMContentLoaded", function () {
        setupLockedButtons();
    });

    // Window variable untuk menandai apakah user dalam mode view-only
    window.viewOnly = document.getElementById("viewOnly")
        ? document.getElementById("viewOnly").value === "1"
        : false;
    console.log("View Only Mode:", window.viewOnly);

    // Event listener untuk tombol kategori tergembok
    const lockedKategoriBtn = document.getElementById("locked-kategori-btn");
    if (lockedKategoriBtn) {
        lockedKategoriBtn.addEventListener("click", function (e) {
            e.preventDefault();
            e.stopPropagation();
            showLockedToast();
        });
    }

    // Fungsi untuk memfilter tabel kategori berdasarkan input pencarian
    function filterKategoriTable() {
        const searchInput = document.getElementById("searchKategori");
        const filter = searchInput.value.toUpperCase();

        if (filter === "") {
            window.kategoriPagination.filteredData = window.allKategoriData;
        } else {
            window.kategoriPagination.filteredData =
                window.allKategoriData.filter((kategori) => {
                    const kategoriNama = kategori.nama_kategori.toUpperCase();
                    return kategoriNama.includes(filter);
                });
        }

        window.kategoriPagination.totalPages = Math.ceil(
            window.kategoriPagination.filteredData.length /
                window.kategoriPagination.itemsPerPage
        );
        if (window.kategoriPagination.totalPages === 0)
            window.kategoriPagination.totalPages = 1;
        window.kategoriPagination.currentPage = 1; // Reset to page 1 on new search

        displayKategoriDataPage(window.kategoriPagination.currentPage);
        updateKategoriPaginationControls();
    }
    // Make sure it's globally available if it was before, or attached to the event listener correctly
    // If searchKategoriInput event listener was set up elsewhere, ensure this function is callable
    window.filterKategoriTable = filterKategoriTable;

    // Function to display a page of Kategori data
    function displayKategoriDataPage(page) {
        const tbody = document.getElementById("kategoriTableBody");
        if (!tbody) return;
        tbody.innerHTML = "";

        const startIndex = (page - 1) * window.kategoriPagination.itemsPerPage;
        const endIndex = startIndex + window.kategoriPagination.itemsPerPage;
        const pageData = window.kategoriPagination.filteredData.slice(
            startIndex,
            endIndex
        );

        pageData.forEach((kategori, index) => {
            const actualIndex = startIndex + index; // Correct index if data is filtered/paginated
            const row = document.createElement("tr");
            row.innerHTML = `
                <td class="px-4 py-2 border">${actualIndex + 1}</td>
                <td class="p-2 border px-4 py-4 max-w-[350px] truncate hover:max-w-none">${
                    kategori.nama_kategori
                }</td>
                <td class="px-4 py-2 border text-center">
                    <button type="button" class="action-btn-kategori px-4 text-blue-500 hover:text-blue-900 p-1 rounded-full hover:bg-blue-200"
                        data-id="${kategori.id}" data-nama="${
                kategori.nama_kategori
            }"
                        onclick="handleKategoriContextMenu(event, this)">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Function to update Kategori pagination controls
    function updateKategoriPaginationControls() {
        const paginationContainer = document.getElementById(
            "kategoriPaginationControls"
        );
        if (!paginationContainer) return;

        const { currentPage, totalPages } = window.kategoriPagination;

        if (totalPages <= 1) {
            paginationContainer.innerHTML = ""; // No controls if only one page or no data
            return;
        }

        let paginationHTML = `
            <div class="w-full flex items-center justify-between mt-4">
                <span class="text-sm text-gray-700 dark:text-gray-400 mr-3">
                    Halaman <span class="font-semibold">${currentPage}</span> dari <span class="font-semibold">${totalPages}</span>
                </span>
                <div class="flex space-x-2">
                    <button 
                        ${currentPage === 1 ? "disabled" : ""}
                        onclick="navigateToKategoriPage(${currentPage - 1})"
                        class="px-3 py-1 ${
                            currentPage === 1
                                ? "bg-gray-300 dark:bg-gray-700 cursor-not-allowed"
                                : "bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80"
                        } text-white rounded-md transition-all duration-200">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button 
                        ${currentPage === totalPages ? "disabled" : ""}
                        onclick="navigateToKategoriPage(${currentPage + 1})"
                        class="px-3 py-1 ${
                            currentPage === totalPages
                                ? "bg-gray-300 dark:bg-gray-700 cursor-not-allowed"
                                : "bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80"
                        } text-white rounded-md transition-all duration-200">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        `;
        paginationContainer.innerHTML = paginationHTML;
    }

    // Function to navigate Kategori pages
    function navigateToKategoriPage(page) {
        if (page < 1 || page > window.kategoriPagination.totalPages) return;
        window.kategoriPagination.currentPage = page;
        displayKategoriDataPage(page);
        updateKategoriPaginationControls();
    }
    window.navigateToKategoriPage = navigateToKategoriPage;

    // Ensure the filter function is attached to the search input
    const searchKategoriInput = document.getElementById("searchKategori");
    if (searchKategoriInput) {
        searchKategoriInput.addEventListener("keyup", filterKategoriTable);
    }

    // Fungsi untuk membuka modal edit judul
    function openEditJudulModal() {
        const itemId = window.currentItem.itemId;
        const contextMenu = document.getElementById("item-context-menu");
        if (contextMenu) contextMenu.classList.add("hidden");

        // Ambil data uraian item dari kolom uraian di baris terkait
        const itemRow = document.querySelector(`tr[data-item-id="${itemId}"]`);
        if (itemRow) {
            const uraianCell = itemRow.querySelector("td:nth-child(2)");
            if (uraianCell) {
                const uraianText = uraianCell.textContent.trim();

                // Set nilai input pada modal
                document.getElementById("editItemId").value = itemId;
                document.getElementById("editUraianInput").value = uraianText;

                // Tampilkan modal
                const editJudulModal =
                    document.getElementById("editJudulModal");
                if (editJudulModal) {
                    editJudulModal.classList.remove("hidden");
                }
            }
        }
    }
    window.openEditJudulModal = openEditJudulModal;

    // Fungsi untuk menutup modal edit judul
    function closeEditJudulModal() {
        const editJudulModal = document.getElementById("editJudulModal");
        if (editJudulModal) {
            editJudulModal.classList.add("hidden");
        }
    }
    window.closeEditJudulModal = closeEditJudulModal;

    // Fungsi untuk menyimpan perubahan judul
    function saveEditedJudul() {
        // Ambil nilai dari input
        const itemId = document.getElementById("editItemId").value;
        const newUraian = document
            .getElementById("editUraianInput")
            .value.trim();

        if (!newUraian) {
            showCustomAlert("Uraian pekerjaan tidak boleh kosong.");
            return;
        }

        // Kirim data ke server
        fetch(`/item-pekerjaan/${itemId}/update-uraian`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: JSON.stringify({
                uraian_item: newUraian,
            }),
        })
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Gagal memperbarui uraian pekerjaan.");
                }
                return response.json();
            })
            .then((data) => {
                // Tutup modal
                closeEditJudulModal();

                // Perbarui tampilan tabel
                updateRABKategoriRealtime();

                // Tampilkan pesan sukses
                showCustomAlert(
                    data.message || "Uraian pekerjaan berhasil diperbarui."
                );
            })
            .catch((error) => {
                console.error("Error updating uraian:", error);
                showCustomAlert(
                    "Terjadi kesalahan saat memperbarui uraian pekerjaan."
                );
            });
    }
    window.saveEditedJudul = saveEditedJudul;
});
