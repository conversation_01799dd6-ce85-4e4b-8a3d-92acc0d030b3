<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ahs_bahan', function (Blueprint $table) {
            $table->unsignedBigInteger('ahs_id');
            $table->unsignedBigInteger('bahan_id');
            $table->decimal('koefisien', 10, 4)->default(0);
            $table->timestamps();

            $table->primary(['ahs_id', 'bahan_id']);
            $table->foreign('ahs_id')->references('id')->on('ahs')->onDelete('cascade');
            $table->foreign('bahan_id')->references('id')->on('bahans')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ahs_bahan');
    }
};
