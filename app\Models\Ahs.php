<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class Ahs extends Model
{
    use HasFactory;

    protected $fillable = [
        'kode',
        'judul',
        'satuan',
        'overhead',
        'grand_total',
        'sumber',
        'user_id',
        'creator_role',
        'created_by'
    ];

    protected $casts = [
        'grand_total' => 'float',
    ];

    public function details()
    {
        return $this->hasMany(AhspDetail::class, 'ahs_id');
    }
    public function recalcGrandTotal()
    {
        $subtotal = $this->details()->sum('harga_satuan');
        $overhead = $subtotal * ($this->overhead / 100);
        $this->grand_total = $subtotal + $overhead;
        $this->save();
    }

    // Relasi ke ItemPekerjaans
    public function itemPekerjaans()
    {
        return $this->hasMany(ItemPekerjaan::class, 'ahs_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relasi ke pembuat
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Relasi untuk download Excel dengan rumus
    public function upah()
    {
        return $this->belongsToMany(Upah::class, 'ahs_upah', 'ahs_id', 'upah_id')
            ->withPivot('koefisien');
    }

    public function bahan()
    {
        return $this->belongsToMany(Bahan::class, 'ahs_bahan', 'ahs_id', 'bahan_id')
            ->withPivot('koefisien');
    }

    public function alat()
    {
        return $this->belongsToMany(Alat::class, 'ahs_alat', 'ahs_id', 'alat_id')
            ->withPivot('koefisien');
    }

    // Accessor untuk harga satuan
    public function getHargaSatuanAttribute()
    {
        return $this->grand_total;
    }
}
