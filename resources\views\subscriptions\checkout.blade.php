@extends('layouts.app')

@section('scripts')
    @vite('resources/js/payment-gateway.js')
    <script>
        // Ensure PaymentGateway is loaded
        function ensurePaymentGatewayLoaded() {
            return new Promise((resolve, reject) => {
                if (window.PaymentGateway) {
                    resolve(window.PaymentGateway);
                } else {
                    // If not loaded yet, wait a bit and check again
                    let attempts = 0;
                    const checkInterval = setInterval(() => {
                        attempts++;
                        if (window.PaymentGateway) {
                            clearInterval(checkInterval);
                            resolve(window.PaymentGateway);
                        } else if (attempts >= 10) {
                            clearInterval(checkInterval);
                            reject(new Error('Failed to load PaymentGateway'));
                        }
                    }, 200);
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            const planId = {{ $plan->id }};
            const subscriptionId = null;
            const autoRenewCheckbox = document.getElementById('auto_renew');
            const termsCheckbox = document.getElementById('terms');
            const payButton = document.getElementById('pay-button');
            const paymentLoading = document.getElementById('payment-loading');
            const durationOptions = document.querySelectorAll('.duration-option');
            const packagePriceElement = document.getElementById('package-price');
            const totalPriceElement = document.getElementById('total-price');
            const useDurationCheckbox = document.getElementById('use_duration');
            const durationContainer = document.getElementById('duration-container');

            // Variabel untuk menyimpan durasi yang dipilih
            let selectedDurationId = null;
            let originalPlanPrice = {{ $plan->price }};
            let originalFormattedPrice = '{{ $plan->formatted_price }}';

            // Inisialisasi durasi yang dipilih
            if (durationOptions.length > 0 && useDurationCheckbox.checked) {
                const firstOption = durationOptions[0];
                selectedDurationId = firstOption.dataset.durationId;

                // Update harga sesuai dengan durasi yang dipilih
                if (firstOption.dataset.formattedPrice) {
                    packagePriceElement.textContent = firstOption.dataset.formattedPrice;
                    totalPriceElement.textContent = firstOption.dataset.formattedPrice;
                }
            } else {
                // Jika checkbox unchecked, tampilkan harga untuk 1 bulan
                packagePriceElement.textContent = originalFormattedPrice;
                totalPriceElement.textContent = originalFormattedPrice;
                selectedDurationId = null;
            }

            // Event listener untuk checkbox durasi
            useDurationCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // Tampilkan durasi container jika checkbox dicentang
                    durationContainer.classList.remove('hidden');
                    
                    // Set durasi pertama sebagai pilihan
                    if (durationOptions.length > 0) {
                        const firstOption = durationOptions[0];
                        selectedDurationId = firstOption.dataset.durationId;
                        
                        // Update harga sesuai dengan durasi yang dipilih
                        if (firstOption.dataset.formattedPrice) {
                            packagePriceElement.textContent = firstOption.dataset.formattedPrice;
                            totalPriceElement.textContent = firstOption.dataset.formattedPrice;
                        }
                        
                        // Select the first option visually
                        durationOptions.forEach(opt => {
                            opt.classList.remove('selected', 'border-blue-500');
                            opt.querySelector('.duration-radio div').classList.add('hidden');
                        });
                        firstOption.classList.add('selected', 'border-blue-500');
                        firstOption.querySelector('.duration-radio div').classList.remove('hidden');
                    }
                } else {
                    // Sembunyikan durasi container jika checkbox tidak dicentang
                    durationContainer.classList.add('hidden');
                    
                    // Reset durasi dan tampilkan harga untuk 1 bulan
                    selectedDurationId = null;
                    packagePriceElement.textContent = originalFormattedPrice;
                    totalPriceElement.textContent = originalFormattedPrice;
                }
            });

            // Tambahkan event listener untuk setiap opsi durasi
            durationOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Hanya berfungsi jika durasi diaktifkan
                    if (!useDurationCheckbox.checked) return;
                    
                    // Hapus kelas selected dari semua opsi
                    durationOptions.forEach(opt => {
                        opt.classList.remove('selected', 'border-blue-500');
                        opt.querySelector('.duration-radio div').classList.add('hidden');
                    });

                    // Tambahkan kelas selected ke opsi yang dipilih
                    this.classList.add('selected', 'border-blue-500');
                    this.querySelector('.duration-radio div').classList.remove('hidden');

                    // Simpan ID durasi yang dipilih
                    selectedDurationId = this.dataset.durationId;

                    // Update harga sesuai dengan durasi yang dipilih
                    if (this.dataset.formattedPrice) {
                        packagePriceElement.textContent = this.dataset.formattedPrice;
                        totalPriceElement.textContent = this.dataset.formattedPrice;
                    }
                });
            });

            // Fetch payment gateway configuration
            console.log('Fetching payment gateway configuration...');
            fetch('{{ route('payment-gateway.config') }}')
                .then(response => {
                    console.log('Payment gateway config response status:', response.status);
                    if (!response.ok) {
                        return response.text().then(text => {
                            console.error('Error response text:', text);
                            throw new Error('Failed to get payment gateway config: ' + response.status);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Payment gateway config data:', data);
                    if (data.success) {
                        // Ensure PaymentGateway is loaded before initializing
                        return ensurePaymentGatewayLoaded().then(PaymentGateway => {
                            // Initialize payment gateway
                            const gateway = PaymentGateway.init(data.gateway, data.config);
                            return {
                                gateway,
                                data
                            };
                        });
                    } else {
                        throw new Error('Failed to get payment gateway configuration: ' + data.message);
                    }
                })
                .then(({
                    gateway,
                    data
                }) => {

                    // Add event listener to pay button
                    payButton.addEventListener('click', function() {
                        // Validate terms checkbox
                        if (!termsCheckbox.checked) {
                            alert('Anda harus menyetujui Syarat dan Ketentuan untuk melanjutkan.');
                            return;
                        }

                        // Show loading indicator
                        payButton.disabled = true;
                        paymentLoading.classList.remove('hidden');

                        // Create subscription
                        console.log('Creating subscription...');
                        createSubscription()
                            .then(subscription => {
                                console.log('Subscription created:', subscription);
                                if (data.gateway === 'xendit') {
                                    console.log('Using Xendit gateway');
                                    // Create Xendit invoice
                                    return createXenditInvoice(subscription.id);
                                } else {
                                    console.error('Unknown gateway:', data.gateway);
                                    throw new Error('Unknown payment gateway: ' + data.gateway);
                                }
                            })
                            .then(paymentData => {
                                // Hide loading indicator
                                payButton.disabled = false;
                                paymentLoading.classList.add('hidden');

                                if (paymentData.email_sent) {
                                    console.log('Payment link sent to email:', paymentData);

                                    // Show success message
                                    const successMessage = document.createElement('div');
                                    successMessage.className =
                                        'bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded dark:bg-green-900/30 dark:text-green-400 mt-4';
                                    successMessage.innerHTML = `
                                        <p class="font-medium">Pembayaran sedang diproses!</p>
                                        <p class="mt-2">Link pembayaran telah dikirim ke email Anda. Silakan cek email Anda untuk melanjutkan pembayaran. Jika tidak menerima email dalam beberapa menit, periksa folder spam atau klik tombol di bawah.</p>
                                    `;

                                    // Add button to open payment link directly
                                    const buttonContainer = document.createElement('div');
                                    buttonContainer.className = 'mt-4';
                                    buttonContainer.innerHTML = `
                                        <button type="button" id="open-payment-link" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                                            <i class="fas fa-external-link-alt mr-1"></i> Lanjutkan Pembayaran
                                        </button>
                                    `;

                                    successMessage.appendChild(buttonContainer);

                                    // Insert message after payment button
                                    const paymentContainer = document.getElementById(
                                        'payment-loading').parentNode;
                                    paymentContainer.appendChild(successMessage);

                                    // Add event listener to open payment link
                                    document.getElementById('open-payment-link').addEventListener(
                                        'click',
                                        function() {
                                            console.log('Opening payment link, gateway:', data
                                                .gateway);
                                            console.log('Payment data:', paymentData);

                                            if (data.gateway === 'xendit' && paymentData
                                                .invoice_url) {
                                                console.log('Opening Xendit URL:', paymentData
                                                    .invoice_url);
                                                window.open(paymentData.invoice_url, '_blank');
                                            } else {
                                                console.error('No valid payment URL found');
                                                alert(
                                                    'Tidak dapat menemukan link pembayaran yang valid. Silakan cek email Anda atau hubungi administrator.'
                                                );
                                            }
                                        });

                                    // Change pay button text
                                    payButton.innerHTML =
                                        '<i class="fas fa-check-circle mr-1"></i> Pembayaran Diproses';
                                    payButton.disabled = true;
                                    payButton.className =
                                        'w-full bg-green-500 text-white font-medium py-3 px-4 rounded-lg cursor-not-allowed';

                                } else if (data.gateway === 'xendit' && paymentData.invoice_url) {
                                    console.log('Processing Xendit payment with URL:', paymentData
                                        .invoice_url);
                                    // Pay with Xendit
                                    gateway.pay(paymentData.invoice_url);
                                } else {
                                    console.error('Invalid payment data:', paymentData);
                                    alert(
                                        'Terjadi kesalahan saat memproses pembayaran. Data pembayaran tidak valid.'
                                    );
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                alert(
                                    'Terjadi kesalahan saat memproses pembayaran. Silakan coba lagi.'
                                );

                                // Hide loading indicator
                                payButton.disabled = false;
                                paymentLoading.classList.add('hidden');
                            });
                    });
                })

                .catch(error => {
                    console.error('Error:', error);
                    alert('Terjadi kesalahan saat memuat konfigurasi pembayaran. Silakan coba lagi.');
                });

            // Create subscription
            function createSubscription() {
                console.log('Creating subscription for plan ID:', {{ $plan->id }});

                // Prepare request data
                const requestData = {
                    auto_renew: autoRenewCheckbox.checked,
                    use_duration: useDurationCheckbox.checked
                };

                // Add duration_id if selected and duration checkbox is checked
                if (selectedDurationId && useDurationCheckbox.checked) {
                    requestData.duration_id = selectedDurationId;
                    console.log('Using duration ID:', selectedDurationId);
                }

                return fetch('{{ route('subscriptions.purchase', $plan->id) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(requestData),
                        credentials: 'same-origin'
                    })
                    .then(response => {
                        if (!response.ok) {
                            // Get the response text for better error debugging
                            return response.text().then(text => {
                                console.error('Error response:', text);
                                throw new Error('Failed to create subscription: ' + response.status);
                            });
                        }
                        return response.json();
                    });
            }

            // Create Xendit invoice
            function createXenditInvoice(subscriptionId) {
                console.log('Creating Xendit invoice for subscription ID:', subscriptionId);
                return fetch('{{ route('payment-gateway.xendit.invoice') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            subscription_id: subscriptionId
                        }),
                        credentials: 'same-origin'
                    })
                    .then(response => {
                        if (!response.ok) {
                            // Get the response text for better error debugging
                            return response.text().then(text => {
                                console.error('Error response:', text);
                                throw new Error('Failed to create Xendit invoice: ' + response.status);
                            });
                        }
                        return response.json();
                    });
            }
        });
    </script>
@endsection

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2">Checkout</h1>
                <p class="text-gray-600 dark:text-gray-400">Selesaikan pembayaran untuk paket langganan Anda</p>
            </div>
            <div>
                <a href="{{ route('subscriptions.index') }}"
                    class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                    <i class="fas fa-arrow-left mr-1"></i> Kembali
                </a>
            </div>
        </div>

        @if (session('error'))
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400"
                role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Detail Paket -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
                    <div class="bg-blue-600 dark:bg-blue-700 p-4">
                        <h2 class="text-white text-lg font-semibold">Detail Paket</h2>
                    </div>

                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h3 class="text-xl font-bold text-light-text dark:text-dark-text">{{ $plan->name }}</h3>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">{{ $plan->description }}</p>
                            </div>
                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                {{ $plan->formatted_price }}
                            </div>
                        </div>

                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mb-6">
                            <h4 class="font-semibold text-light-text dark:text-dark-text mb-3">Rincian Paket:</h4>

                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Durasi:</span>
                                    <span
                                        class="font-medium text-light-text dark:text-dark-text">{{ $plan->duration_in_months }}
                                        bulan</span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Jumlah Proyek:</span>
                                    <span class="font-medium text-light-text dark:text-dark-text">Maksimal
                                        {{ $plan->project_limit }} proyek</span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Jumlah Pengguna:</span>
                                    <span class="font-medium text-light-text dark:text-dark-text">Maksimal
                                        {{ $plan->max_users }} pengguna</span>
                                </div>
                            </div>
                        </div>

                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <h4 class="font-semibold text-light-text dark:text-dark-text mb-3">Fitur:</h4>

                            <ul class="space-y-2">
                                @if ($plan->features)
                                    @foreach ($plan->features as $feature)
                                        <li class="flex items-start">
                                            <i class="fas fa-check text-green-500 dark:text-green-400 mt-1 mr-2"></i>
                                            <span class="text-gray-600 dark:text-gray-400">{{ $feature }}</span>
                                        </li>
                                    @endforeach
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Pembayaran -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
                    <div class="bg-blue-600 dark:bg-blue-700 p-4">
                        <h2 class="text-white text-lg font-semibold">Pembayaran</h2>
                    </div>

                    <div class="p-6">
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-semibold text-light-text dark:text-dark-text">Pilih Durasi Berlangganan</h3>
                                <div class="flex items-center">
                                    <input type="checkbox" id="use_duration" name="use_duration"
                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600" checked>
                                    <label for="use_duration" class="ml-2 text-sm text-light-text dark:text-dark-text">Gunakan durasi berlangganan</label>
                                </div>
                            </div>
                            
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">Jika tidak dicentang, Anda hanya akan membayar untuk 1 bulan.</p>

                            @if ($plan->activeDurations && $plan->activeDurations->count() > 0)
                                <div id="duration-container" class="space-y-3 mb-4">
                                    @foreach ($plan->activeDurations as $index => $duration)
                                        <div class="duration-option bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg border-2 border-transparent hover:border-blue-500 cursor-pointer transition-all duration-200 @if ($index === 0) selected border-blue-500 @endif"
                                            data-duration-id="{{ $duration->id }}"
                                            data-duration-months="{{ $duration->duration_months }}"
                                            data-total-price="{{ $duration->calculateTotalPrice() }}"
                                            data-formatted-price="{{ $duration->formatted_total_price }}">
                                            <div class="flex justify-between items-center">
                                                <div class="flex items-center">
                                                    <div
                                                        class="w-5 h-5 rounded-full border-2 border-blue-500 flex items-center justify-center mr-3 duration-radio">
                                                        <div
                                                            class="w-3 h-3 rounded-full bg-blue-500 @if ($index !== 0) hidden @endif">
                                                        </div>
                                                    </div>
                                                    <span
                                                        class="font-medium text-light-text dark:text-dark-text">{{ $duration->duration_months }}
                                                        bulan</span>
                                                </div>
                                                <div class="text-right">
                                                    <span
                                                        class="text-blue-600 dark:text-blue-400 font-medium">{{ $duration->formatted_total_price }}</span>
                                                    @if ($duration->promo_type === 'discount')
                                                        <div class="text-xs text-green-600 dark:text-green-400">Diskon
                                                            {{ $duration->promo_value }}%</div>
                                                    @elseif($duration->promo_type === 'free_months')
                                                        <div class="text-xs text-green-600 dark:text-green-400">Gratis
                                                            {{ $duration->promo_value }} bulan</div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg mb-4">
                                    <div class="flex justify-between mb-2">
                                        <span class="text-gray-600 dark:text-gray-400">Paket {{ $plan->name }}
                                            ({{ $plan->duration_in_months }} bulan)</span>
                                        <span
                                            class="font-medium text-light-text dark:text-dark-text">{{ $plan->formatted_price }}</span>
                                    </div>
                                </div>
                            @endif

                            <h3 class="font-semibold text-light-text dark:text-dark-text mb-3">Ringkasan Pembayaran</h3>

                            <div class="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                                <div class="flex justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">Paket {{ $plan->name }}</span>
                                    <span id="package-price"
                                        class="font-medium text-light-text dark:text-dark-text">{{ $plan->formatted_price }}</span>
                                </div>

                                @if ($hasActiveSubscription || $isOnTrial)
                                    <div
                                        class="mt-2 mb-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800">
                                        <div class="flex items-start">
                                            <i class="fas fa-info-circle text-blue-500 dark:text-blue-400 mt-1 mr-2"></i>
                                            <div>
                                                <p class="text-sm text-blue-700 dark:text-blue-300">
                                                    @if ($hasActiveSubscription)
                                                        Langganan baru akan dimulai setelah langganan saat ini berakhir pada
                                                        <strong>{{ $formattedStartDate }}</strong>.
                                                    @elseif($isOnTrial)
                                                        Langganan akan dimulai setelah masa uji coba berakhir pada
                                                        <strong>{{ $formattedStartDate }}</strong>.
                                                    @endif
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <div class="border-t border-gray-200 dark:border-gray-700 my-2 pt-2">
                                    <div class="flex justify-between font-bold">
                                        <span class="text-light-text dark:text-dark-text">Total</span>
                                        <span id="total-price"
                                            class="text-blue-600 dark:text-blue-400">{{ $plan->formatted_price }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="auto_renew" name="auto_renew"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                <label for="auto_renew" class="ml-2 text-light-text dark:text-dark-text">Perpanjangan
                                    Otomatis</label>
                            </div>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Langganan Anda akan diperpanjang secara
                                otomatis pada akhir periode.</p>
                        </div>

                        <div class="mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="terms" name="terms"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                                    required>
                                <label for="terms" class="ml-2 text-light-text dark:text-dark-text">Saya setuju dengan <a
                                        href="{{ route('visitor.terms') }}" class="text-blue-600 dark:text-blue-400 hover:underline">Syarat dan
                                        Ketentuan</a></label>
                            </div>
                        </div>

                        <button id="pay-button" type="button"
                            class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-150"
                            data-plan-id="{{ $plan->id }}" data-plan-price="{{ $plan->price }}">
                            <i class="fas fa-credit-card mr-1"></i> Bayar Sekarang
                        </button>

                        <div id="payment-loading" class="hidden mt-4 text-center">
                            <div
                                class="inline-block animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500">
                            </div>
                            <p class="mt-2 text-gray-600 dark:text-gray-400">Memproses pembayaran...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
