<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Alat extends Model
{
    use HasFactory;

    protected $table = 'alats';

    protected $fillable = [
        'uraian_alat',
        'satuan',
        'harga_alat',
        'sumber',
        'alamat'
    ];

    public function details()
    {
        return $this->morphMany(\App\Models\AhspDetail::class, 'itemable');
    }

    // Accessor untuk download Excel dengan rumus
    public function getNamaAlatAttribute()
    {
        return $this->uraian_alat;
    }

    public function getHargaSatuanAttribute()
    {
        return $this->harga_alat;
    }
}
