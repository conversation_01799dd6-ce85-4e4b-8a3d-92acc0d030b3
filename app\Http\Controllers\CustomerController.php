<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class CustomerController extends Controller
{
    /**
     * Display a listing of the customers.
     */
    public function index(Request $request)
    {
        $search = $request->input('search');
        $query = User::where('role', 'customer');

        // Apply search filter if provided
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Order by newest first
        $query->orderBy('created_at', 'desc');

        // Paginate results
        $customers = $query->paginate(10);

        // Append search to pagination links
        if ($search) {
            $customers->appends(['search' => $search]);
        }

        // Get project counts for each customer
        $projectCounts = [];
        foreach ($customers as $customer) {
            $projectCounts[$customer->id] = Project::where('user_id', $customer->id)->count();
        }

        return view('admin.customers.index', compact('customers', 'projectCounts'));
    }

    /**
     * Store a newly created customer in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'address' => 'nullable|string|max:500',
            'phone' => 'nullable|string|max:20',
        ], [
            'email.unique' => 'Email sudah terdaftar. Silakan gunakan email lain.'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first() ?: 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        $customer = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'customer',
            'address' => $request->address,
            'phone' => $request->phone,
        ]);

        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Customer berhasil ditambahkan',
                'data' => $customer
            ]);
        }

        return redirect()->route('customers.index')->with('success', 'Customer berhasil ditambahkan');
    }

    /**
     * Update the specified customer in storage.
     */
    public function update(Request $request, $id)
    {
        $customer = User::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($customer->id),
            ],
            'password' => 'nullable|string|min:8',
            'address' => 'nullable|string|max:500',
            'phone' => 'nullable|string|max:20',
        ], [
            'email.unique' => 'Email sudah terdaftar. Silakan gunakan email lain.'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first() ?: 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        $customer->name = $request->name;
        $customer->email = $request->email;
        $customer->address = $request->address;
        $customer->phone = $request->phone;

        if ($request->filled('password')) {
            $customer->password = Hash::make($request->password);
        }

        $customer->save();

        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Customer berhasil diperbarui',
                'data' => $customer
            ]);
        }

        return redirect()->route('customers.index')->with('success', 'Customer berhasil diperbarui');
    }

    /**
     * Remove the specified customer from storage.
     */
    public function destroy($id)
    {
        $customer = User::findOrFail($id);

        // Check if customer has projects
        $projectCount = Project::where('user_id', $id)->count();

        if ($projectCount > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Customer tidak dapat dihapus karena memiliki ' . $projectCount . ' proyek'
            ], 422);
        }

        $customer->delete();

        return response()->json([
            'success' => true,
            'message' => 'Customer berhasil dihapus'
        ]);
    }

    /**
     * Get customer details
     */
    public function show($id)
    {
        $customer = User::findOrFail($id);
        $projects = Project::where('user_id', $id)->get();

        return view('admin.customers.show', compact('customer', 'projects'));
    }
}
