<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class EmailVerificationRequest extends FormRequest
{
    /**
     * User instance
     *
     * @var \App\Models\User
     */
    protected $verifiableUser;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Cari user berdasarkan ID
        $user = User::find($this->route('id'));

        if (!$user) {
            return false;
        }

        // Verifikasi hash email
        if (!hash_equals(
            (string) $this->route('hash'),
            sha1($user->email)
        )) {
            return false;
        }

        // Simpan user di properti request untuk digunakan di fulfill
        $this->verifiableUser = $user;

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [];
    }

    /**
     * Fulfill the email verification request.
     */
    public function fulfill()
    {
        // Gunakan user dari properti yang sudah disimpan
        if (!$this->verifiableUser->hasVerifiedEmail()) {
            $this->verifiableUser->email_verified_at = now();
            $this->verifiableUser->save();

            event(new Verified($this->verifiableUser));
        }

        // Auto login user setelah verifikasi
        Auth::login($this->verifiableUser);
    }
}
