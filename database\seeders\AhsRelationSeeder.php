<?php

namespace Database\Seeders;


use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Ahs;
use App\Models\Upah;
use App\Models\Bahan;
use App\Models\Alat;

class AhsRelationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ambil semua AHS yang ada
        $ahsList = Ahs::all();

        // Ambil beberapa upah, bahan, dan alat untuk direlasikan
        $upahList = Upah::take(5)->get();
        $bahanList = Bahan::take(5)->get();
        $alatList = Alat::take(5)->get();

        foreach ($ahsList as $ahs) {
            // Tambahkan upah ke AHS
            foreach ($upahList as $upah) {
                DB::table('ahs_upah')->insert([
                    'ahs_id' => $ahs->id,
                    'upah_id' => $upah->id,
                    'koefisien' => rand(1, 10) / 10,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Tambahkan bahan ke AHS
            foreach ($bahanList as $bahan) {
                DB::table('ahs_bahan')->insert([
                    'ahs_id' => $ahs->id,
                    'bahan_id' => $bahan->id,
                    'koefisien' => rand(1, 10) / 10,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Tambahkan alat ke AHS
            foreach ($alatList as $alat) {
                DB::table('ahs_alat')->insert([
                    'ahs_id' => $ahs->id,
                    'alat_id' => $alat->id,
                    'koefisien' => rand(1, 10) / 10,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
