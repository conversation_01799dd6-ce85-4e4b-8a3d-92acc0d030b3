<?php

namespace App\Http\Controllers;

use App\Models\Bahan;
use Illuminate\Http\Request;
use App\Http\Controllers\ResourceController;
use App\Traits\AjaxPagination;

class BahanController extends Controller
{
    use ResourceController, AjaxPagination;

    /**
     * Display a listing of the resources.
     */
    public function index(Request $request)
    {
        // Get search query
        $search = $request->input('search');

        // Create query
        $query = Bahan::query();

        // Apply search if provided
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('uraian_bahan', 'like', "%{$search}%")
                    ->orWhere('satuan', 'like', "%{$search}%")
                    ->orWhere('sumber', 'like', "%{$search}%");
            });
        }

        // Order by newest first
        $query->orderBy('created_at', 'desc');

        // Paginate results
        $bahans = $query->paginate(15);

        // Append search to pagination links
        if ($search) {
            $bahans->appends(['search' => $search]);
        }

        // Handle AJAX pagination
        return $this->handleAjaxPagination($request, $bahans, 'bahan.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        return $this->storeResource($request, Bahan::class, 'Bahan berhasil disimpan!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        return $this->updateResource($request, $id, Bahan::class, 'Bahan berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        return $this->destroyResource(request(), $id, Bahan::class, 'Bahan berhasil dihapus!');
    }

    /**
     * Get bahan data for AJAX requests
     */
    public function getData()
    {
        return $this->getResourceData(Bahan::class);
    }
}
