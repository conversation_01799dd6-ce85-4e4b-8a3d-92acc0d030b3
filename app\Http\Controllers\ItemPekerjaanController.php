<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ItemPekerjaan;

class ItemPekerjaanController extends Controller
{
    public function store(Request $request)
    {
        // Ambil payload JSON
        $data = $request->json()->all();

        // Periksa apakah data dibungkus dalam key "items"
        if (isset($data['items']) && is_array($data['items'])) {
            $items = [];
            foreach ($data['items'] as $itemData) {
                $validated = validator($itemData, [
                    'ahs_id'               => 'required|integer|exists:ahs,id',
                    'kategori_pekerjaan_id' => 'required|integer|exists:kategori_pekerjaans,id',
                    'uraian_item'          => 'required|string',
                    'satuan'               => 'required|string',
                    'harga_satuan'         => 'required|numeric',
                    'volume'               => 'nullable|numeric',
                    'harga_total'          => 'nullable|numeric',
                ])->validate();

                $validated['volume'] = $validated['volume'] ?? 0;
                $validated['harga_total'] = $validated['harga_total'] ?? 0;

                $items[] = ItemPekerjaan::create($validated);
            }

            return response()->json([
                'success' => true,
                'message' => 'Item pekerjaan berhasil disimpan',
                'data'    => $items,
            ]);
        } else {
            // Jika payload tidak dibungkus dengan key "items", proses sebagai objek tunggal
            $validated = $request->validate([
                'ahs_id'               => 'required|integer|exists:ahs,id',
                'kategori_pekerjaan_id' => 'required|integer|exists:kategori_pekerjaans,id',
                'uraian_item'          => 'required|string',
                'satuan'               => 'required|string',
                'harga_satuan'         => 'required|numeric',
                'volume'               => 'nullable|numeric',
                'harga_total'          => 'nullable|numeric',
            ]);

            $validated['volume'] = $validated['volume'] ?? 0;
            $validated['harga_total'] = $validated['harga_total'] ?? 0;

            $item = ItemPekerjaan::create($validated);

            return response()->json([
                'success' => true,
                'message' => 'Item pekerjaan berhasil disimpan',
                'data'    => $item,
            ]);
        }
    }

    public function updateVolume(Request $request, $id)
    {
        $validated = $request->validate([
            'volume'      => 'required|numeric',
            'harga_total' => 'required|numeric',
        ]);

        $item = ItemPekerjaan::findOrFail($id);

        // Simpan nilai lama untuk logging
        $oldVolume = $item->volume;
        $oldHargaTotal = $item->harga_total;

        // Update item
        $item->update($validated);

        // Log perubahan
        \Illuminate\Support\Facades\Log::info('Item pekerjaan volume updated', [
            'item_id' => $id,
            'old_volume' => $oldVolume,
            'new_volume' => $item->volume,
            'old_harga_total' => $oldHargaTotal,
            'new_harga_total' => $item->harga_total
        ]);

        return response()->json(['message' => 'Data volume dan harga total berhasil diperbarui.']);
    }

    public function destroy($id)
    {
        $item = ItemPekerjaan::findOrFail($id);
        $item->delete();
        return response()->json([
            'message' => 'Item pekerjaan berhasil dihapus'
        ]);
    }

    // Endpoint untuk mendapatkan data item pekerjaan tunggal berdasarkan ID
    public function show($id)
    {
        $item = ItemPekerjaan::findOrFail($id);
        return response()->json($item);
    }

    // Endpoint untuk mendapatkan data harga satuan dari beberapa item
    public function getHargaSatuan(Request $request)
    {
        $ids = $request->query('ids');

        if (!$ids) {
            return response()->json(['error' => 'Tidak ada ID yang diberikan'], 400);
        }

        // Split IDs string into array
        $idArray = explode(',', $ids);

        // Get items with the requested IDs
        $items = ItemPekerjaan::whereIn('id', $idArray)
            ->select('id', 'harga_satuan', 'harga_total')
            ->get();

        return response()->json($items);
    }

    // Endpoint untuk memperbarui uraian item pekerjaan
    public function updateUraian(Request $request, $id)
    {
        $validated = $request->validate([
            'uraian_item' => 'required|string',
        ]);

        $item = ItemPekerjaan::findOrFail($id);

        // Simpan nilai lama untuk logging
        $oldUraian = $item->uraian_item;

        // Update hanya uraian item, tidak mempengaruhi data AHSP
        $item->update([
            'uraian_item' => $validated['uraian_item']
        ]);

        // Log perubahan
        \Illuminate\Support\Facades\Log::info('Item pekerjaan uraian updated', [
            'item_id' => $id,
            'old_uraian' => $oldUraian,
            'new_uraian' => $item->uraian_item
        ]);

        return response()->json(['message' => 'Uraian pekerjaan berhasil diperbarui.']);
    }
}
