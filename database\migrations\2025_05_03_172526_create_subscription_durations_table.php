<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_durations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subscription_plan_id')->constrained()->onDelete('cascade');
            $table->integer('duration_months')->comment('Durasi dalam bulan');
            $table->enum('promo_type', ['discount', 'free_months'])->comment('Jenis promosi: diskon atau bulan gratis');
            $table->integer('promo_value')->comment('Nilai promosi: persentase diskon atau jumlah bulan gratis');
            $table->text('description')->nullable()->comment('Deskripsi durasi');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0)->comment('Urutan tampilan');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_durations');
    }
};
