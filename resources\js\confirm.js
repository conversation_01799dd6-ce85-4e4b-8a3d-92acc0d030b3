/**
 * Sistem Konfirmasi untuk RAB Estimator
 * Menggantikan confirm standar dengan dialog konfirmasi yang lebih menarik
 * dan mendukung dark mode
 */

/**
 * Buat dan tampilkan dialog konfirmasi
 * @param {string} message - <PERSON><PERSON> konfirmasi yang akan ditampilkan
 * @param {Function} onConfirm - Fungsi yang akan dipanggil jika user menekan tombol Ya
 * @param {Function} onCancel - Fungsi yang akan dipanggil jika user menekan tombol Tidak
 */
window.showCustomConfirm = function (message, onConfirm, onCancel) {
    // Buat container untuk dialog konfirmasi
    const confirmContainer = document.createElement("div");
    confirmContainer.className =
        "confirm-container fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";

    // Buat HTML untuk dialog konfirmasi yang mirip dengan deleteConfirmModal
    confirmContainer.innerHTML = `
        <div class="confirm-box bg-white dark:bg-dark-card rounded-lg shadow-lg p-6 max-w-sm mx-auto">
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-5">Konfirmasi</h3>
                <p class="confirm-message text-gray-700 dark:text-gray-300 mb-6">${message}</p>
                <div class="flex justify-center space-x-4">
                    <button id="btnCancelConfirm" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                        Tidak
                    </button>
                    <button id="btnActionConfirm" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                        Ya, Hapus
                    </button>
                </div>
            </div>
        </div>
    `;

    // Tambahkan dialog ke body
    document.body.appendChild(confirmContainer);

    // Tambahkan event listener untuk tombol konfirmasi
    document
        .getElementById("btnActionConfirm")
        .addEventListener("click", function () {
            document.body.removeChild(confirmContainer);
            if (onConfirm) onConfirm();
        });

    // Tambahkan event listener untuk tombol batal
    document
        .getElementById("btnCancelConfirm")
        .addEventListener("click", function () {
            document.body.removeChild(confirmContainer);
            if (onCancel) onCancel();
        });
};

// Override fungsi confirm standar dengan showCustomConfirm
window.originalConfirm = window.confirm;
window.confirm = function (message) {
    return new Promise((resolve) => {
        window.showCustomConfirm(
            message,
            () => resolve(true),
            () => resolve(false)
        );
    });
};
