@extends('layouts.app')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2"><PERSON><PERSON><PERSON></h1>
                <p class="text-gray-600 dark:text-gray-400"><PERSON><PERSON><PERSON> paket langganan yang tersedia untuk pelanggan</p>
            </div>
            <div>
                <a href="{{ route('admin.subscription-plans.create') }}"
                    class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                    <i class="fas fa-plus-circle mr-1"></i> Tambah Paket
                </a>
            </div>
        </div>

        @if (session('error'))
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400"
                role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        @if (session('success'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded dark:bg-green-900/30 dark:text-green-400"
                role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif

        <!-- Daftar Paket Langganan -->
        <div
            class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 transform hover:shadow-lg dark:bg-dark-card">
            <div class="bg-blue-600 dark:bg-blue-700 p-4">
                <h2 class="text-white text-lg font-semibold">Daftar Paket Langganan</h2>
            </div>

            <div class="p-6">
                @if ($plans->isEmpty())
                    <div class="text-center py-8">
                        <i class="fas fa-box-open text-gray-400 dark:text-gray-600 text-5xl mb-4"></i>
                        <p class="text-gray-600 dark:text-gray-400">Belum ada paket langganan yang tersedia.</p>
                        <a href="{{ route('admin.subscription-plans.create') }}"
                            class="mt-4 inline-block bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                            <i class="fas fa-plus mr-1"></i> Tambah Paket
                        </a>
                    </div>
                @else
                    <div class="overflow-x-auto">
                        <table class="text-sm min-w-full bg-white dark:bg-dark-card border dark:border-gray-700">
                            <thead class="bg-blue-200 dark:bg-gray-800 sticky top-0 z-10">
                                <tr>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Nama</th>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Slug</th>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Harga</th>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Durasi</th>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Status</th>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach ($plans as $plan)
                                    <tr>
                                        <td class="py-2 px-4 border dark:border-gray-700">
                                            <div class="flex items-center">
                                                <div class="font-medium text-light-text dark:text-dark-text">
                                                    {{ $plan->name }}</div>
                                                @if ($plan->is_featured)
                                                    <span
                                                        class="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900/30 dark:text-blue-400">Unggulan</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="py-2 px-4 border dark:border-gray-700">
                                            {{ $plan->slug }}
                                        </td>
                                        <td class="py-2 px-4 border dark:border-gray-700">
                                            {{ $plan->formatted_price }}
                                        </td>
                                        <td class="py-2 px-4 border dark:border-gray-700">
                                            {{ $plan->duration_in_months }} bulan
                                        </td>
                                        <td class="py-2 px-4 border dark:border-gray-700">
                                            @if ($plan->is_active)
                                                <span
                                                    class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900/30 dark:text-green-400">Aktif</span>
                                            @else
                                                <span
                                                    class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-red-900/30 dark:text-red-400">Tidak
                                                    Aktif</span>
                                            @endif
                                        </td>
                                        <td class="py-2 px-4 border dark:border-gray-700 text-center">
                                            <button type="button"
                                                class="action-btn-item px-4 text-light-accent dark:text-dark-accent p-1 rounded-full hover:bg-light-accent/10 dark:hover:bg-dark-accent/10 transition-all duration-200 transform hover:scale-105"
                                                data-plan-id="{{ $plan->id }}">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>

                                            <form id="delete-form-{{ $plan->id }}"
                                                action="{{ route('admin.subscription-plans.destroy', $plan->id) }}"
                                                method="POST" class="hidden">
                                                @csrf
                                                @method('DELETE')
                                            </form>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Context Menu -->
    <div id="context-menu"
        class="hidden absolute bg-white dark:bg-dark-bg-secondary shadow-lg rounded-md py-2 w-32 border border-gray-200 dark:border-dark-accent/20 z-50 text-sm">
        <a href="#" id="edit-link"
            class="w-full px-4 py-2 text-left text-light-accent dark:text-dark-accent hover:bg-light-accent/10 dark:hover:bg-dark-accent/10 hover:text-light-accent/80 dark:hover:text-dark-accent/80 text-sm flex items-center transition-all duration-200 edit-btn">
            <i class="fas fa-edit mr-2 w-4 h-4"></i> Edit
        </a>
        <button id="delete-btn"
            class="w-full px-4 py-2 text-left hover:bg-red-50 dark:hover:bg-red-900/10 text-sm flex items-center text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 transition-all duration-200 delete-btn">
            <i class="fas fa-trash-alt mr-2 w-4 h-4"></i> Hapus
        </button>
    </div>


@endsection

@section('scripts')
    <script>
        let planIdToDelete = null;
        let contextMenu = document.getElementById('context-menu');
        let actionButtons = document.querySelectorAll('.action-btn-item');

        // Setup context menu for each action button
        actionButtons.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Hide any visible context menu first
                contextMenu.classList.add('hidden');

                // Get plan ID from the button's data attribute
                planIdToDelete = this.getAttribute('data-plan-id');

                // Update edit link
                document.getElementById('edit-link').href = "{{ url('admin/subscription-plans') }}/" +
                    planIdToDelete + "/edit";

                // Setup delete button
                document.getElementById('delete-btn').onclick = function() {
                    confirmDelete();
                };

                // Position the context menu
                const rect = this.getBoundingClientRect();
                contextMenu.style.top = `${rect.bottom + window.scrollY}px`;
                contextMenu.style.left = `${rect.left + window.scrollX - 100}px`;

                // Show the context menu
                contextMenu.classList.remove('hidden');
            });
        });

        // Hide context menu when clicking elsewhere
        document.addEventListener('click', function() {
            contextMenu.classList.add('hidden');
        });

        function confirmDelete() {
            // Sembunyikan context menu
            contextMenu.classList.add('hidden');

            // Gunakan showCustomConfirm untuk tampilan yang konsisten dengan aplikasi
            window.showCustomConfirm(
                'Apakah Anda yakin ingin menghapus paket langganan ini? Tindakan ini tidak dapat dibatalkan.',
                function() {
                    // Fungsi yang dijalankan jika user menekan tombol "Ya, Hapus"
                    if (planIdToDelete) {
                        document.getElementById('delete-form-' + planIdToDelete).submit();
                    }
                },
                function() {
                    // Fungsi yang dijalankan jika user menekan tombol "Tidak"
                    console.log('Hapus dibatalkan.');
                }
            );
        }
    </script>
@endsection
