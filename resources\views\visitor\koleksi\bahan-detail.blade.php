@extends('visitor.koleksi.layout')

@section('collection-title', 'Detail Bahan')
@section('collection-description', 'Informasi detail tentang bahan material konstruksi.')

@section('collection-content')
    <div class="mb-8">
        <a href="{{ route('visitor.koleksi.bahan') }}" class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:underline">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Ke<PERSON><PERSON> ke Daftar Bahan
        </a>
    </div>

    <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden">
        <div class="bg-gradient-to-r from-light-navbar via-[#0C4A7A] to-[#083D66] dark:from-blue-900 dark:to-blue-950 text-white p-6">
            <h2 class="text-2xl font-bold">{{ $bahan->uraian_bahan }}</h2>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Informasi Bahan</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Uraian Bahan</p>
                            <p class="text-lg font-medium text-gray-900 dark:text-white">{{ $bahan->uraian_bahan }}</p>
                        </div>
                        
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Satuan</p>
                            <p class="text-lg font-medium text-gray-900 dark:text-white">{{ $bahan->satuan }}</p>
                        </div>
                        
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Harga</p>
                            <p class="text-lg font-medium text-gray-900 dark:text-white">Rp {{ number_format($bahan->harga_bahan, 2) }}</p>
                        </div>
                        
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Sumber</p>
                            <p class="text-lg font-medium text-gray-900 dark:text-white">{{ $bahan->sumber ?: 'Tidak ada' }}</p>
                        </div>
                        
                        @if($bahan->alamat)
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Alamat</p>
                            <p class="text-lg font-medium text-gray-900 dark:text-white">{{ $bahan->alamat }}</p>
                        </div>
                        @endif
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Keterangan</h3>
                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                        <p class="text-gray-600 dark:text-gray-300">
                            Bahan material ini digunakan dalam perhitungan Analisa Harga Satuan Pekerjaan (AHSP) untuk menentukan biaya material dalam suatu pekerjaan konstruksi.
                        </p>
                        <p class="text-gray-600 dark:text-gray-300 mt-4">
                            Harga bahan dapat bervariasi tergantung pada lokasi, waktu, dan kondisi pasar. Pastikan untuk memverifikasi harga terbaru sebelum menggunakannya dalam perhitungan RAB.
                        </p>
                    </div>
                    
                    <div class="mt-6">
                        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-2">Informasi Tambahan</h4>
                        <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-2">
                            <li>Harga belum termasuk biaya pengiriman</li>
                            <li>Harga belum termasuk pajak</li>
                            <li>Satuan {{ $bahan->satuan }} mengacu pada standar pengukuran material konstruksi</li>
                            <li>Data terakhir diperbarui: {{ $bahan->updated_at->format('d F Y') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
