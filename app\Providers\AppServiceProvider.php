<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\Bahan;
use App\Models\Upah;
use App\Models\Alat;
use App\Models\AhspDetail;
use App\Models\Ahs;
use App\Models\ItemPekerjaan;
use App\Models\VolumeCalculation;
use App\Observers\BahanObserver;
use App\Observers\UpahObserver;
use App\Observers\AlatObserver;
use App\Observers\AhspDetailObserver;
use App\Observers\AhsObserver;
use App\Observers\ItemPekerjaanObserver;
use App\Observers\VolumeCalculationObserver;
use Illuminate\Support\Facades\View;
use App\Models\Project;
use Illuminate\Support\Facades\Auth;
use App\Observers\ProjectObserver;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Blade;
use App\Helpers\GravatarHelper;

class AppServiceProvider extends ServiceProvider
{
    public function boot()
    {
        Bahan::observe(BahanObserver::class);
        Upah::observe(UpahObserver::class);
        Alat::observe(AlatObserver::class);
        AhspDetail::observe(AhspDetailObserver::class);
        Ahs::observe(AhsObserver::class);
        ItemPekerjaan::observe(ItemPekerjaanObserver::class);
        VolumeCalculation::observe(VolumeCalculationObserver::class);
        Project::observe(ProjectObserver::class);

        // Register Blade directive for Gravatar
        Blade::directive('gravatar', function ($expression) {
            return "<?php echo App\\Helpers\\GravatarHelper::url($expression); ?>";
        });

        // Use custom pagination view
        Paginator::defaultView('vendor.pagination.custom');
        Paginator::defaultSimpleView('vendor.pagination.custom');

        // Register the koleksi pagination view for visitor pages
        \Illuminate\Support\Facades\View::addNamespace('koleksi', resource_path('views/components'));

        // Add a custom method to use the koleksi pagination view
        \Illuminate\Pagination\Paginator::useBootstrapFive();

        View::composer('*', function ($view) {
            if (Auth::check()) {
                $view->with('projects', Project::where('user_id', Auth::id())->get());
            } else {
                $view->with('projects', collect());
            }
        });
    }
}
