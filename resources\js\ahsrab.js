document.addEventListener("DOMContentLoaded", function () {
    // Fungsi untuk update harga satuan pada halaman RAB tanpa reload
    window.updateHargaSatuanOnRAB = function (
        ahsId,
        hargaSatuan,
        formattedHargaSatuan
    ) {
        // Cari semua baris dengan ahs_id yang sama
        const rows = document.querySelectorAll(`tr[data-ahs_id="${ahsId}"]`);

        if (rows.length > 0) {
            rows.forEach((row) => {
                // Update harga satuan pada baris
                const hargaSatuanCell = row.querySelector(".harga-satuan");
                if (hargaSatuanCell) {
                    // Pastikan format HTML tetap sama dengan format aslinya
                    // Cari span float-right untuk update nilai harga
                    const hargaSpan =
                        hargaSatuanCell.querySelector(".float-right");
                    if (hargaSpan) {
                        // Format harga dengan format Indonesia (koma sebagai desimal)
                        const formattedValue = formattedHargaSatuan.replace(
                            ".",
                            ","
                        );
                        hargaSpan.textContent = formattedValue;
                    } else {
                        // Jika span tidak ditemukan, buat struktur HTML yang benar
                        hargaSatuanCell.innerHTML = `
                            <span class="float-left">Rp.</span>
                            <span class="float-right">${formattedHargaSatuan.replace(
                                ".",
                                ","
                            )}</span>
                        `;
                    }

                    // Update data attribute
                    hargaSatuanCell.setAttribute(
                        "data-hargasatuan",
                        hargaSatuan
                    );

                    // Ambil volume dari baris
                    const volumeCell = row.querySelector(".volume");
                    if (volumeCell) {
                        const volume = parseFloat(
                            volumeCell.getAttribute("data-volume") || "0"
                        );

                        // Hitung harga total baru
                        const hargaTotal = volume * hargaSatuan;

                        // Format harga total dengan format Indonesia (koma sebagai desimal)
                        const formattedHargaTotal = new Intl.NumberFormat(
                            "id-ID",
                            {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                            }
                        )
                            .format(hargaTotal)
                            .replace(".", ",");

                        // Update harga total pada baris
                        const hargaTotalCell =
                            row.querySelector(".harga-total");
                        if (hargaTotalCell) {
                            // Update dengan format HTML yang benar
                            const totalSpan =
                                hargaTotalCell.querySelector(".float-right");
                            if (totalSpan) {
                                totalSpan.textContent = formattedHargaTotal;
                            } else {
                                hargaTotalCell.innerHTML = `
                                    <span class="float-left">Rp.</span>
                                    <span class="float-right">${formattedHargaTotal}</span>
                                `;
                            }

                            // Update data attribute untuk perhitungan grand total
                            hargaTotalCell.setAttribute(
                                "data-harga-total",
                                hargaTotal
                            );

                            // Trigger event untuk update grand total jika ada
                            if (typeof window.updateGrandTotal === "function") {
                                window.updateGrandTotal();
                            }
                        }
                    }
                }
            });
        }
    };

    // Fungsi untuk membuka modal edit AHSP dari halaman RAB.
    // Fungsi ini mengambil data dari baris item_pekerjaans (pastikan setiap baris memiliki atribut data-ahs_id, data-kode, data-sumber)
    window.editAhsp = function (itemId, kategoriId) {
        // Cek jika ahsId tersedia pada window.currentItem
        if (window.currentItem && window.currentItem.ahsId) {
            let row = document.querySelector(`tr[data-item-id="${itemId}"]`);
            if (!row) {
                window.showErrorToast("Data item tidak ditemukan.");
                return;
            }

            let kode = row.getAttribute("data-kode") || "";
            let sumber = row.getAttribute("data-sumber") || "";

            // Gunakan ahsId dari window.currentItem
            let ahsId = window.currentItem.ahsId;

            // Ambil data AHS asli untuk mendapatkan judul asli, bukan uraian item
            fetch(`/ahs/${ahsId}/detail`)
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Gagal mengambil detail AHS");
                    }
                    return response.json();
                })
                .then((ahsData) => {
                    // Dapatkan data AHS dari server
                    const ahsOriginal = window.ahspData
                        ? window.ahspData.find((item) => item.id == ahsId)
                        : null;
                    const judulAsli = ahsOriginal ? ahsOriginal.judul : "";

                    // Jika tidak menemukan judul di ahspData, coba ambil dari API tanpa memanggil openEditModal dulu
                    if (judulAsli) {
                        openEditModal(ahsId, kode, sumber, row, judulAsli);
                    } else {
                        // Jika belum ada data AHSP di memori, panggil API untuk data AHS
                        fetch(`/ahsp/all`)
                            .then((response) => response.json())
                            .then((data) => {
                                // Simpan data untuk penggunaan berikutnya
                                window.ahspData = data;
                                const ahsData = data.find(
                                    (item) => item.id == ahsId
                                );
                                if (ahsData) {
                                    openEditModal(
                                        ahsId,
                                        kode,
                                        sumber,
                                        row,
                                        ahsData.judul
                                    );
                                } else {
                                    // Jika masih tidak ditemukan, gunakan uraian item sebagai fallback
                                    let uraian = row.cells[1]
                                        ? row.cells[1].innerText
                                        : "";
                                    openEditModal(
                                        ahsId,
                                        kode,
                                        sumber,
                                        row,
                                        uraian
                                    );
                                }
                            })
                            .catch((error) => {
                                console.error(
                                    "Error fetching AHSP data:",
                                    error
                                );
                                // Gunakan uraian item sebagai fallback
                                let uraian = row.cells[1]
                                    ? row.cells[1].innerText
                                    : "";
                                openEditModal(ahsId, kode, sumber, row, uraian);
                            });
                    }
                })
                .catch((error) => {
                    console.error("Error fetching AHS detail:", error);
                    // Jika gagal mendapatkan detail, fallback ke uraian_item sebagai judul
                    let uraian = row.cells[1] ? row.cells[1].innerText : "";
                    openEditModal(ahsId, kode, sumber, row, uraian);
                });
            return;
        }

        // Jika tidak ada di window.currentItem, lanjutkan dengan cara lama
        // Cari baris tabel berdasarkan atribut data-item-id
        let row = document.querySelector(`tr[data-item-id="${itemId}"]`);
        if (!row) {
            window.showErrorToast("Data item tidak ditemukan.");
            return;
        }

        // Periksa jika ahsId ada pada button yang dipanggil
        const button = document.querySelector(
            `button[data-item-id="${itemId}"]`
        );
        let ahsId = null;

        // Coba ambil ahs_id dari button jika tersedia
        if (button && button.hasAttribute("data-ahs-id")) {
            ahsId = button.getAttribute("data-ahs-id");
        }

        // Jika tidak ada di button, coba ambil dari baris
        if (!ahsId) {
            ahsId = row.getAttribute("data-ahs_id");
        }

        // Jika masih tidak ditemukan, coba ambil dari API
        if (!ahsId) {
            // Ambil data item dari API
            fetch(`/item-pekerjaans/${itemId}`)
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Gagal mengambil data item");
                    }
                    return response.json();
                })
                .then((item) => {
                    if (item && item.ahs_id) {
                        // Ambil data AHS asli
                        fetch(`/ahs/${item.ahs_id}/detail`)
                            .then((response) => response.json())
                            .then((ahsData) => {
                                // Dapatkan judul asli AHS
                                const ahsOriginal = window.ahspData
                                    ? window.ahspData.find(
                                          (ahs) => ahs.id == item.ahs_id
                                      )
                                    : null;
                                const judulAsli = ahsOriginal
                                    ? ahsOriginal.judul
                                    : "";

                                if (judulAsli) {
                                    openEditModal(
                                        item.ahs_id,
                                        item.kode,
                                        item.sumber,
                                        row,
                                        judulAsli
                                    );
                                } else {
                                    // Jika tidak ditemukan, gunakan API untuk mendapatkan semua AHS
                                    fetch(`/ahsp/all`)
                                        .then((resp) => resp.json())
                                        .then((data) => {
                                            window.ahspData = data;
                                            const ahsItem = data.find(
                                                (ahs) => ahs.id == item.ahs_id
                                            );
                                            if (ahsItem) {
                                                openEditModal(
                                                    item.ahs_id,
                                                    item.kode,
                                                    item.sumber,
                                                    row,
                                                    ahsItem.judul
                                                );
                                            } else {
                                                // Fallback ke uraian item jika tidak ditemukan
                                                let uraian = row.cells[1]
                                                    ? row.cells[1].innerText
                                                    : "";
                                                openEditModal(
                                                    item.ahs_id,
                                                    item.kode,
                                                    item.sumber,
                                                    row,
                                                    uraian
                                                );
                                            }
                                        })
                                        .catch((err) => {
                                            console.error(
                                                "Error fetching all AHSP:",
                                                err
                                            );
                                            let uraian = row.cells[1]
                                                ? row.cells[1].innerText
                                                : "";
                                            openEditModal(
                                                item.ahs_id,
                                                item.kode,
                                                item.sumber,
                                                row,
                                                uraian
                                            );
                                        });
                                }
                            })
                            .catch((error) => {
                                console.error(
                                    "Error fetching AHS detail:",
                                    error
                                );
                                let uraian = row.cells[1]
                                    ? row.cells[1].innerText
                                    : "";
                                openEditModal(
                                    item.ahs_id,
                                    item.kode,
                                    item.sumber,
                                    row,
                                    uraian
                                );
                            });
                    } else {
                        window.showErrorToast(
                            "AHS ID tidak ditemukan untuk item ini."
                        );
                    }
                })
                .catch((error) => {
                    console.error("Error fetching item:", error);
                    window.showErrorToast(
                        "Terjadi kesalahan saat mengambil data item"
                    );
                });
            return;
        }

        // Jika ahsId ditemukan, ambil data AHS asli
        let kode = row.getAttribute("data-kode") || "";
        let sumber = row.getAttribute("data-sumber") || "";

        // Ambil judul asli AHS
        fetch(`/ahs/${ahsId}/detail`)
            .then((response) => response.json())
            .then((ahsData) => {
                // Coba dapatkan judul dari window.ahspData jika tersedia
                const ahsOriginal = window.ahspData
                    ? window.ahspData.find((item) => item.id == ahsId)
                    : null;
                const judulAsli = ahsOriginal ? ahsOriginal.judul : "";

                if (judulAsli) {
                    openEditModal(ahsId, kode, sumber, row, judulAsli);
                } else {
                    // Jika tidak ditemukan, ambil semua AHS
                    fetch(`/ahsp/all`)
                        .then((resp) => resp.json())
                        .then((data) => {
                            window.ahspData = data;
                            const ahsItem = data.find(
                                (item) => item.id == ahsId
                            );
                            if (ahsItem) {
                                openEditModal(
                                    ahsId,
                                    kode,
                                    sumber,
                                    row,
                                    ahsItem.judul
                                );
                            } else {
                                // Fallback ke uraian item
                                let uraian = row.cells[1]
                                    ? row.cells[1].innerText
                                    : "";
                                openEditModal(ahsId, kode, sumber, row, uraian);
                            }
                        })
                        .catch((err) => {
                            console.error("Error fetching all AHSP:", err);
                            let uraian = row.cells[1]
                                ? row.cells[1].innerText
                                : "";
                            openEditModal(ahsId, kode, sumber, row, uraian);
                        });
                }
            })
            .catch((error) => {
                console.error("Error fetching AHS detail:", error);
                let uraian = row.cells[1] ? row.cells[1].innerText : "";
                openEditModal(ahsId, kode, sumber, row, uraian);
            });
    };

    function openEditModal(ahsId, kode, sumber, row, judul) {
        // Ambil informasi lain dari sel-sel tabel
        let satuan = row.cells[3] ? row.cells[3].innerText : "";

        // Pastikan fungsi openFullFormModal sudah tersedia (didefinisikan di ahsp.js)
        if (typeof window.openFullFormModal === "function") {
            // Panggil openFullFormModal dalam mode edit (parameter pertama true)
            window.openFullFormModal(true, judul, ahsId, kode, satuan, sumber);
            document.getElementById("btnUpdate1").classList.add("hidden"); // Hide the new button
        } else {
            window.showErrorToast("Fungsi openFullFormModal tidak ditemukan.");
        }
    }
});
