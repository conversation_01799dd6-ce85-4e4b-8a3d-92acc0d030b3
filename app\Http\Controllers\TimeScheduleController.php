<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TimeSchedule;
use App\Models\Project;
use App\Models\ItemPekerjaan;
use App\Models\KategoriPekerjaan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class TimeScheduleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->isMethod('post')) {
            $request->validate([
                'project_id' => 'required|exists:projects,id',
            ]);
            session(['project_id' => $request->project_id]);
        }

        $projectId = session('project_id');
        if (!$projectId) {
            return redirect()->route('proyek')->with('error', 'Pilih proyek terlebih dahulu.');
        }

        $project = Project::findOrFail($projectId);
        $kategoriPekerjaans = KategoriPekerjaan::with(['items' => function ($query) {
            $query->with('timeSchedule');
        }])->where('project_id', $projectId)->get();

        $timeSchedules = TimeSchedule::where('project_id', $projectId)
            ->orderBy('tanggal_mulai')
            ->get();

        // Hitung total durasi proyek
        $projectStartDate = $timeSchedules->min('tanggal_mulai');
        $projectEndDate = $timeSchedules->max('tanggal_selesai');
        $projectDuration = 0;

        if ($projectStartDate && $projectEndDate) {
            $projectDuration = $projectStartDate->diffInDays($projectEndDate) + 1;
        }

        // Hitung total nilai proyek
        $totalNilaiProyek = 0;
        foreach ($kategoriPekerjaans as $kategori) {
            foreach ($kategori->items as $item) {
                $totalNilaiProyek += $item->harga_total;
            }
        }

        // Hitung bobot tersisa untuk setiap item pekerjaan
        $itemBobotTersisa = [];
        foreach ($kategoriPekerjaans as $kategori) {
            foreach ($kategori->items as $item) {
                // Hitung bobot total berdasarkan proporsi harga_total terhadap total nilai proyek
                $totalBobot = 0;
                if ($totalNilaiProyek > 0) {
                    $totalBobot = ($item->harga_total / $totalNilaiProyek) * 100;
                }

                // Hitung bobot yang sudah digunakan
                $usedBobot = TimeSchedule::where('project_id', $projectId)
                    ->where('item_pekerjaan_id', $item->id)
                    ->sum('bobot');

                // Hitung bobot yang tersisa
                $remainingBobot = max(0, $totalBobot - $usedBobot);

                $itemBobotTersisa[$item->id] = [
                    'total_bobot' => round($totalBobot, 2),
                    'used_bobot' => round($usedBobot, 2),
                    'remaining_bobot' => round($remainingBobot, 2)
                ];
            }
        }

        return view('time-schedule.index', compact('project', 'kategoriPekerjaans', 'timeSchedules', 'projectStartDate', 'projectEndDate', 'projectDuration', 'itemBobotTersisa'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required|exists:projects,id',
            'item_pekerjaan_id' => 'nullable|exists:item_pekerjaans,id',
            'nama_kegiatan' => 'required_without:item_pekerjaan_id|string|max:255',
            'tanggal_mulai' => 'required|date',
            'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
            'bobot' => 'required|numeric|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            DB::beginTransaction();

            // Konversi bobot ke float dan bulatkan ke 2 desimal untuk konsistensi
            $bobot = round(floatval($request->bobot), 2);

            // Hitung durasi
            $startDate = new \DateTime($request->tanggal_mulai);
            $endDate = new \DateTime($request->tanggal_selesai);
            $durasi = $startDate->diff($endDate)->days + 1;

            // Jika ada item_pekerjaan_id, validasi bobot tidak melebihi bobot tersisa
            if ($request->item_pekerjaan_id) {
                // Hitung bobot total dari RAB
                $projectId = $request->project_id;
                $itemPekerjaanId = $request->item_pekerjaan_id;

                // Ambil semua item pekerjaan dalam proyek
                $kategoriPekerjaans = KategoriPekerjaan::with('items')
                    ->where('project_id', $projectId)
                    ->get();

                // Hitung total nilai proyek
                $totalNilaiProyek = 0;
                foreach ($kategoriPekerjaans as $kategori) {
                    foreach ($kategori->items as $item) {
                        $totalNilaiProyek += $item->harga_total;
                    }
                }

                // Ambil item pekerjaan yang dipilih
                $selectedItem = ItemPekerjaan::findOrFail($itemPekerjaanId);

                // Hitung bobot total berdasarkan proporsi harga_total terhadap total nilai proyek
                $totalBobot = 0;
                if ($totalNilaiProyek > 0) {
                    $totalBobot = ($selectedItem->harga_total / $totalNilaiProyek) * 100;
                }

                // Hitung bobot yang sudah digunakan
                $usedBobot = TimeSchedule::where('project_id', $projectId)
                    ->where('item_pekerjaan_id', $itemPekerjaanId)
                    ->sum('bobot');

                // Hitung bobot yang tersisa (bulatkan ke 2 desimal untuk konsistensi)
                $remainingBobot = round(max(0, $totalBobot - $usedBobot), 2);

                // Log untuk debugging
                Log::info('Validasi bobot:', [
                    'bobot_request' => $bobot,
                    'total_bobot' => round($totalBobot, 2),
                    'used_bobot' => round($usedBobot, 2),
                    'remaining_bobot' => $remainingBobot,
                ]);

                // Validasi bobot tidak melebihi bobot tersisa (dengan toleransi 0.02 untuk mengatasi masalah pembulatan)
                if ($bobot > ($remainingBobot + 0.02)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Bobot melebihi bobot tersisa. Bobot tersisa: ' . $remainingBobot . '%',
                        'debug' => [
                            'bobot_request' => $bobot,
                            'remaining_bobot' => $remainingBobot,
                            'difference' => $bobot - $remainingBobot
                        ]
                    ], 422);
                }

                // Jika bobot sangat dekat dengan bobot tersisa, gunakan nilai bobot tersisa
                if (abs($bobot - $remainingBobot) < 0.02) {
                    $bobot = $remainingBobot;
                }
            }

            // Buat time schedule baru
            $timeScheduleData = [
                'project_id' => $request->project_id,
                'item_pekerjaan_id' => $request->item_pekerjaan_id,
                'nama_kegiatan' => $request->nama_kegiatan ?? ItemPekerjaan::find($request->item_pekerjaan_id)->uraian_item,
                'tanggal_mulai' => $request->tanggal_mulai,
                'tanggal_selesai' => $request->tanggal_selesai,
                'bobot' => $bobot, // Gunakan nilai bobot yang sudah diproses
                'progress' => 0, // Default progress 0
                'durasi' => $durasi,
            ];

            // Tambahkan distribusi_bobot jika ada
            if ($request->has('distribusi_bobot')) {
                $timeScheduleData['distribusi_bobot'] = $request->distribusi_bobot;
            }

            $timeSchedule = new TimeSchedule($timeScheduleData);

            $timeSchedule->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Time schedule berhasil disimpan',
                'data' => $timeSchedule
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error saving time schedule: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Check if this is a real-time update from drag/resize operations
        $isRealTimeUpdate = $request->has('is_realtime_update') && $request->is_realtime_update === true;

        // For real-time updates, we'll use lighter validation
        if ($isRealTimeUpdate) {
            $validator = Validator::make($request->all(), [
                'tanggal_mulai' => 'required|date',
                'tanggal_selesai' => 'required|date',
                'durasi' => 'required|integer|min:1',
            ]);
        } else {
            // Full validation for normal updates
            $validator = Validator::make($request->all(), [
                'tanggal_mulai' => 'required|date',
                'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
                'bobot' => 'required|numeric|min:0|max:100',
                'progress' => 'nullable|numeric|min:0|max:100',
            ]);
        }

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $timeSchedule = TimeSchedule::findOrFail($id);

            // Hitung durasi jika tidak disediakan
            $durasi = $request->durasi ?? null;
            if (!$durasi) {
                $startDate = new \DateTime($request->tanggal_mulai);
                $endDate = new \DateTime($request->tanggal_selesai);
                $durasi = $startDate->diff($endDate)->days + 1;
            }

            // Prepare data for update
            $updateData = [
                'tanggal_mulai' => $request->tanggal_mulai,
                'tanggal_selesai' => $request->tanggal_selesai,
                'durasi' => $durasi,
            ];

            // Only include these fields if they're provided or if it's not a real-time update
            if (!$isRealTimeUpdate || $request->has('bobot')) {
                $updateData['bobot'] = $request->bobot;
            }

            if (!$isRealTimeUpdate || $request->has('progress')) {
                $updateData['progress'] = $request->progress ?? $timeSchedule->progress;
            }

            // Handle distribusi_bobot if provided
            if ($request->has('distribusi_bobot')) {
                $updateData['distribusi_bobot'] = $request->distribusi_bobot;
            }

            $timeSchedule->update($updateData);

            return response()->json([
                'success' => true,
                'message' => $isRealTimeUpdate ? 'Schedule updated' : 'Time schedule berhasil diperbarui',
                'data' => $timeSchedule
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating time schedule: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $timeSchedule = TimeSchedule::findOrFail($id);
            $timeSchedule->delete();

            return response()->json([
                'success' => true,
                'message' => 'Time schedule berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting time schedule: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get time schedule data for a project
     */
    public function getProjectSchedule(Request $request)
    {
        $projectId = $request->project_id ?? session('project_id');

        if (!$projectId) {
            return response()->json([
                'success' => false,
                'message' => 'Project ID tidak ditemukan'
            ], 400);
        }

        $timeSchedules = TimeSchedule::where('project_id', $projectId)
            ->with('itemPekerjaan')
            ->orderBy('tanggal_mulai')
            ->get()
            ->map(function ($schedule) {
                return [
                    'id' => $schedule->id,
                    'title' => $schedule->nama_kegiatan ?? $schedule->itemPekerjaan->uraian_item ?? 'Untitled',
                    'start' => $schedule->tanggal_mulai->format('Y-m-d'),
                    'end' => $schedule->tanggal_selesai->format('Y-m-d'),
                    'bobot' => $schedule->bobot,
                    'progress' => $schedule->progress,
                    'durasi' => $schedule->durasi,
                    'item_id' => $schedule->item_pekerjaan_id
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $timeSchedules
        ]);
    }

    /**
     * Update progress for a time schedule
     */
    public function updateProgress(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'progress' => 'required|numeric|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $timeSchedule = TimeSchedule::findOrFail($id);
            $timeSchedule->update([
                'progress' => $request->progress,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Progress berhasil diperbarui',
                'data' => $timeSchedule
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating progress: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate automatic weight based on RAB data
     */
    public function calculateWeight(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required|exists:projects,id',
            'item_pekerjaan_id' => 'required|exists:item_pekerjaans,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $projectId = $request->project_id;
            $itemPekerjaanId = $request->item_pekerjaan_id;

            // Ambil semua item pekerjaan dalam proyek
            $kategoriPekerjaans = KategoriPekerjaan::with('items')
                ->where('project_id', $projectId)
                ->get();

            // Hitung total nilai proyek
            $totalNilaiProyek = 0;
            foreach ($kategoriPekerjaans as $kategori) {
                foreach ($kategori->items as $item) {
                    $totalNilaiProyek += $item->harga_total;
                }
            }

            // Ambil item pekerjaan yang dipilih
            $selectedItem = ItemPekerjaan::findOrFail($itemPekerjaanId);

            // Hitung bobot berdasarkan proporsi harga_total terhadap total nilai proyek
            $totalBobot = 0;
            if ($totalNilaiProyek > 0) {
                $totalBobot = ($selectedItem->harga_total / $totalNilaiProyek) * 100;
            }

            // Hitung bobot yang sudah digunakan
            $usedBobot = TimeSchedule::where('project_id', $projectId)
                ->where('item_pekerjaan_id', $itemPekerjaanId)
                ->sum('bobot');

            // Hitung bobot yang tersisa (bulatkan ke 2 desimal untuk konsistensi)
            $totalBobot = round($totalBobot, 2);
            $usedBobot = round($usedBobot, 2);
            $remainingBobot = round(max(0, $totalBobot - $usedBobot), 2);

            // Log untuk debugging
            Log::info('Perhitungan bobot otomatis:', [
                'item_id' => $itemPekerjaanId,
                'total_bobot' => $totalBobot,
                'used_bobot' => $usedBobot,
                'remaining_bobot' => $remainingBobot,
            ]);

            return response()->json([
                'success' => true,
                'bobot' => $remainingBobot,
                'total_bobot' => $totalBobot,
                'used_bobot' => $usedBobot,
                'message' => 'Bobot berhasil dihitung'
            ]);
        } catch (\Exception $e) {
            Log::error('Error calculating weight: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get remaining weight for an item
     */
    public function getRemainingWeight(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required|exists:projects,id',
            'item_pekerjaan_id' => 'required|exists:item_pekerjaans,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $projectId = $request->project_id;
            $itemPekerjaanId = $request->item_pekerjaan_id;

            // Ambil semua item pekerjaan dalam proyek
            $kategoriPekerjaans = KategoriPekerjaan::with('items')
                ->where('project_id', $projectId)
                ->get();

            // Hitung total nilai proyek
            $totalNilaiProyek = 0;
            foreach ($kategoriPekerjaans as $kategori) {
                foreach ($kategori->items as $item) {
                    $totalNilaiProyek += $item->harga_total;
                }
            }

            // Ambil item pekerjaan yang dipilih
            $selectedItem = ItemPekerjaan::findOrFail($itemPekerjaanId);

            // Hitung bobot total berdasarkan proporsi harga_total terhadap total nilai proyek
            $totalBobot = 0;
            if ($totalNilaiProyek > 0) {
                $totalBobot = ($selectedItem->harga_total / $totalNilaiProyek) * 100;
            }

            // Hitung bobot yang sudah digunakan
            $usedBobot = TimeSchedule::where('project_id', $projectId)
                ->where('item_pekerjaan_id', $itemPekerjaanId)
                ->sum('bobot');

            // Hitung bobot yang tersisa (bulatkan ke 2 desimal untuk konsistensi)
            $totalBobot = round($totalBobot, 2);
            $usedBobot = round($usedBobot, 2);
            $remainingBobot = round(max(0, $totalBobot - $usedBobot), 2);

            // Ambil jadwal yang sudah ada untuk item ini
            $schedules = TimeSchedule::where('project_id', $projectId)
                ->where('item_pekerjaan_id', $itemPekerjaanId)
                ->orderBy('tanggal_mulai')
                ->get()
                ->map(function ($schedule) {
                    return [
                        'id' => $schedule->id,
                        'tanggal_mulai' => $schedule->tanggal_mulai->format('Y-m-d'),
                        'tanggal_selesai' => $schedule->tanggal_selesai->format('Y-m-d'),
                        'bobot' => round($schedule->bobot, 2), // Bulatkan ke 2 desimal
                        'progress' => round($schedule->progress, 2) // Bulatkan ke 2 desimal
                    ];
                });

            // Log untuk debugging
            Log::info('Perhitungan bobot tersisa:', [
                'item_id' => $itemPekerjaanId,
                'total_bobot' => $totalBobot,
                'used_bobot' => $usedBobot,
                'remaining_bobot' => $remainingBobot,
                'schedules_count' => count($schedules)
            ]);

            return response()->json([
                'success' => true,
                'total_bobot' => $totalBobot,
                'used_bobot' => $usedBobot,
                'remaining_bobot' => $remainingBobot,
                'schedules' => $schedules,
                'message' => 'Bobot tersisa berhasil dihitung'
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting remaining weight: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }
}
