@extends('layouts.app')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2">Riwayat Pembayaran</h1>
                <p class="text-gray-600 dark:text-gray-400">Daftar pembayaran yang telah Anda lakukan</p>
            </div>
            <div>
                <a href="{{ route('subscriptions.show') }}"
                    class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                    <i class="fas fa-arrow-left mr-1"></i> Kembali
                </a>
            </div>
        </div>

        @if (session('error'))
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400"
                role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        @if (session('success'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded dark:bg-green-900/30 dark:text-green-400"
                role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif

        <!-- Daftar Pembayaran -->
        <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-blue-600 dark:bg-blue-700 p-4">
                <h2 class="text-white text-lg font-semibold">Riwayat Pembayaran</h2>
            </div>

            <div class="p-6">
                @if ($payments->isEmpty())
                    <div class="text-center py-8">
                        <i class="fas fa-credit-card text-gray-400 dark:text-gray-600 text-5xl mb-4"></i>
                        <p class="text-gray-600 dark:text-gray-400">Anda belum memiliki riwayat pembayaran.</p>
                    </div>
                @else
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead>
                                <tr>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        No. Invoice</th>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Tanggal</th>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Paket</th>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Jumlah</th>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Status</th>
                                    <th
                                        class="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach ($payments as $payment)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-light-text dark:text-dark-text">
                                                {{ $payment->invoice_number }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                                {{ $payment->created_at->format('d F Y') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                                @if ($payment->subscription && $payment->subscription->plan)
                                                    {{ $payment->subscription->plan->name }}
                                                @else
                                                    -
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                                {{ $payment->formatted_amount }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if ($payment->isCompleted())
                                                <span
                                                    class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900/30 dark:text-green-400">Selesai</span>
                                            @elseif($payment->isPending())
                                                @php
                                                    $paymentVerificationMode = \App\Models\SystemSetting::getValue(
                                                        'payment_verification_mode',
                                                        'manual',
                                                    );
                                                @endphp

                                                @if ($payment->payment_method === 'xendit' || $payment->payment_method === 'midtrans')
                                                    <div>
                                                        <span
                                                            class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900/30 dark:text-green-400">Pembayaran
                                                            Berhasil</span>
                                                        @if ($paymentVerificationMode === 'manual')
                                                            <div class="mt-1">
                                                                <span
                                                                    class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900/30 dark:text-yellow-400">Menunggu
                                                                    Verifikasi Admin</span>
                                                            </div>
                                                        @else
                                                            <div class="mt-1">
                                                                <span
                                                                    class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900/30 dark:text-blue-400">Menunggu
                                                                    Konfirmasi Sistem</span>
                                                            </div>
                                                        @endif
                                                    </div>
                                                @else
                                                    <span
                                                        class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900/30 dark:text-yellow-400">Menunggu
                                                        Pembayaran</span>
                                                @endif
                                            @elseif($payment->hasFailed())
                                                <span
                                                    class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-red-900/30 dark:text-red-400">Gagal</span>
                                            @elseif($payment->wasRefunded())
                                                <span
                                                    class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-purple-900/30 dark:text-purple-400">Dikembalikan</span>
                                            @elseif($payment->status === 'processing')
                                                <span
                                                    class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900/30 dark:text-blue-400">Diproses</span>
                                            @else
                                                <span
                                                    class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-gray-300">{{ $payment->status }}</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="{{ route('payments.show', $payment->id) }}"
                                                class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-3">
                                                <i class="fas fa-eye"></i> Detail
                                            </a>

                                            @if ($payment->isPending() && $payment->payment_method === 'bank_transfer' && !$payment->payment_proof)
                                                <a href="{{ route('payments.upload-form', $payment->id) }}"
                                                    class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">
                                                    <i class="fas fa-upload"></i> Upload Bukti
                                                </a>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $payments->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection
