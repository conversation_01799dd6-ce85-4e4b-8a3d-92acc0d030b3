@props(['type' => 'button', 'color' => 'blue', 'loading' => false])

<button 
    type="{{ $type }}"
    {{ $attributes->merge([
        'class' => 'px-4 py-2 rounded-md text-white bg-'.$color.'-600 hover:bg-'.$color.'-700 focus:outline-none focus:ring-2 focus:ring-'.$color.'-500 focus:ring-offset-2 transition-all duration-150 ease-in-out disabled:opacity-80 disabled:cursor-not-allowed' . ($loading ? ' loading-btn' : '')
    ]) }}
    @if($loading) disabled @endif
>
    @if($loading)
        <span class="flex items-center justify-center">
            <i class="fas fa-spinner fa-spin mr-2 spinner"></i>
            <span>Memproses...</span>
        </span>
    @else
        {{ $slot }}
    @endif
</button> 