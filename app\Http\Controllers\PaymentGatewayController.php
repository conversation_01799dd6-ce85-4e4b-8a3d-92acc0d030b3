<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\SubscriptionDuration;
use App\Models\SystemSetting;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PaymentGatewayController extends Controller
{


    /**
     * Create Xendit invoice
     */
    public function createXenditInvoice(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'subscription_id' => 'required|exists:subscriptions,id'
            ]);

            // Get subscription
            $subscription = Subscription::findOrFail($request->subscription_id);

            // Check if subscription belongs to the authenticated user
            if ($subscription->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            // Get subscription plan
            $plan = $subscription->plan;

            if (!$plan) {
                Log::error('Subscription plan not found for subscription ID: ' . $subscription->id);
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription plan not found'
                ], 404);
            }

            // Cek apakah ada durasi berlangganan yang dipilih
            $duration = null;
            $amount = $plan->price; // Default: hanya 1 bulan
            $description = 'Pembayaran untuk paket ' . $plan->name . ' selama 1 bulan';
            $durationMonths = 1;

            // Cek apakah ada durasi berlangganan yang terkait dengan subscription
            $durationId = $subscription->duration_id ?? null;
            if ($durationId) {
                $duration = SubscriptionDuration::where('id', $durationId)
                    ->where('subscription_plan_id', $plan->id)
                    ->where('is_active', true)
                    ->first();

                if ($duration) {
                    // Hitung jumlah yang harus dibayar
                    $amount = $duration->calculateTotalPrice();
                    $durationMonths = $duration->duration_months;

                    // Buat deskripsi pembayaran
                    $description = 'Pembayaran untuk paket ' . $plan->name . ' selama ' . $durationMonths . ' bulan';

                    // Tambahkan informasi diskon atau bulan gratis jika ada
                    if ($duration->promo_type === 'discount') {
                        $description .= " dengan diskon {$duration->promo_value}%";
                    } else {
                        $description .= " dengan gratis {$duration->promo_value} bulan";
                    }
                }
            }

            // Create payment
            $payment = new Payment();
            $payment->user_id = Auth::id();
            $payment->subscription_id = $subscription->id;
            $payment->amount = $amount;
            $payment->payment_id = 'XEN-' . Str::random(10);
            $payment->status = 'pending';
            $payment->payment_method = 'xendit';

            // Generate invoice number
            $invoiceNumber = 'INV-' . date('Ymd') . '-' . str_pad($subscription->id, 5, '0', STR_PAD_LEFT);
            $payment->invoice_number = $invoiceNumber;
            $payment->currency = 'IDR';
            $payment->description = $description;

            // Cek mode verifikasi pembayaran
            $paymentVerificationMode = SystemSetting::getValue('payment_verification_mode', 'manual');
            Log::info('Payment verification mode: ' . $paymentVerificationMode);

            try {
                Log::info('Saving Xendit payment', [
                    'payment_id' => $payment->payment_id,
                    'subscription_id' => $subscription->id,
                    'invoice_number' => $invoiceNumber
                ]);
                $payment->save();
                Log::info('Xendit payment saved successfully');
            } catch (\Exception $e) {
                Log::error('Error saving Xendit payment: ' . $e->getMessage());
                throw $e;
            }

            // Get Xendit settings
            $secretKey = SystemSetting::getValue('xendit_secret_key', '');

            // Check if secret key is empty
            if (empty($secretKey)) {
                Log::error('Xendit secret key is empty');
                return response()->json([
                    'success' => false,
                    'message' => 'Xendit API key not configured'
                ], 500);
            }

            // Set Xendit API URL
            $apiUrl = 'https://api.xendit.co/v2/invoices';

            // Prepare invoice data
            $invoiceData = [
                'external_id' => $payment->payment_id,
                'amount' => (int) $payment->amount,
                'payer_email' => Auth::user()->email,
                'description' => $plan->name . ' Subscription',
                'success_redirect_url' => route('subscriptions.success'),
                'failure_redirect_url' => route('subscriptions.error'),
                'callback_url' => url('/xendit-webhook')
            ];

            // Log invoice data untuk debugging
            Log::info('Xendit invoice data', [
                'payment_id' => $payment->payment_id,
                'callback_url' => url('/xendit-webhook'),
                'invoice_data' => $invoiceData
            ]);

            // Send request to Xendit API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($invoiceData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Basic ' . base64_encode($secretKey . ':')
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if (curl_errno($ch)) {
                throw new \Exception('Curl error: ' . curl_error($ch));
            }

            curl_close($ch);

            // Parse response
            $responseData = json_decode($response, true);

            if ($httpCode !== 200 || !isset($responseData['invoice_url'])) {
                Log::error('Xendit API error: ' . $response);
                throw new \Exception('Failed to create Xendit invoice');
            }

            // Update payment with invoice URL
            $payment->payment_details = [
                'invoice_id' => $responseData['id'],
                'invoice_url' => $responseData['invoice_url']
            ];
            $payment->save();

            // Send email with payment link
            \Illuminate\Support\Facades\Mail::to(Auth::user()->email)->send(new \App\Mail\PaymentLinkMail(
                Auth::user(),
                $payment,
                $payment->subscription,
                $responseData['invoice_url']
            ));

            return response()->json([
                'success' => true,
                'invoice_url' => $responseData['invoice_url'],
                'email_sent' => true
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating Xendit invoice: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to create invoice: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment gateway configuration
     */
    public function getConfig()
    {
        // Ambil konfigurasi gateway pembayaran dari database
        $gateway = SystemSetting::getValue('payment_gateway', 'xendit');
        $config = [];

        if ($gateway === 'xendit') {
            $config = [
                'publicKey' => SystemSetting::getValue('xendit_public_key', ''),
                'isProduction' => (bool) SystemSetting::getValue('xendit_production', false)
            ];
        }

        return response()->json([
            'success' => true,
            'gateway' => $gateway,
            'config' => $config
        ]);
    }
}
