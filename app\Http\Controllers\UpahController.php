<?php

namespace App\Http\Controllers;

use App\Models\Upah;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\ResourceController;

class UpahController extends Controller
{
    use ResourceController;

    /**
     * Display a listing of the resources.
     */
    public function index(Request $request)
    {
        // Jika pengguna adalah customer, alihkan ke halaman proyek
        if (Auth::user()->role === 'customer') {
            return redirect()->route('proyek');
        }

        // Get search query
        $search = $request->input('search');

        // Create query
        $query = Upah::query();

        // Apply search if provided
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('uraian_tenaga', 'like', "%{$search}%")
                    ->orWhere('satuan', 'like', "%{$search}%")
                    ->orWhere('sumber', 'like', "%{$search}%");
            });
        }

        // Order by newest first
        $query->orderBy('created_at', 'desc');

        // Paginate results
        $upahs = $query->paginate(15);

        // Append search to pagination links
        if ($search) {
            $upahs->appends(['search' => $search]);
        }

        return view('upah.index', compact('upahs'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        return $this->storeResource($request, Upah::class, 'Upah berhasil disimpan!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        return $this->updateResource($request, $id, Upah::class, 'Upah berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        return $this->destroyResource(request(), $id, Upah::class, 'Upah berhasil dihapus!');
    }

    /**
     * Get upah data for AJAX requests
     */
    public function getData()
    {
        return $this->getResourceData(Upah::class);
    }
}
