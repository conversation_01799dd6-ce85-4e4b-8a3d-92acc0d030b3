<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionDuration;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;

class SubscriptionPlanController extends Controller
{
    /**
     * Display a listing of subscription plans.
     */
    public function index()
    {
        $plans = \App\Models\SubscriptionPlan::orderBy('price')->get();
        return view('admin.subscription_plans.index', compact('plans'));
    }

    /**
     * Show the form for creating a new subscription plan.
     */
    public function create()
    {
        return view('admin.subscription_plans.create');
    }

    /**
     * Store a newly created subscription plan in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:subscription_plans',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'duration_days' => 'required|integer|min:1',
            'project_limit' => 'required|integer|min:1',
            'max_users' => 'required|integer|min:1',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'can_export_excel' => 'boolean',
            'can_export_excel_formula' => 'boolean',
            'can_export_pdf' => 'boolean',
            'can_use_time_schedule' => 'boolean',
            'can_use_empirical_ahsp' => 'boolean',
            'duration_months.*' => 'nullable|integer|min:1|max:60',
            'promo_type.*' => 'nullable|in:discount,free_months',
            'promo_value.*' => 'nullable|integer|min:1|max:100',
        ]);

        // Konversi features dari array menjadi JSON
        $features = $request->input('features', []);

        $plan = new SubscriptionPlan([
            'name' => $request->name,
            'slug' => $request->slug,
            'description' => $request->description,
            'price' => $request->price,
            'duration_days' => $request->duration_days,
            'features' => $features,
            'is_active' => (bool)$request->input('is_active'),
            'is_featured' => (bool)$request->input('is_featured'),
            'project_limit' => $request->project_limit,
            'max_users' => $request->max_users,
            'can_export_excel' => (bool)$request->input('can_export_excel'),
            'can_export_excel_formula' => (bool)$request->input('can_export_excel_formula'),
            'can_export_pdf' => (bool)$request->input('can_export_pdf'),
            'can_use_time_schedule' => (bool)$request->input('can_use_time_schedule'),
            'can_use_empirical_ahsp' => (bool)$request->input('can_use_empirical_ahsp'),
        ]);

        $plan->save();

        // Simpan durasi berlangganan
        $this->saveDurations($request, $plan);

        return redirect()->route('admin.subscription-plans.index')
            ->with('success', 'Paket langganan berhasil dibuat.');
    }

    /**
     * Show the form for editing the specified subscription plan.
     */
    public function edit($id)
    {
        $plan = \App\Models\SubscriptionPlan::findOrFail($id);
        return view('admin.subscription_plans.edit', compact('plan'));
    }

    /**
     * Update the specified subscription plan in storage.
     */
    public function update(Request $request, $id)
    {
        $plan = SubscriptionPlan::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:subscription_plans,slug,' . $id,
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'duration_days' => 'required|integer|min:1',
            'project_limit' => 'required|integer|min:1',
            'max_users' => 'required|integer|min:1',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'can_export_excel' => 'boolean',
            'can_export_excel_formula' => 'boolean',
            'can_export_pdf' => 'boolean',
            'can_use_time_schedule' => 'boolean',
            'can_use_empirical_ahsp' => 'boolean',
            'duration_months.*' => 'nullable|integer|min:1|max:60',
            'promo_type.*' => 'nullable|in:discount,free_months',
            'promo_value.*' => 'nullable|integer|min:1|max:100',
        ]);

        // Konversi features dari array menjadi JSON
        $features = $request->input('features', []);

        $plan->name = $request->name;
        $plan->slug = $request->slug;
        $plan->description = $request->description;
        $plan->price = $request->price;
        $plan->duration_days = $request->duration_days;
        $plan->features = $features;
        $plan->is_active = (bool)$request->input('is_active');
        $plan->is_featured = (bool)$request->input('is_featured');
        $plan->project_limit = $request->project_limit;
        $plan->max_users = $request->max_users;
        $plan->can_export_excel = (bool)$request->input('can_export_excel');
        $plan->can_export_excel_formula = (bool)$request->input('can_export_excel_formula');
        $plan->can_export_pdf = (bool)$request->input('can_export_pdf');
        $plan->can_use_time_schedule = (bool)$request->input('can_use_time_schedule');
        $plan->can_use_empirical_ahsp = (bool)$request->input('can_use_empirical_ahsp');

        $plan->save();

        // Simpan durasi berlangganan
        $this->saveDurations($request, $plan);

        return redirect()->route('admin.subscription-plans.index')
            ->with('success', 'Paket langganan berhasil diperbarui.');
    }

    /**
     * Remove the specified subscription plan from storage.
     */
    public function destroy($id)
    {
        $plan = \App\Models\SubscriptionPlan::findOrFail($id);

        // Cek apakah ada subscription yang menggunakan plan ini
        $subscriptionsCount = $plan->subscriptions()->count();

        if ($subscriptionsCount > 0) {
            return redirect()->route('admin.subscription-plans.index')
                ->with('error', 'Paket langganan tidak dapat dihapus karena masih digunakan oleh ' . $subscriptionsCount . ' pelanggan.');
        }

        $plan->delete();

        return redirect()->route('admin.subscription-plans.index')
            ->with('success', 'Paket langganan berhasil dihapus.');
    }

    /**
     * Verify a payment.
     */
    public function verifyPayment($id)
    {
        // Cek mode verifikasi pembayaran
        $paymentVerificationMode = \App\Models\SystemSetting::getValue('payment_verification_mode', 'manual');

        // Jika mode verifikasi otomatis, tidak izinkan verifikasi manual
        if ($paymentVerificationMode === 'automatic') {
            return redirect()->route('admin.payments.index')
                ->with('error', 'Verifikasi manual dinonaktifkan. Sistem menggunakan verifikasi otomatis.');
        }

        $payment = \App\Models\Payment::findOrFail($id);

        // Pastikan payment masih dalam status processing
        if ($payment->status !== 'processing') {
            return redirect()->route('admin.payments.index')
                ->with('error', 'Pembayaran tidak dapat diverifikasi karena status bukan processing.');
        }

        // Update payment
        $payment->status = 'completed';
        $payment->paid_at = now();
        $payment->save();

        // Aktifkan subscription
        if ($payment->subscription) {
            $payment->subscription->status = 'active';
            $payment->subscription->save();

            // Update user current subscription
            $user = $payment->user;
            $user->current_subscription_id = $payment->subscription_id;
            $user->save();
        }

        return redirect()->route('admin.payments.index')
            ->with('success', 'Pembayaran berhasil diverifikasi.');
    }

    /**
     * Reject a payment.
     */
    public function rejectPayment($id)
    {
        // Cek mode verifikasi pembayaran
        $paymentVerificationMode = \App\Models\SystemSetting::getValue('payment_verification_mode', 'manual');

        // Jika mode verifikasi otomatis, tidak izinkan penolakan manual
        if ($paymentVerificationMode === 'automatic') {
            return redirect()->route('admin.payments.index')
                ->with('error', 'Penolakan manual dinonaktifkan. Sistem menggunakan verifikasi otomatis.');
        }

        $payment = \App\Models\Payment::findOrFail($id);

        // Pastikan payment masih dalam status processing
        if ($payment->status !== 'processing') {
            return redirect()->route('admin.payments.index')
                ->with('error', 'Pembayaran tidak dapat ditolak karena status bukan processing.');
        }

        // Update payment
        $payment->status = 'failed';
        $payment->save();

        return redirect()->route('admin.payments.index')
            ->with('success', 'Pembayaran berhasil ditolak.');
    }

    /**
     * Save subscription durations for a plan
     */
    private function saveDurations(Request $request, SubscriptionPlan $plan)
    {
        // Jika tidak ada durasi yang dikirim, return
        if (!$request->has('duration_months')) {
            return;
        }

        $durationMonths = $request->input('duration_months', []);
        $promoTypes = $request->input('promo_type', []);
        $promoValues = $request->input('promo_value', []);
        $durationIds = $request->input('duration_id', []);
        $isActive = $request->input('duration_is_active', []);
        $sortOrder = $request->input('duration_sort_order', []);

        // Hapus durasi yang tidak ada di request
        $existingIds = [];
        foreach ($durationIds as $index => $id) {
            if (strpos($id, 'new_') === 0) {
                continue; // Skip new durations
            }
            $existingIds[] = $id;
        }

        // Hapus durasi yang tidak ada di request
        $plan->durations()->whereNotIn('id', $existingIds)->delete();

        // Simpan atau update durasi
        foreach ($durationMonths as $index => $months) {
            if (!isset($promoTypes[$index]) || !isset($promoValues[$index])) {
                continue; // Skip jika data tidak lengkap
            }

            $durationId = $durationIds[$index] ?? null;
            $durationIsActive = isset($isActive[$index]) ? (bool)$isActive[$index] : true;
            $durationSortOrder = $sortOrder[$index] ?? $index;

            // Jika ID dimulai dengan 'new_', ini adalah durasi baru
            if ($durationId && strpos($durationId, 'new_') === 0) {
                $durationId = null;
            }

            $durationData = [
                'subscription_plan_id' => $plan->id,
                'duration_months' => $months,
                'promo_type' => $promoTypes[$index],
                'promo_value' => $promoValues[$index],
                'is_active' => $durationIsActive,
                'sort_order' => $durationSortOrder,
            ];

            if ($durationId) {
                // Update existing duration
                SubscriptionDuration::where('id', $durationId)->update($durationData);
            } else {
                // Create new duration
                SubscriptionDuration::create($durationData);
            }
        }
    }
}
