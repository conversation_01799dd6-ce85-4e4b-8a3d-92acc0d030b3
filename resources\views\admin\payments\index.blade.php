@extends('layouts.app')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2"><PERSON><PERSON><PERSON></h1>
            <p class="text-gray-600 dark:text-gray-400">Ke<PERSON>la dan verifikasi pembayaran dari pelanggan</p>
        </div>

        @if (session('error'))
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400"
                role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        @if (session('success'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded dark:bg-green-900/30 dark:text-green-400"
                role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif

        <!-- Filter dan <PERSON> -->
        <div
            class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 transform hover:shadow-lg dark:bg-dark-card p-4 mb-6">
            <form action="{{ route('admin.payments.index') }}" method="GET" class="flex flex-wrap gap-4">
                <div class="flex-1 min-w-[200px]">
                    <label for="status"
                        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                    <select id="status" name="status"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white">
                        <option value="">Semua Status</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Menunggu</option>
                        <option value="processing" {{ request('status') === 'processing' ? 'selected' : '' }}>Diproses
                        </option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Selesai</option>
                        <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Gagal</option>
                        <option value="refunded" {{ request('status') === 'refunded' ? 'selected' : '' }}>Dikembalikan
                        </option>
                    </select>
                </div>

                <div class="flex-1 min-w-[200px]">
                    <label for="search"
                        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Cari</label>
                    <input type="text" id="search" name="search" value="{{ request('search') }}"
                        placeholder="Cari invoice atau nama pelanggan"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white">
                </div>

                <div class="flex items-end">
                    <button type="submit"
                        class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                        <i class="fas fa-search mr-1"></i> Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Daftar Pembayaran -->
        <div
            class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 transform hover:shadow-lg dark:bg-dark-card">
            <div class="bg-blue-600 dark:bg-blue-700 p-4">
                <h2 class="text-white text-lg font-semibold">Daftar Pembayaran</h2>
            </div>

            <div class="p-6">
                @if ($payments->isEmpty())
                    <div class="text-center py-8">
                        <i class="fas fa-credit-card text-gray-400 dark:text-gray-600 text-5xl mb-4"></i>
                        <p class="text-gray-600 dark:text-gray-400">Belum ada data pembayaran.</p>
                    </div>
                @else
                    <div class="overflow-x-auto">
                        <table class="text-sm min-w-full bg-white dark:bg-dark-card border dark:border-gray-700">
                            <thead class="bg-blue-200 dark:bg-gray-800 sticky top-0 z-10">
                                <tr>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">No. Invoice</th>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Tanggal</th>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Pelanggan</th>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Paket</th>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Jumlah</th>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Status</th>
                                    <th class="py-2 px-4 border dark:border-gray-700 text-left">Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach ($payments as $payment)
                                    <tr>
                                        <td class="py-2 px-4 border dark:border-gray-700">
                                            {{ $payment->invoice_number }}
                                        </td>
                                        <td class="py-2 px-4 border dark:border-gray-700">
                                            {{ $payment->created_at->format('d F Y') }}
                                        </td>
                                        <td class="py-2 px-4 border dark:border-gray-700">
                                            <div class="font-medium text-light-text dark:text-dark-text">
                                                {{ $payment->user->name }}</div>
                                            <div class="text-gray-600 dark:text-gray-400 text-xs">
                                                {{ $payment->user->email }}</div>
                                        </td>
                                        <td class="py-2 px-4 border dark:border-gray-700">
                                            @if ($payment->subscription && $payment->subscription->plan)
                                                {{ $payment->subscription->plan->name }}
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="py-2 px-4 border dark:border-gray-700">
                                            {{ $payment->formatted_amount }}
                                        </td>
                                        <td class="py-2 px-4 border dark:border-gray-700">
                                            @if ($payment->isCompleted())
                                                <span
                                                    class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900/30 dark:text-green-400">Selesai</span>
                                            @elseif($payment->isPending())
                                                <span
                                                    class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900/30 dark:text-yellow-400">Menunggu</span>
                                            @elseif($payment->hasFailed())
                                                <span
                                                    class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-red-900/30 dark:text-red-400">Gagal</span>
                                            @elseif($payment->wasRefunded())
                                                <span
                                                    class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-purple-900/30 dark:text-purple-400">Dikembalikan</span>
                                            @elseif($payment->status === 'processing')
                                                <span
                                                    class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900/30 dark:text-blue-400">Diproses</span>
                                            @else
                                                <span
                                                    class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-gray-300">{{ $payment->status }}</span>
                                            @endif
                                        </td>
                                        <td class="py-2 px-4 border dark:border-gray-700 text-center">
                                            <button type="button"
                                                class="action-btn-item px-4 text-light-accent dark:text-dark-accent p-1 rounded-full hover:bg-light-accent/10 dark:hover:bg-dark-accent/10 transition-all duration-200 transform hover:scale-105"
                                                data-payment-id="{{ $payment->id }}"
                                                data-detail-url="{{ route('admin.payments.show', $payment->id) }}"
                                                data-status="{{ $payment->status }}">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>

                                            <form id="verify-form-{{ $payment->id }}"
                                                action="{{ route('admin.payments.verify', $payment->id) }}" method="POST"
                                                class="hidden">
                                                @csrf
                                            </form>

                                            <form id="reject-form-{{ $payment->id }}"
                                                action="{{ route('admin.payments.reject', $payment->id) }}" method="POST"
                                                class="hidden">
                                                @csrf
                                            </form>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $payments->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Context Menu -->
    <div id="context-menu"
        class="hidden absolute bg-white dark:bg-dark-bg-secondary shadow-lg rounded-md py-2 w-32 border border-gray-200 dark:border-dark-accent/20 z-50 text-sm">
        <a href="#" id="detail-link"
            class="w-full px-4 py-2 text-left text-light-accent dark:text-dark-accent hover:bg-light-accent/10 dark:hover:bg-dark-accent/10 hover:text-light-accent/80 dark:hover:text-dark-accent/80 text-sm flex items-center transition-all duration-200">
            <i class="fas fa-eye mr-2 w-4 h-4"></i> Detail
        </a>
        <div id="processing-actions" class="hidden">
            <button id="verify-btn"
                class="w-full px-4 py-2 text-left hover:bg-green-50 dark:hover:bg-green-900/10 text-sm flex items-center text-green-500 hover:text-green-600 dark:text-green-400 dark:hover:text-green-300 transition-all duration-200">
                <i class="fas fa-check mr-2 w-4 h-4"></i> Verifikasi
            </button>
            <button id="reject-btn"
                class="w-full px-4 py-2 text-left hover:bg-red-50 dark:hover:bg-red-900/10 text-sm flex items-center text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 transition-all duration-200">
                <i class="fas fa-times mr-2 w-4 h-4"></i> Tolak
            </button>
        </div>
    </div>

    <!-- Modal Konfirmasi Verifikasi -->
    <div id="verifyModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden overflow-y-auto py-8 text-sm">
        <div class="flex items-center justify-center h-full">
            <div
                class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 transform hover:shadow-lg dark:bg-dark-card max-w-md w-full mx-auto my-auto border-2 border-light-accent dark:border-dark-accent">
                <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10 rounded-t-lg">
                    <div class="flex justify-between items-center">
                        <h3 class="text-white dark:text-dark-text text-lg font-semibold">Konfirmasi Verifikasi</h3>
                        <button type="button" 
                            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none"
                            onclick="closeVerifyModal()">
                            <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    </div>

                <div class="p-6 overflow-y-auto" style="scrollbar-width: thin;">
                    <p class="text-gray-600 dark:text-gray-400 mb-4">Apakah Anda yakin ingin memverifikasi pembayaran ini?
                        Tindakan ini akan mengaktifkan langganan pelanggan.</p>

                    <div class="flex justify-end gap-3 border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                        <button type="button"
                            class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150"
                            onclick="closeVerifyModal()">
                            Batal
                        </button>
                        <button type="button" id="confirmVerifyButton"
                            class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                            Verifikasi
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Konfirmasi Tolak -->
    <div id="rejectModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden overflow-y-auto py-8 text-sm">
        <div class="flex items-center justify-center h-full">
            <div
                class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 transform hover:shadow-lg dark:bg-dark-card max-w-md w-full mx-auto my-auto border-2 border-light-accent dark:border-dark-accent">
                <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10 rounded-t-lg">
                    <div class="flex justify-between items-center">
                        <h3 class="text-white dark:text-dark-text text-lg font-semibold">Konfirmasi Penolakan</h3>
                        <button type="button"
                            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none"
                            onclick="closeRejectModal()">
                            <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    </div>

                <div class="p-6 overflow-y-auto" style="scrollbar-width: thin;">
                    <p class="text-gray-600 dark:text-gray-400 mb-4">Apakah Anda yakin ingin menolak pembayaran ini?
                        Tindakan ini akan menandai pembayaran sebagai gagal.</p>

                    <div class="flex justify-end gap-3 border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                        <button type="button"
                            class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150"
                            onclick="closeRejectModal()">
                            Batal
                        </button>
                        <button type="button" id="confirmRejectButton"
                            class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                            Tolak
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        let paymentIdToVerify = null;
        let paymentIdToReject = null;
        let contextMenu = document.getElementById('context-menu');
        let actionButtons = document.querySelectorAll('.action-btn-item');

        // Setup context menu for each action button
        actionButtons.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Hide any visible context menu first
                contextMenu.classList.add('hidden');

                // Get payment data from the button's data attributes
                const paymentId = this.getAttribute('data-payment-id');
                const detailUrl = this.getAttribute('data-detail-url');
                const status = this.getAttribute('data-status');

                // Set payment ID for verification/rejection
                paymentIdToVerify = paymentId;
                paymentIdToReject = paymentId;

                // Update detail link
                document.getElementById('detail-link').href = detailUrl;

                // Setup verify and reject buttons
                document.getElementById('verify-btn').onclick = function() {
                    confirmVerify();
                };

                document.getElementById('reject-btn').onclick = function() {
                    confirmReject();
                };

                // Show/hide processing actions based on payment status
                const processingActions = document.getElementById('processing-actions');
                if (status === 'processing') {
                    processingActions.classList.remove('hidden');
                } else {
                    processingActions.classList.add('hidden');
                }

                // Position the context menu
                const rect = this.getBoundingClientRect();
                contextMenu.style.top = `${rect.bottom + window.scrollY}px`;
                contextMenu.style.left = `${rect.left + window.scrollX - 100}px`;

                // Show the context menu
                contextMenu.classList.remove('hidden');
            });
        });

        // Hide context menu when clicking elsewhere
        document.addEventListener('click', function() {
            contextMenu.classList.add('hidden');
        });

        function confirmVerify() {
            document.getElementById('verifyModal').classList.remove('hidden');
            document.getElementById('confirmVerifyButton').addEventListener('click', verifyPayment);
            contextMenu.classList.add('hidden');
        }

        function closeVerifyModal() {
            document.getElementById('verifyModal').classList.add('hidden');
            document.getElementById('confirmVerifyButton').removeEventListener('click', verifyPayment);
        }

        function verifyPayment() {
            if (paymentIdToVerify) {
                document.getElementById('verify-form-' + paymentIdToVerify).submit();
            }
        }

        function confirmReject() {
            document.getElementById('rejectModal').classList.remove('hidden');
            document.getElementById('confirmRejectButton').addEventListener('click', rejectPayment);
            contextMenu.classList.add('hidden');
        }

        function closeRejectModal() {
            document.getElementById('rejectModal').classList.add('hidden');
            document.getElementById('confirmRejectButton').removeEventListener('click', rejectPayment);
        }

        function rejectPayment() {
            if (paymentIdToReject) {
                document.getElementById('reject-form-' + paymentIdToReject).submit();
            }
        }
    </script>
@endsection
