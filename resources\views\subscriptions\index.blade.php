@extends('layouts.app')

@section('styles')
<style>
    /* Modal Checkout Styles */
    #checkoutModal .dark\:bg-dark-card {
        @apply bg-white dark:bg-gray-800;
    }
    
    #checkoutModal .text-light-text {
        @apply text-gray-800 dark:text-white;
    }
    
    #checkoutModal .dark\:text-dark-text {
        @apply text-gray-800 dark:text-white;
    }
    
    #checkoutModal .border-gray-200 {
        @apply border-gray-200 dark:border-gray-700;
    }
    
    #checkoutModal .dark\:border-gray-700 {
        @apply border-gray-200 dark:border-gray-700;
    }
    
    #checkoutModal .bg-gray-50 {
        @apply bg-gray-50 dark:bg-gray-700;
    }
    
    #checkoutModal .dark\:bg-gray-800\/50 {
        @apply bg-gray-50 dark:bg-gray-700;
    }
    
    #checkoutModal .text-gray-600 {
        @apply text-gray-600 dark:text-gray-300;
    }
    
    #checkoutModal .dark\:text-gray-400 {
        @apply text-gray-600 dark:text-gray-300;
    }
    
    /* Animation styles for modal */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(-20px); }
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-out forwards;
    }
    
    .animate-fadeOut {
        animation: fadeOut 0.2s ease-in forwards;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        #checkoutModal .lg\:col-span-2,
        #checkoutModal .lg\:col-span-1 {
            @apply col-span-full;
        }
        
        #checkoutModal .lg\:grid-cols-3 {
            @apply grid-cols-1;
        }
    }
</style>
@endsection

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2">Paket Berlangganan</h1>
            <p class="text-gray-600 dark:text-gray-400">Pilih paket berlangganan yang sesuai dengan kebutuhan Anda</p>
        </div>

        @if (session('error'))
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400"
                role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        @if (session('success'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded dark:bg-green-900/30 dark:text-green-400"
                role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif

        <!-- Status Langganan Saat Ini -->
        <div class="bg-white dark:bg-dark-card rounded-lg shadow-md p-6 mb-8 transition-all duration-200">
            <h2 class="text-xl font-semibold text-light-text dark:text-dark-text mb-4">Status Langganan Anda</h2>

            @if ($user->isOnTrial())
                <div class="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-lg mb-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-500 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-blue-800 dark:text-blue-300">Masa Uji Coba</h3>
                            <div class="mt-2 text-blue-700 dark:text-blue-400">
                                <p>Anda sedang dalam masa uji coba paket Enterprise.</p>
                                <p class="mt-1">Sisa waktu: <span id="trial-remaining-time"
                                        class="font-semibold">{{ $user->trialDaysRemaining(true) }}</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            @elseif($currentSubscription && $currentSubscription->isActive())
                <div class="bg-green-100 dark:bg-green-900/30 p-4 rounded-lg mb-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 dark:text-green-400 text-xl"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-green-800 dark:text-green-300">Langganan Aktif</h3>
                            <div class="mt-2 text-green-700 dark:text-green-400">
                                <p>Anda berlangganan paket <span
                                        class="font-semibold">{{ $currentSubscription->plan->name }}</span>.</p>
                                <p class="mt-1">Berlaku hingga: <span
                                        class="font-semibold">{{ $currentSubscription->end_date->format('d F Y') }}</span>
                                </p>
                                <p class="mt-1">Sisa waktu: <span id="index-remaining-time"
                                        class="font-semibold">{{ $currentSubscription->daysRemaining(true) }}</span></p>
                            </div>
                            <div class="mt-3">
                                <a href="{{ route('subscriptions.show') }}"
                                    class="text-light-accent dark:text-dark-accent hover:underline">
                                    <i class="fas fa-eye mr-1"></i> Lihat Detail
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <div class="bg-yellow-100 dark:bg-yellow-900/30 p-4 rounded-lg mb-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-500 dark:text-yellow-400 text-xl"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-yellow-800 dark:text-yellow-300">Tidak Ada Langganan Aktif
                            </h3>
                            <div class="mt-2 text-yellow-700 dark:text-yellow-400">
                                <p>Anda belum memiliki langganan aktif. Silakan pilih paket langganan di bawah ini.</p>

                                @if (!$user->has_used_trial)
                                    <div class="mt-3">
                                        <form action="{{ route('subscriptions.start-trial') }}" method="POST">
                                            @csrf
                                            <button type="submit"
                                                class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text font-medium py-2 px-4 rounded-lg transition-colors duration-150 shadow-sm hover:shadow transform hover:scale-105">
                                                <i class="fas fa-rocket mr-1"></i> Coba Gratis 15 Hari
                                            </button>
                                        </form>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Daftar Paket Langganan -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach ($plans as $plan)
                <div
                    class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-300 transform hover:scale-105 hover:shadow-lg {{ $plan->is_featured ? 'border-2 border-light-accent dark:border-dark-accent' : '' }}">
                    @if ($plan->is_featured)
                        <div class="bg-light-accent dark:bg-dark-accent text-white dark:text-dark-text text-center py-1 font-medium">
                            <span>Rekomendasi</span>
                        </div>
                    @endif

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-light-text dark:text-dark-text mb-2">{{ $plan->name }}</h3>
                        <div class="text-3xl font-bold text-light-accent dark:text-dark-accent mb-4">
                            {{ $plan->formatted_price }}
                            <span class="text-sm font-normal text-gray-600 dark:text-gray-400">/
                                {{ $plan->duration_in_months }} bulan</span>
                        </div>

                        <div class="text-gray-600 dark:text-gray-400 mb-6">
                            <p>{{ $plan->description }}</p>
                        </div>

                        <div class="mb-6">
                            <h4 class="font-semibold text-light-text dark:text-dark-text mb-2">Fitur:</h4>
                            <ul class="space-y-2">
                                <li class="flex items-start">
                                    <i class="fas fa-check text-light-accent dark:text-dark-accent mt-1 mr-2"></i>
                                    <span>Maksimal {{ $plan->project_limit }} proyek</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-light-accent dark:text-dark-accent mt-1 mr-2"></i>
                                    <span>Maksimal {{ $plan->max_users }} pengguna</span>
                                </li>
                                @if ($plan->features)
                                    @foreach ($plan->features as $feature)
                                        <li class="flex items-start">
                                            <i class="fas fa-check text-light-accent dark:text-dark-accent mt-1 mr-2"></i>
                                            <span>{{ $feature }}</span>
                                        </li>
                                    @endforeach
                                @endif
                            </ul>
                        </div>

                        <div class="mt-auto">
                            <button 
                                onclick="openCheckoutModal({{ $plan->id }})"
                                class="block w-full bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text font-medium py-2 px-4 rounded-lg transition-colors duration-150 shadow-sm hover:shadow transform hover:scale-105">
                                Pilih Paket
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Checkout Modal - Updated to match fullFormModal style -->
    <div id="checkoutModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
        <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-5xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
            <!-- Header -->
            <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
                <div class="flex justify-between items-center">
                    <h2 class="text-white dark:text-dark-text text-lg font-semibold">Checkout</h2>
                    <button type="button" onclick="closeCheckoutModal()" 
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                        <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Content -->
            <div id="checkoutModalContent" class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
                <div class="flex flex-col justify-center items-center h-48">
                    <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-light-accent dark:border-dark-accent mb-4"></div>
                    <p class="text-gray-600 dark:text-gray-400 text-center">Memuat halaman checkout...</p>
                </div>
            </div>

            <div id="payment-loading" class="hidden mt-4 text-center">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-light-accent dark:border-dark-accent">
                </div>
                <p class="mt-2 text-gray-600 dark:text-gray-400">Memproses pembayaran...</p>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    @vite('resources/js/payment-gateway.js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fungsi untuk memperbarui sisa waktu
            function updateRemainingTime(elementId, endDateStr) {
                const remainingTimeElement = document.getElementById(elementId);
                if (!remainingTimeElement) return;

                const endDate = new Date(endDateStr);
                const now = new Date();

                // Hitung selisih waktu dalam milidetik
                let diff = endDate - now;

                // Jika waktu sudah habis
                if (diff <= 0) {
                    remainingTimeElement.textContent = '0 hari 0 jam 0 menit 0 detik';
                    return;
                }

                // Hitung hari, jam, menit, detik
                const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                diff -= days * (1000 * 60 * 60 * 24);

                const hours = Math.floor(diff / (1000 * 60 * 60));
                diff -= hours * (1000 * 60 * 60);

                const minutes = Math.floor(diff / (1000 * 60));
                diff -= minutes * (1000 * 60);

                const seconds = Math.floor(diff / 1000);

                // Perbarui teks
                remainingTimeElement.textContent = `${days} hari ${hours} jam ${minutes} menit ${seconds} detik`;
            }

            // Perbarui sisa waktu langganan aktif
            @if (isset($currentSubscription) && $currentSubscription)
                const subscriptionEndDateStr = "{{ $currentSubscription->end_date->toIso8601String() }}";
                updateRemainingTime('index-remaining-time', subscriptionEndDateStr);
                setInterval(function() {
                    updateRemainingTime('index-remaining-time', subscriptionEndDateStr);
                }, 1000);
            @endif

            // Perbarui sisa waktu uji coba
            @if ($user->isOnTrial())
                const trialEndDateStr = "{{ $user->trial_ends_at->toIso8601String() }}";
                updateRemainingTime('trial-remaining-time', trialEndDateStr);
                setInterval(function() {
                    updateRemainingTime('trial-remaining-time', trialEndDateStr);
                }, 1000);
            @endif
        });
        
        // Fungsi untuk membuka modal checkout
        function openCheckoutModal(planId) {
            // Tampilkan modal dengan animasi
            const modal = document.getElementById('checkoutModal');
            modal.classList.remove('hidden');
            
            // Tambahkan efek animasi slide-in
            const modalContent = modal.querySelector('.bg-white');
            modalContent.classList.add('animate-fadeIn');
            
            // Ambil konten checkout dari server
            fetch(`/subscriptions/checkout/${planId}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                // Ekstrak konten utama dari response HTML
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const content = doc.querySelector('.container');
                
                // Tampilkan konten di modal
                if (content) {
                    // Hanya tampilkan bagian konten yang relevan (grid dengan detail dan pembayaran)
                    const relevantContent = content.querySelector('.grid');
                    document.getElementById('checkoutModalContent').innerHTML = relevantContent.outerHTML;
                    
                    // Inisialisasi script untuk payment gateway
                    initializeModalScripts();
                } else {
                    document.getElementById('checkoutModalContent').innerHTML = '<p class="text-red-500 dark:text-red-400">Gagal memuat konten checkout.</p>';
                }
            })
            .catch(error => {
                console.error('Error loading checkout content:', error);
                document.getElementById('checkoutModalContent').innerHTML = '<p class="text-red-500 dark:text-red-400">Terjadi kesalahan saat memuat konten checkout.</p>';
            });
        }
        
        // Fungsi untuk menutup modal checkout
        function closeCheckoutModal() {
            // Tambahkan efek animasi fade-out
            const modal = document.getElementById('checkoutModal');
            const modalContent = modal.querySelector('.bg-white');
            modalContent.classList.add('animate-fadeOut');
            
            // Tunda menyembunyikan modal hingga animasi selesai
            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('animate-fadeOut');
                
                // Reset konten modal
                document.getElementById('checkoutModalContent').innerHTML = `
                    <div class="flex flex-col justify-center items-center h-48">
                        <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-light-accent dark:border-dark-accent mb-4"></div>
                        <p class="text-gray-600 dark:text-gray-400 text-center">Memuat halaman checkout...</p>
                    </div>
                `;
                
                // Bersihkan event listener yang tersisa
                const oldPayButton = document.getElementById('pay-button');
                if (oldPayButton) {
                    const newPayButton = oldPayButton.cloneNode(true);
                    oldPayButton.parentNode.replaceChild(newPayButton, oldPayButton);
                }
            }, 200);
        }
        
        // Fungsi untuk menginisialisasi script di dalam modal
        function initializeModalScripts() {
            // Ambil semua elemen script yang ada dalam konten modal
            const modalContent = document.getElementById('checkoutModalContent');
            
            // Inisialisasi variabel dan elemen untuk payment
            const autoRenewCheckbox = document.getElementById('auto_renew');
            const useDurationCheckbox = document.getElementById('use_duration');
            const termsCheckbox = document.getElementById('terms');
            const payButton = document.getElementById('pay-button');
            const paymentLoading = document.getElementById('payment-loading');
            const durationOptions = document.querySelectorAll('.duration-option');
            const packagePriceElement = document.getElementById('package-price');
            const totalPriceElement = document.getElementById('total-price');
            const durationContainer = document.getElementById('duration-container');
            
            if (!payButton) return;
            
            // Variabel untuk menyimpan durasi yang dipilih
            let selectedDurationId = null;
            let originalPlanPrice = payButton.getAttribute('data-plan-price');
            let originalFormattedPrice = '';
            
            if (packagePriceElement) {
                originalFormattedPrice = packagePriceElement.textContent;
            }
            
            // Inisialisasi durasi yang dipilih
            if (durationOptions && durationOptions.length > 0 && useDurationCheckbox && useDurationCheckbox.checked) {
                const firstOption = durationOptions[0];
                selectedDurationId = firstOption.dataset.durationId;

                // Update harga sesuai dengan durasi yang dipilih
                if (firstOption.dataset.formattedPrice && packagePriceElement && totalPriceElement) {
                    packagePriceElement.textContent = firstOption.dataset.formattedPrice;
                    totalPriceElement.textContent = firstOption.dataset.formattedPrice;
                }
            }
            
            // Event listener untuk checkbox durasi
            if (useDurationCheckbox) {
                useDurationCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        // Tampilkan durasi container jika checkbox dicentang
                        if (durationContainer) durationContainer.classList.remove('hidden');
                        
                        // Set durasi pertama sebagai pilihan
                        if (durationOptions && durationOptions.length > 0) {
                            const firstOption = durationOptions[0];
                            selectedDurationId = firstOption.dataset.durationId;
                            
                            // Update harga sesuai dengan durasi yang dipilih
                            if (firstOption.dataset.formattedPrice && packagePriceElement && totalPriceElement) {
                                packagePriceElement.textContent = firstOption.dataset.formattedPrice;
                                totalPriceElement.textContent = firstOption.dataset.formattedPrice;
                            }
                            
                            // Select the first option visually
                            durationOptions.forEach(opt => {
                                opt.classList.remove('selected', 'border-blue-500');
                                const radioDiv = opt.querySelector('.duration-radio div');
                                if (radioDiv) radioDiv.classList.add('hidden');
                            });
                            
                            firstOption.classList.add('selected', 'border-blue-500');
                            const firstRadioDiv = firstOption.querySelector('.duration-radio div');
                            if (firstRadioDiv) firstRadioDiv.classList.remove('hidden');
                        }
                    } else {
                        // Sembunyikan durasi container jika checkbox tidak dicentang
                        if (durationContainer) durationContainer.classList.add('hidden');
                        
                        // Reset durasi dan tampilkan harga untuk 1 bulan
                        selectedDurationId = null;
                        if (packagePriceElement && totalPriceElement && originalFormattedPrice) {
                            packagePriceElement.textContent = originalFormattedPrice;
                            totalPriceElement.textContent = originalFormattedPrice;
                        }
                    }
                });
            }
            
            // Event listener untuk durasi options
            if (durationOptions) {
                durationOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        // Hanya berfungsi jika durasi diaktifkan
                        if (useDurationCheckbox && !useDurationCheckbox.checked) return;
                        
                        // Hapus kelas selected dari semua opsi
                        durationOptions.forEach(opt => {
                            opt.classList.remove('selected', 'border-blue-500');
                            const radioDiv = opt.querySelector('.duration-radio div');
                            if (radioDiv) radioDiv.classList.add('hidden');
                        });

                        // Tambahkan kelas selected ke opsi yang dipilih
                        this.classList.add('selected', 'border-blue-500');
                        const radioDiv = this.querySelector('.duration-radio div');
                        if (radioDiv) radioDiv.classList.remove('hidden');

                        // Simpan ID durasi yang dipilih
                        selectedDurationId = this.dataset.durationId;

                        // Update harga sesuai dengan durasi yang dipilih
                        if (this.dataset.formattedPrice && packagePriceElement && totalPriceElement) {
                            packagePriceElement.textContent = this.dataset.formattedPrice;
                            totalPriceElement.textContent = this.dataset.formattedPrice;
                        }
                    });
                });
            }
            
            // Event listener untuk tombol bayar
            if (payButton) {
                // Hapus event listener lama jika ada (untuk mencegah duplikasi)
                const newPayButton = payButton.cloneNode(true);
                payButton.parentNode.replaceChild(newPayButton, payButton);
                
                // Tambahkan class sesuai tema pada tombol
                newPayButton.className = 'w-full bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text font-medium py-3 px-4 rounded-lg transition-colors duration-150 shadow-sm hover:shadow transform hover:scale-105';
                
                // Tambahkan event listener baru
                newPayButton.addEventListener('click', function() {
                    const planId = this.getAttribute('data-plan-id');
                    
                    // Validate terms checkbox
                    if (termsCheckbox && !termsCheckbox.checked) {
                        alert('Anda harus menyetujui Syarat dan Ketentuan untuk melanjutkan.');
                        return;
                    }

                    // Show loading indicator
                    this.disabled = true;
                    if (paymentLoading) paymentLoading.classList.remove('hidden');

                    // Prepare request data
                    const requestData = {
                        auto_renew: autoRenewCheckbox ? autoRenewCheckbox.checked : false,
                        use_duration: useDurationCheckbox ? useDurationCheckbox.checked : true
                    };

                    // Add duration_id if selected and duration checkbox is checked
                    if (selectedDurationId && useDurationCheckbox && useDurationCheckbox.checked) {
                        requestData.duration_id = selectedDurationId;
                    }
                    
                    // Create subscription
                    fetch(`/subscriptions/purchase/${planId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    })
                    .then(response => {
                        if (!response.ok) {
                            // Get the response text for better error debugging
                            return response.text().then(text => {
                                console.error('Error response:', text);
                                throw new Error('Failed to create subscription: ' + response.status);
                            });
                        }
                        return response.json();
                    })
                    .then(subscription => {
                        console.log('Subscription created:', subscription);
                        // Get payment gateway
                        ensurePaymentGatewayLoaded().then(PaymentGateway => {
                            // Cek payment gateway
                            fetch('/payment-gateway/config')
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        const gateway = PaymentGateway.init(data.gateway, data.config);
                                        
                                        // Buat invoice/payment berdasarkan gateway
                                        if (data.gateway === 'xendit') {
                                            return createXenditInvoice(subscription.id, gateway);
                                        } else {
                                            throw new Error('Unknown payment gateway: ' + data.gateway);
                                        }
                                    } else {
                                        throw new Error(data.message || 'Failed to get payment gateway configuration');
                                    }
                                })
                                .catch(error => {
                                    console.error('Error:', error);
                                    alert('Terjadi kesalahan saat memproses pembayaran. Silakan coba lagi.');
                                    
                                    // Reset UI
                                    if (newPayButton) newPayButton.disabled = false;
                                    if (paymentLoading) paymentLoading.classList.add('hidden');
                                });
                        });
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Terjadi kesalahan saat membuat langganan. Silakan coba lagi.');
                        
                        // Reset UI
                        if (newPayButton) newPayButton.disabled = false;
                        if (paymentLoading) paymentLoading.classList.add('hidden');
                    });
                });
            }
        }
        
        // Fungsi untuk memastikan PaymentGateway sudah dimuat
        function ensurePaymentGatewayLoaded() {
            return new Promise((resolve, reject) => {
                if (window.PaymentGateway) {
                    resolve(window.PaymentGateway);
                } else {
                    // If not loaded yet, wait a bit and check again
                    let attempts = 0;
                    const checkInterval = setInterval(() => {
                        attempts++;
                        if (window.PaymentGateway) {
                            clearInterval(checkInterval);
                            resolve(window.PaymentGateway);
                        } else if (attempts >= 10) {
                            clearInterval(checkInterval);
                            reject(new Error('Failed to load PaymentGateway'));
                        }
                    }, 200);
                }
            });
        }
        
        // Fungsi untuk membuat Xendit invoice
        function createXenditInvoice(subscriptionId, gateway) {
            return fetch('/payment-gateway/xendit/invoice', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    subscription_id: subscriptionId
                })
            })
            .then(response => {
                if (!response.ok) {
                    return response.text().then(text => {
                        console.error('Error response:', text);
                        throw new Error('Failed to create Xendit invoice: ' + response.status);
                    });
                }
                return response.json();
            })
            .then(paymentData => {
                handlePaymentResponse(paymentData, gateway, 'xendit');
                return paymentData;
            });
        }
        
        // Fungsi untuk menangani respons pembayaran
        function handlePaymentResponse(paymentData, gateway, gatewayName) {
            const payButton = document.getElementById('pay-button');
            const paymentLoading = document.getElementById('payment-loading');
            
            // Reset UI
            if (payButton) payButton.disabled = false;
            if (paymentLoading) paymentLoading.classList.add('hidden');
            
            if (paymentData.email_sent) {
                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'bg-green-100 dark:bg-green-900/30 border-l-4 border-green-500 dark:border-green-400 text-green-700 dark:text-green-400 p-4 mb-6 rounded mt-4';
                successMessage.innerHTML = `
                    <p class="font-medium">Pembayaran sedang diproses!</p>
                    <p class="mt-2">Link pembayaran telah dikirim ke email Anda. Silakan cek email Anda untuk melanjutkan pembayaran. Jika tidak menerima email dalam beberapa menit, periksa folder spam atau klik tombol di bawah.</p>
                `;

                // Add button to open payment link directly
                const buttonContainer = document.createElement('div');
                buttonContainer.className = 'mt-4';
                buttonContainer.innerHTML = `
                    <button type="button" id="open-payment-link" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text font-medium py-2 px-4 rounded-lg transition-colors duration-150 shadow-sm hover:shadow transform hover:scale-105">
                        <i class="fas fa-external-link-alt mr-1"></i> Lanjutkan Pembayaran
                    </button>
                `;

                successMessage.appendChild(buttonContainer);

                // Insert message after payment button
                if (paymentLoading) {
                    const paymentContainer = paymentLoading.parentNode;
                    paymentContainer.appendChild(successMessage);
                }

                // Add event listener to open payment link
                document.getElementById('open-payment-link').addEventListener('click', function() {
                    if (gatewayName === 'xendit' && paymentData.invoice_url) {
                        window.open(paymentData.invoice_url, '_blank');
                    } else {
                        alert('Tidak dapat menemukan link pembayaran yang valid. Silakan cek email Anda atau hubungi administrator.');
                    }
                });

                // Change pay button text
                if (payButton) {
                    payButton.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Pembayaran Diproses';
                    payButton.disabled = true;
                    payButton.className = 'w-full bg-green-500 dark:bg-green-600 text-white font-medium py-3 px-4 rounded-lg cursor-not-allowed';
                }
            } else if (paymentData.invoice_url) {
                // Redirect to payment page
                gateway.pay(paymentData.invoice_url);
            } else {
                alert('Terjadi kesalahan saat memproses pembayaran. Data pembayaran tidak valid.');
            }
        }
    </script>
@endsection
