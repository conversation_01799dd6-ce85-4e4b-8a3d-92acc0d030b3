import "./bootstrap";
import "../css/loading.css";
import "../css/confirm-alert.css";
import "./confirm.js";
import "./ahsrab.js";
import "./volumeContextMenu.js";
import "./pagination.js";

// Tooltip functionality removed

// Flag to prevent multiple setup calls in short succession
let isSettingUp = false;

// Function to setup theme toggles
function setupThemeToggles() {
    if (isSettingUp) return; // Prevent concurrent setup calls
    isSettingUp = true;

    // Add click event listener to all theme toggle buttons except visitor toggle
    // (visitor toggle has its own special handler)
    const themeToggleBtns = document.querySelectorAll(
        "#theme-toggle, #mobile-theme-toggle"
    );
    themeToggleBtns.forEach((toggleBtn) => {
        // Skip if already has event listener attached (using data attribute marker)
        if (toggleBtn.dataset.hasEventListener === "true") return;

        toggleBtn.dataset.hasEventListener = "true";
        console.log("Adding click listener to", toggleBtn.id);

        toggleBtn.addEventListener("click", function (e) {
            e.preventDefault();
            e.stopPropagation();
            console.log(toggleBtn.id + " clicked");

            // Toggle dark class on document element
            document.documentElement.classList.toggle("dark");

            // Update toggle icon positions
            updateToggleIcons();

            // Save theme preference
            if (document.documentElement.classList.contains("dark")) {
                localStorage.setItem("theme", "dark");
            } else {
                localStorage.setItem("theme", "light");
            }
        });
    });

    isSettingUp = false; // Reset flag
}

// Function to update all toggle icons based on current theme
function updateToggleIcons() {
    const isDark = document.documentElement.classList.contains("dark");
    console.log("Updating toggle icons, isDark:", isDark);

    // Update all toggle icons
    document.querySelectorAll("#theme-toggle-icon").forEach((icon) => {
        if (isDark) {
            icon.style.transform = "translateX(28px)";
        } else {
            icon.style.transform = "translateX(0)";
        }
        console.log("Updated #theme-toggle-icon");
    });

    document.querySelectorAll("#theme-toggle-visitor-icon").forEach((icon) => {
        if (isDark) {
            icon.style.transform = "translateX(16px)";
        } else {
            icon.style.transform = "translateX(0)";
        }
        console.log(
            "Updated #theme-toggle-visitor-icon, transform:",
            icon.style.transform
        );
    });

    document.querySelectorAll("#mobile-theme-toggle-icon").forEach((icon) => {
        if (isDark) {
            icon.style.transform = "translateX(28px)";
        } else {
            icon.style.transform = "translateX(0)";
        }
        console.log("Updated #mobile-theme-toggle-icon");
    });

    // Force repaint to ensure transforms are applied
    document.body.style.display = "none";
    document.body.offsetHeight; // Trigger a reflow
    document.body.style.display = "";
}

// Theme Toggle Functionality
document.addEventListener("DOMContentLoaded", function () {
    // Check for saved theme preference or use system preference
    const darkModeMediaQuery = window.matchMedia(
        "(prefers-color-scheme: dark)"
    );
    const savedTheme = localStorage.getItem("theme");

    // Set initial theme based on saved preference or system preference
    if (savedTheme === "dark" || (!savedTheme && darkModeMediaQuery.matches)) {
        document.documentElement.classList.add("dark");
    } else {
        document.documentElement.classList.remove("dark");
    }

    // Update all toggle icons based on current theme
    updateToggleIcons();

    // Setup theme toggles initially
    setupThemeToggles();

    // Add direct click handler for visitor toggle
    const visitorToggle = document.getElementById("theme-toggle-visitor");
    if (visitorToggle) {
        visitorToggle.addEventListener("click", function (e) {
            e.preventDefault();
            e.stopPropagation();
            console.log("Visitor toggle clicked directly");

            // Toggle dark class on document element
            document.documentElement.classList.toggle("dark");

            // Update toggle icon positions
            updateToggleIcons();

            // Save theme preference
            if (document.documentElement.classList.contains("dark")) {
                localStorage.setItem("theme", "dark");
            } else {
                localStorage.setItem("theme", "light");
            }
        });
    }

    // Log for debugging
    console.log("Theme toggles initialized");
    console.log(
        "Current theme:",
        document.documentElement.classList.contains("dark") ? "dark" : "light"
    );
    console.log(
        "Toggle buttons found:",
        document.querySelectorAll('[id^="theme-toggle"]').length
    );
});

// Use a more efficient approach without MutationObserver to avoid infinite loops
// Check periodically for new toggles but with decreasing frequency
let checkCount = 0;
const checkIntervals = [100, 500, 1000, 2000];

function scheduleNextCheck() {
    if (checkCount < checkIntervals.length) {
        setTimeout(() => {
            setupThemeToggles();
            checkCount++;
            scheduleNextCheck();
        }, checkIntervals[checkCount]);
    }
}

scheduleNextCheck();
