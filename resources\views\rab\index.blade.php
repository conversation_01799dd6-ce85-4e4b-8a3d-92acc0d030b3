@extends('layouts.app')

@section('content')
    <div class="container mx-auto max-w-6xl p-6">
        <!-- Hidden input untuk viewOnly mode -->
        @if(session('view_only'))
            <input type="hidden" id="viewOnly" value="1">
        @endif

        <div class="flex justify-between items-center mb-4">
            <!-- Tombol untuk membuka modal kategori -->
            <button type="button" onclick="{{ !session('view_only') ? 'openKategoriModal()' : 'showLockedToast()' }}"
                class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105 {{ session('view_only') ? 'locked-btn' : '' }}">
                <i class="fas fa-list"></i> Sesuaikan <PERSON>gor<PERSON>
            </button>
            
            <!-- Fitur Search -->
            <div class="flex items-center space-x-2">
                <label for="searchRAB" class="text-sm font-medium">Cari Data:</label>
                <input type="text" id="searchRAB" placeholder="Cari..." class="border px-3 py-2 rounded"
                    oninput="filterRABTable()">
            </div>
        </div>

        <!-- Tabel RAB -->
        <div class="overflow-y-auto max-h-[530px]">
            <table class="text-sm min-w-full bg-white border">
                <thead class="bg-blue-200 sticky top-0 z-10">
                    <tr>
                        <th class="py-2 px-4 border">No.</th>
                        <th class="py-2 px-4 border">Uraian Pekerjaan</th>
                        <th class="py-2 px-4 border">Volume</th>
                        <th class="py-2 px-4 border">Satuan</th>
                        <th class="py-2 px-4 border">Harga Satuan</th>
                        <th class="py-2 px-4 border">Harga</th>
                        <th class="py-2 px-4 border">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($kategoriPekerjaans as $catIndex => $kategori)
                        <!-- Baris Kategori (nomor huruf) -->
                        <tr class="bg-blue-100 font-semibold">
                            <td class="py-2 px-4 border">{{ chr(65 + $catIndex) }}</td>
                            <td colspan="5" class="py-2 px-4 border">{{ $kategori->nama_kategori }}</td>
                            <!-- Kolom kosong untuk kategori -->
                            <td class="py-2 px-2 border text-center">
                                @if (!session('view_only'))
                                    <button type="button" onclick="tambahItemPekerjaan({{ $kategori->id }})"
                                        class="bg-light-accent hover:bg-light-accent/80 text-white px-2 py-1 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                                        <i class="fas fa-plus-circle"></i>
                                    </button>
                                @endif
                            </td>
                        </tr>
                        <!-- Baris Item Pekerjaan untuk kategori (nomor angka) -->
                        @if ($kategori->items && $kategori->items->count() > 0)
                            @foreach ($kategori->items as $itemIndex => $item)
                                <tr data-item-id="{{ $item->id }}" data-ahs_id="{{ $item->ahs_id }}"
                                    data-kode="{{ $item->kode }}" data-sumber="{{ $item->sumber }}">
                                    <!-- Sel-sel tabel -->
                                    <td class="py-2 px-4 border pl-8">{{ $itemIndex + 1 }}</td>
                                    <td class="py-2 px-4 border pl-8">{{ $item->uraian_item }}</td>
                                    <td class="py-2 px-4 border volume" data-volume="{{ $item->volume }}">
                                        {{ number_format($item->volume, 2) }}</td>
                                    <td class="py-2 px-4 border">{{ $item->satuan }}</td>
                                    <td class="py-2 px-4 border harga-satuan" data-hargasatuan="{{ $item->harga_satuan }}">
                                        <span class="float-left">Rp.</span>
                                        <span
                                            class="float-right">{{ number_format($item->harga_satuan, 2, ',', '.') }}</span>
                                    </td>
                                    <td class="py-2 px-4 border harga-total" data-harga-total="{{ $item->harga_total }}">
                                        <span class="float-left">Rp.</span>
                                        <span
                                            class="float-right">{{ number_format($item->harga_total, 2, ',', '.') }}</span>
                                    </td>
                                    <td class="py-2 px-4 border text-center">
                                        @if (!session('view_only'))
                                            <button type="button"
                                                class="action-btn-item px-4 text-blue-500 hover:text-blue-700 p-1 rounded-full hover:bg-blue-200"
                                                data-item-id="{{ $item->id }}" data-kategori-id="{{ $kategori->id }}"
                                                data-harga-satuan="{{ $item->harga_satuan }}"
                                                data-satuan="{{ $item->satuan }}"
                                                onclick="handleItemContextMenu(event, this)">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        @endif
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Tambahan baris untuk Jumlah Harga, PPN, Total Harga, dan Terbilang -->
        <div>
            <table class="min-w-full bg-blue-200 border">
                <tbody>
                    <!-- Baris Jumlah Harga -->
                    <tr>
                        <td class="py-2 px-4 border font-bold text-right">Jumlah Harga</td>
                        <td class="py-2 px-4 border font-bold text-left" id="jumlahHarga">Rp. 0</td>
                    </tr>
                    <!-- Baris PPN -->
                    <tr>
                        <td class="py-2 px-4 border font-bold text-right">
                            PPN
                            @if (!session('view_only'))
                                <input type="number" id="ppnInput" value="{{ $project->ppn }}"
                                    class="w-16 rounded px-2 py-1 text-right ml-2 bg-blue-200" min="0" max="100"
                                    step="0.01" oninput="updatePPN(this.value)">
                            @else
                                <span id="ppnInput" class="ml-2">{{ $project->ppn }}</span>
                            @endif
                            %
                        </td>
                        <td class="py-2 px-4 border font-bold text-left" id="ppnHarga">Rp. 0</td>
                    </tr>
                    <!-- Baris Total Harga -->
                    <tr>
                        <td class="py-2 px-4 border font-bold text-right">Total Harga</td>
                        <td class="py-2 px-4 border font-bold text-left" id="totalHarga">Rp. 0</td>
                    </tr>
                    <!-- Baris Dibulatkan -->
                    <tr>
                        <td class="py-2 px-4 border font-bold text-right">Dibulatkan</td>
                        <td class="py-2 px-4 border font-bold text-left" id="dibulatkanHarga">Rp. 0</td>
                    </tr>
                    <!-- Baris Terbilang -->
                    <tr>
                        <td colspan="2" class="py-2 px-4 border font-bold text-center">
                            Terbilang : <span id="terbilangHarga" class="italic">Nol Rupiah</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Tambahkan input untuk project_id -->
    {{-- <input type="hidden" id="projectIdInput" value="{{ request('project_id') }}"> --}}

    <!-- Memanggil component modal RAB -->
    <x-modal-rab />
    <x-modal-volume />
    <x-ahs-modal />

    <!-- Include Preview Download Modal -->
    @include('modals.preview-download')
@endsection

@section('scripts')
    <script>
        // Inisialisasi variabel global dari controller
        window.bahanItems = @json($bahanItems);
        window.upahItems = @json($upahItems);
        window.alatItems = @json($alatItems);
        window.ahsBaseUrl = '{{ url('ahs') }}';
        window.ahsDetailStoreRoute = '{{ route('ahs.detail.store') }}';
        window.csrfToken = '{{ csrf_token() }}';
        window.viewOnly = {{ session('view_only') ? 'true' : 'false' }};

        // Check if we should show the preview download modal
        @if (isset($showPreviewDownload) && $showPreviewDownload)
            document.addEventListener('DOMContentLoaded', function() {
                // Menggunakan fungsi openPreviewDownloadModal dari preview-download-modal.js
                if (typeof window.openPreviewDownloadModal === 'function') {
                    window.openPreviewDownloadModal();
                }
            });
        @endif

        function filterRABTable() {
            const input = document.getElementById("searchRAB").value.toLowerCase();
            const rows = document.querySelectorAll("tbody tr[data-item-id]");
            rows.forEach(row => {
                const uraian = row.cells[1].innerText.toLowerCase();
                const satuan = row.cells[3].innerText.toLowerCase();
                row.style.display = (uraian.includes(input) || satuan.includes(input)) ? "" : "none";
            });
        }

        function numberToWords(number) {
            const satuan = ["", "Satu", "Dua", "Tiga", "Empat", "Lima", "Enam", "Tujuh", "Delapan", "Sembilan"];
            const belasan = ["Sepuluh", "Sebelas", "Dua Belas", "Tiga Belas", "Empat Belas", "Lima Belas", "Enam Belas",
                "Tujuh Belas", "Delapan Belas", "Sembilan Belas"
            ];
            const puluhan = ["", "", "Dua Puluh", "Tiga Puluh", "Empat Puluh", "Lima Puluh", "Enam Puluh", "Tujuh Puluh",
                "Delapan Puluh", "Sembilan Puluh"
            ];
            const ribuan = ["", "Ribu", "Juta", "Miliar", "Triliun"];

            if (number === 0) return "Nol Rupiah";

            let words = "";
            let i = 0;

            while (number > 0) {
                const chunk = number % 1000;
                if (chunk > 0) {
                    let chunkWords = "";
                    const hundreds = Math.floor(chunk / 100);
                    const remainder = chunk % 100;

                    if (hundreds > 0) {
                        chunkWords += (hundreds === 1 ? "Seratus" : satuan[hundreds] + " Ratus") + " ";
                    }

                    if (remainder > 0) {
                        if (remainder < 10) {
                            chunkWords += satuan[remainder] + " ";
                        } else if (remainder < 20) {
                            chunkWords += belasan[remainder - 10] + " ";
                        } else {
                            chunkWords += puluhan[Math.floor(remainder / 10)] + " " + satuan[remainder % 10] + " ";
                        }
                    }

                    words = chunkWords + ribuan[i] + " " + words;
                }

                number = Math.floor(number / 1000);
                i++;
            }

            return words.trim() + " Rupiah";
        }

        function calculateTotals() {
            const rows = document.querySelectorAll("tbody tr[data-item-id]");
            let totalHarga = 0;

            rows.forEach(row => {
                const harga = parseFloat(row.cells[5].innerText.replace(/[^0-9,-]+/g, '').replace(',', '.')) || 0;
                totalHarga += harga;
            });

            const jumlahHarga = totalHarga;
            const ppn = parseFloat(document.getElementById("ppnInput").value) || 0;
            const ppnHarga = (jumlahHarga * ppn) / 100;
            const totalHargaFinal = jumlahHarga + ppnHarga;
            const dibulatkanHarga = Math.floor(totalHargaFinal / 1000) * 1000;

            // Update semua elemen HTML termasuk Terbilang
            document.getElementById("jumlahHarga").innerText = formatRupiah(jumlahHarga);
            document.getElementById("ppnHarga").innerText = formatRupiah(ppnHarga);
            document.getElementById("totalHarga").innerText = formatRupiah(totalHargaFinal);
            document.getElementById("dibulatkanHarga").innerText = formatRupiah(dibulatkanHarga);
            document.getElementById("terbilangHarga").innerText = numberToWords(dibulatkanHarga);

            // Send data to backend immediately
            updateTotalsInDatabase(jumlahHarga, totalHargaFinal, dibulatkanHarga);
        }

        function updateTotalsInDatabase(jumlahHarga, totalHargaFinal, dibulatkanHarga) {
            // Skip database update in view-only mode
            if (window.viewOnly) {
                return;
            }

            fetch(`/rab/update-totals`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        jumlah_harga: jumlahHarga,
                        total_harga: totalHargaFinal,
                        dibulatkan: dibulatkanHarga
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.error('Failed to save totals:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error saving totals:', error);
                });
        }

        function updatePPN(value) {
            // Skip database update in view-only mode
            if (window.viewOnly) {
                // Just update the display and recalculate totals
                calculateTotals();
                return;
            }

            // Update PPN in the project
            fetch(`/projects/{{ $project->id }}/update-ppn`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        ppn: value
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update PPN display
                        if (!window.viewOnly) {
                            document.getElementById('ppnInput').value = data.ppn;
                        }
                        // Recalculate totals which will update all values including Terbilang
                        calculateTotals();
                    } else {
                        alert(data.message || 'Gagal memperbarui PPN');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Terjadi kesalahan saat memperbarui PPN');
                });
        }

        // Fungsi untuk memformat angka sebagai Rupiah
        function formatRupiah(angka) {
            return 'Rp. ' + angka.toLocaleString('id-ID', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        // Menambahkan event listener untuk perubahan PPN
        document.getElementById('ppnInput').addEventListener('input', function(e) {
            updatePPN(this.value);
        });

        // Recalculate totals on page load and whenever the table changes
        document.addEventListener("DOMContentLoaded", calculateTotals);
        const observer = new MutationObserver(calculateTotals);
        observer.observe(document.querySelector("tbody"), {
            childList: true,
            subtree: true
        });
    </script>
    @vite(['resources/js/ahsp.js', 'resources/js/rab.js', 'resources/js/volume.js', 'resources/js/ahsrab.js', 'resources/js/pdf-export.js', 'resources/js/preview-download-modal.js'])
@endsection
