@extends('layouts.app')

@section('content')
    <div class="container mx-auto max-w-[1080px] p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-light-text dark:text-dark-text">Detail Customer</h1>
            <a href="{{ route('customers.index') }}"
                class="bg-light-accent hover:bg-light-accent/80 transition-colors text-white px-4 py-2 rounded">
                <i class="fas fa-arrow-left mr-2"></i> Kembali
            </a>
        </div>

        <!-- Customer Info Card -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden transition-all duration-200 mb-8">
            <div class="bg-blue-600 dark:bg-blue-700 p-4">
                <h2 class="text-white text-lg font-semibold">Informasi Customer</h2>
            </div>
            <div class="p-6">
                <div class="flex flex-col md:flex-row gap-8 mb-6">
                    <!-- Foto Profil -->
                    <div class="flex-shrink-0 flex flex-col items-center">
                        <div
                            class="w-40 h-40 rounded-full overflow-hidden border-4 border-light-accent dark:border-dark-accent mb-3 shadow-lg">
                            @if ($customer->photo)
                                <img src="{{ asset('storage/' . $customer->photo) }}"
                                    alt="Foto Profil {{ $customer->name }}" class="w-full h-full object-cover">
                            @else
                                <img src="@gravatar($customer->email, 200, 'identicon')" alt="Foto Profil {{ $customer->name }}"
                                    class="w-full h-full object-cover">
                            @endif
                        </div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Profil {{ $customer->name }}</p>
                    </div>

                    <!-- Informasi Customer -->
                    <div class="flex-grow grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Nama</p>
                            <p class="text-lg font-medium text-gray-800 dark:text-gray-200">{{ $customer->name }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Email</p>
                            <p class="text-lg font-medium text-gray-800 dark:text-gray-200">{{ $customer->email }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Alamat</p>
                            <p class="text-lg font-medium text-gray-800 dark:text-gray-200">
                                {{ $customer->address ?: 'Belum diatur' }}
                            </p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Nomor Telepon</p>
                            <p class="text-lg font-medium text-gray-800 dark:text-gray-200">
                                {{ $customer->phone ?: 'Belum diatur' }}
                            </p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Tanggal Daftar</p>
                            <p class="text-lg font-medium text-gray-800 dark:text-gray-200">
                                {{ $customer->created_at->format('d M Y H:i') }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Jumlah Proyek</p>
                            <p class="text-lg font-medium text-gray-800 dark:text-gray-200">{{ count($projects) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Projects List -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-light-accent dark:bg-dark-accent p-4">
                <h2 class="text-white text-lg font-semibold">Daftar Proyek</h2>
            </div>
            <div class="p-6">
                @if (count($projects) > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full border border-gray-200 dark:border-gray-700 rounded-lg">
                            <thead class="bg-blue-200 dark:bg-dark-table-header">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700">
                                        No.</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700">
                                        Nama Proyek</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700">
                                        Lokasi</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700">
                                        Pemilik Proyek</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700">
                                        Tahun</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700">
                                        Tanggal Dibuat</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach ($projects as $index => $project)
                                    <tr class="hover:bg-gray-50 dark:hover:bg-dark-hover transition-colors duration-150">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
                                            {{ $index + 1 }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-gray-200">
                                            {{ $project->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                                            {{ $project->location }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                                            {{ $project->project_owner }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                                            {{ $project->year }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                                            {{ $project->created_at->format('d M Y') }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                        Customer ini belum memiliki proyek
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Modal Customer -->
    <div id="customerModal" tabindex="-1" aria-hidden="true"
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
        <div class="relative w-full max-w-md mx-auto my-auto">
            <!-- Modal content -->
            <div class="relative bg-white dark:bg-dark-card rounded-lg shadow border-2 border-light-accent dark:border-dark-accent">
                <!-- Modal header -->
                <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10 rounded-t-lg">
                    <div class="flex items-center justify-between">
                        <h3 class="text-white dark:text-dark-text text-lg font-semibold" id="customerModalTitle">
                            Edit Customer
                        </h3>
                        <button type="button"
                            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none"
                            data-modal-hide="customerModal">
                            <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" aria-hidden="true" fill="none" viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                </div>
                <!-- Modal body -->
                <div class="p-6 space-y-6 overflow-y-auto" style="scrollbar-width: thin;">
                    <form id="customerForm">
                        @csrf
                        <input type="hidden" id="customerId" value="{{ $customer->id }}">
                        <input type="hidden" id="customerMethod" value="PUT">

                        <div class="mb-4">
                            <label for="name"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nama</label>
                            <input type="text" id="name" name="name"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                                placeholder="Masukkan nama customer" value="{{ $customer->name }}" required>
                        </div>
                        <div class="mb-4">
                            <label for="email"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Email</label>
                            <input type="email" id="email" name="email"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                                placeholder="Masukkan email customer" value="{{ $customer->email }}" required>
                        </div>
                        <div class="mb-4">
                            <label for="password"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Password</label>
                            <input type="password" id="password" name="password"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                                placeholder="Masukkan password">
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Kosongkan jika tidak ingin mengubah
                                password</p>
                        </div>
                    </form>
                </div>
                <!-- Modal footer -->
                <div
                    class="flex items-center justify-end p-6 space-x-2 border-t border-gray-200 dark:border-gray-600">
                    <button data-modal-hide="customerModal" type="button"
                        class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600">Batal</button>
                    <button type="button" onclick="saveCustomer()"
                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Simpan</button>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('scripts')
    @vite(['resources/js/customer.js'])
@endsection
