@if ($paginator->hasPages())
    <nav role="navigation" aria-label="Pagination Navigation" class="flex justify-center my-4">
        <div class="inline-flex items-center justify-center space-x-1 sm:space-x-2 rounded-lg bg-white dark:bg-gray-800 p-1.5 sm:p-2 shadow-sm">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <span class="relative inline-flex items-center justify-center w-8 h-8 text-sm bg-gray-200 text-gray-500 dark:bg-gray-700 dark:text-gray-400 rounded-full cursor-not-allowed">
                    <i class="fas fa-chevron-left text-xs"></i>
                </span>
            @else
                <a href="{{ $paginator->previousPageUrl() }}" rel="prev" class="relative inline-flex items-center justify-center w-8 h-8 text-sm bg-light-accent text-white dark:bg-dark-accent rounded-full hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-chevron-left text-xs"></i>
                </a>
            @endif

            {{-- First Page Link --}}
            @if($paginator->currentPage() > 3)
                <a href="{{ $paginator->url(1) }}" class="relative inline-flex items-center justify-center w-8 h-8 text-sm bg-white text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-full hover:bg-light-accent hover:text-white dark:hover:bg-dark-accent transition-all duration-200 transform hover:scale-105">
                    1
                </a>
                
                @if($paginator->currentPage() > 4)
                    <span class="relative inline-flex items-center justify-center w-8 h-8 text-sm text-gray-500 dark:text-gray-400">
                        ...
                    </span>
                @endif
            @endif

            {{-- Pagination Elements --}}
            @foreach ($elements as $element)
                {{-- Array Of Links --}}
                @if (is_array($element))
                    @foreach ($element as $page => $url)
                        {{-- Show pages around current page --}}
                        @if ($page == $paginator->currentPage())
                            <span class="relative inline-flex items-center justify-center w-8 h-8 text-sm font-bold bg-light-accent text-white dark:bg-dark-accent rounded-full shadow-md transform scale-110">
                                {{ $page }}
                            </span>
                        @elseif ($page === $paginator->currentPage() + 1 || $page === $paginator->currentPage() - 1 || $page === $paginator->currentPage() + 2 || $page === $paginator->currentPage() - 2)
                            <a href="{{ $url }}" class="relative inline-flex items-center justify-center w-8 h-8 text-sm bg-white text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-full hover:bg-light-accent hover:text-white dark:hover:bg-dark-accent transition-all duration-200 transform hover:scale-105">
                                {{ $page }}
                            </a>
                        @endif
                    @endforeach
                @endif
            @endforeach

            {{-- Last Page Link --}}
            @if($paginator->currentPage() < $paginator->lastPage() - 2)
                @if($paginator->currentPage() < $paginator->lastPage() - 3)
                    <span class="relative inline-flex items-center justify-center w-8 h-8 text-sm text-gray-500 dark:text-gray-400">
                        ...
                    </span>
                @endif
                
                <a href="{{ $paginator->url($paginator->lastPage()) }}" class="relative inline-flex items-center justify-center w-8 h-8 text-sm bg-white text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-full hover:bg-light-accent hover:text-white dark:hover:bg-dark-accent transition-all duration-200 transform hover:scale-105">
                    {{ $paginator->lastPage() }}
                </a>
            @endif

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <a href="{{ $paginator->nextPageUrl() }}" rel="next" class="relative inline-flex items-center justify-center w-8 h-8 text-sm bg-light-accent text-white dark:bg-dark-accent rounded-full hover:bg-light-accent/80 dark:hover:bg-dark-accent/80 transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-chevron-right text-xs"></i>
                </a>
            @else
                <span class="relative inline-flex items-center justify-center w-8 h-8 text-sm bg-gray-200 text-gray-500 dark:bg-gray-700 dark:text-gray-400 rounded-full cursor-not-allowed">
                    <i class="fas fa-chevron-right text-xs"></i>
                </span>
            @endif
        </div>
    </nav>
@endif
