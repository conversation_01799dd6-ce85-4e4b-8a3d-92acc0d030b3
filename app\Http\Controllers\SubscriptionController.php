<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionDuration;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of available subscription plans.
     */
    public function index()
    {
        $plans = \App\Models\SubscriptionPlan::where('is_active', true)
            ->orderBy('price')
            ->get();

        $user = Auth::user();
        $currentSubscription = $user->currentSubscription;

        return view('subscriptions.index', compact('plans', 'currentSubscription', 'user'));
    }

    /**
     * Show the subscription details.
     */
    public function show()
    {
        $user = Auth::user();
        $subscription = $user->currentSubscription;

        if (!$subscription) {
            return redirect()->route('subscriptions.index')
                ->with('error', 'Anda belum memiliki langganan aktif.');
        }

        $payments = $subscription->payments()
            ->orderBy('created_at', 'desc')
            ->get();

        return view('subscriptions.show', compact('subscription', 'payments', 'user'));
    }

    /**
     * Show the checkout page for a subscription plan.
     */
    public function checkout($id)
    {
        $plan = SubscriptionPlan::findOrFail($id);

        if (!$plan->is_active) {
            return redirect()->route('subscriptions.index')
                ->with('error', 'Paket langganan tidak tersedia.');
        }

        // Ambil durasi berlangganan yang aktif
        $plan->activeDurations = SubscriptionDuration::where('subscription_plan_id', $plan->id)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('duration_months')
            ->get();

        // Tambahkan formatted_description dan formatted_total_price ke setiap durasi
        foreach ($plan->activeDurations as $duration) {
            // Buat deskripsi yang diformat
            if ($duration->promo_type === 'discount') {
                $duration->formatted_description = "Diskon {$duration->promo_value}% untuk langganan {$duration->duration_months} bulan";
            } else {
                $duration->formatted_description = "Gratis {$duration->promo_value} bulan untuk langganan {$duration->duration_months} bulan";
            }

            // Hitung dan format total harga
            $totalPrice = $duration->calculateTotalPrice();
            $duration->formatted_total_price = 'Rp ' . number_format($totalPrice, 0, ',', '.');
        }

        // Tentukan tanggal mulai berlangganan
        $user = Auth::user();
        $startDate = now();
        $hasActiveSubscription = false;
        $isOnTrial = false;

        // Cek apakah pengguna memiliki langganan aktif
        if ($user->currentSubscription && $user->currentSubscription->isActive() && !$user->currentSubscription->is_trial) {
            $startDate = $user->currentSubscription->end_date;
            $hasActiveSubscription = true;
        }
        // Cek apakah pengguna sedang dalam masa uji coba
        elseif ($user->trial_ends_at && $user->trial_ends_at > now()) {
            $startDate = $user->trial_ends_at;
            $isOnTrial = true;
        }

        // Format tanggal untuk ditampilkan
        $formattedStartDate = $startDate->format('d F Y');

        // Cek jika request adalah AJAX
        if (request()->ajax()) {
            return view('subscriptions.checkout', compact('plan', 'hasActiveSubscription', 'isOnTrial', 'formattedStartDate'));
        }

        return view('subscriptions.checkout', compact('plan', 'hasActiveSubscription', 'isOnTrial', 'formattedStartDate'));
    }

    /**
     * Process the subscription purchase.
     */
    public function purchase(Request $request, $id)
    {
        $plan = SubscriptionPlan::findOrFail($id);

        if (!$plan->is_active) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Paket langganan tidak tersedia.'
                ], 400);
            }

            return redirect()->route('subscriptions.index')
                ->with('error', 'Paket langganan tidak tersedia.');
        }

        // Buat subscription baru
        $user = Auth::user();

        // Tentukan tanggal mulai berdasarkan status langganan saat ini
        $startDate = now();
        $startDateInfo = ""; // Informasi tambahan untuk deskripsi

        // Cek apakah pengguna memiliki langganan aktif (bukan trial)
        if ($user->currentSubscription && $user->currentSubscription->isActive() && !$user->currentSubscription->is_trial) {
            // Jika pengguna memiliki langganan aktif, mulai langganan baru setelah langganan saat ini berakhir
            $startDate = $user->currentSubscription->end_date;
            $startDateInfo = " (dimulai setelah langganan saat ini berakhir pada " . $startDate->format('d F Y') . ")";
        }
        // Cek apakah pengguna sedang dalam masa uji coba
        elseif ($user->trial_ends_at && $user->trial_ends_at > now()) {
            // Jika pengguna sedang dalam masa uji coba, mulai langganan baru setelah masa uji coba berakhir
            $startDate = $user->trial_ends_at;
            $startDateInfo = " (dimulai setelah masa uji coba berakhir pada " . $startDate->format('d F Y') . ")";
        }

        // Cek apakah ingin menggunakan durasi berlangganan atau hanya 1 bulan
        $useDuration = $request->has('use_duration') && $request->boolean('use_duration');

        // Default durasi: 1 bulan
        $durationId = null;
        $duration = null;
        $durationDays = 30; // 1 bulan sebagai default
        $durationMonths = 1; // 1 bulan

        if ($useDuration) {
            // Cek apakah ada durasi berlangganan yang dipilih
            $durationId = $request->input('duration_id');

        if ($durationId) {
            $duration = SubscriptionDuration::where('id', $durationId)
                ->where('subscription_plan_id', $plan->id)
                ->where('is_active', true)
                ->first();

            if ($duration) {
                // Hitung durasi dalam hari
                $durationDays = $duration->duration_months * 30;
                    $durationMonths = $duration->duration_months;
                } else {
                    // Jika durasi tidak ditemukan, gunakan durasi default dari paket
                    $durationDays = $plan->duration_days;
                    $durationMonths = $plan->duration_in_months;
                }
            } else {
                // Jika tidak ada durasi yang dipilih, gunakan durasi default dari paket
                $durationDays = $plan->duration_days;
                $durationMonths = $plan->duration_in_months;
            }
        }

        // Hitung tanggal berakhir berdasarkan tanggal mulai
        $endDate = (clone $startDate)->addDays($durationDays);

        $subscription = new \App\Models\Subscription([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'duration_id' => $duration ? $duration->id : null,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => 'pending', // Akan diubah menjadi 'active' setelah pembayaran berhasil
            'is_trial' => false,
            'auto_renew' => $request->has('auto_renew') || $request->input('auto_renew', false),
        ]);

        $subscription->save();

        // Jika request adalah JSON atau memiliki header Accept: application/json, kembalikan subscription
        if ($request->expectsJson() || $request->header('Accept') === 'application/json') {
            Log::info('Returning JSON response for subscription', ['id' => $subscription->id]);
            return response()->json([
                'success' => true,
                'id' => $subscription->id,
                'message' => 'Subscription created successfully'
            ]);
        }

        // Untuk request non-JSON, validasi input
        $request->validate([
            'payment_method' => 'required|in:bank_transfer,credit_card,e-wallet',
        ]);

        // Buat invoice number
        $invoiceNumber = 'INV-' . date('Ymd') . '-' . str_pad($subscription->id, 5, '0', STR_PAD_LEFT);

        // Buat deskripsi pembayaran
        $paymentDescription = 'Pembayaran untuk paket ' . $plan->name . ' selama ' . $durationMonths . ' bulan';

        // Tambahkan informasi diskon atau bulan gratis jika ada
        if ($duration) {
            if ($duration->promo_type === 'discount') {
                $paymentDescription .= " dengan diskon {$duration->promo_value}%";
            } else {
                $paymentDescription .= " dengan gratis {$duration->promo_value} bulan";
            }
        }

        // Tambahkan informasi tanggal mulai jika berbeda dari sekarang
        if ($startDate->diffInDays(now()) > 0) {
            $paymentDescription .= $startDateInfo;
        }

        // Hitung jumlah yang harus dibayar
        $amount = $plan->price; // Default: harga bulanan

        // Jika menggunakan durasi dan ada durasi yang dipilih
        if ($useDuration && $duration) {
            $amount = $duration->calculateTotalPrice();
        }

        // Buat payment untuk request non-JSON (form submission)
        $payment = new \App\Models\Payment([
            'user_id' => $user->id,
            'subscription_id' => $subscription->id,
            'payment_id' => 'PAY-' . uniqid(),
            'payment_method' => $request->payment_method,
            'amount' => $amount,
            'currency' => 'IDR',
            'status' => 'pending',
            'invoice_number' => $invoiceNumber,
            'description' => $paymentDescription,
        ]);

        $payment->save();

        // Get payment gateway
        $paymentGateway = \App\Models\SystemSetting::getValue('payment_gateway', 'xendit');

        // Log untuk debugging
        \Illuminate\Support\Facades\Log::info('Creating payment', [
            'payment_id' => $payment->id,
            'payment_method' => $payment->payment_method,
            'system_payment_gateway' => $paymentGateway
        ]);

        // Jika menggunakan Xendit, buat invoice dan kirim email
        if ($paymentGateway === 'xendit') {
            try {
                $xenditSecretKey = \App\Models\SystemSetting::getValue('xendit_secret_key');

                if (empty($xenditSecretKey)) {
                    return redirect()->route('payments.show', $payment->id)
                        ->with('error', 'Konfigurasi Xendit belum lengkap. Silakan hubungi administrator.');
                }

                // Set Xendit API URL
                $apiUrl = 'https://api.xendit.co/v2/invoices';

                // Prepare invoice data
                $invoiceData = [
                    'external_id' => $payment->payment_id,
                    'amount' => (int) $payment->amount,
                    'payer_email' => $user->email,
                    'description' => $payment->description,
                    'success_redirect_url' => route('subscriptions.success'),
                    'failure_redirect_url' => route('subscriptions.error'),
                    'callback_url' => route('payments.callback')
                ];

                // Set headers
                $headers = [
                    'Content-Type: application/json',
                    'Authorization: Basic ' . base64_encode($xenditSecretKey . ':')
                ];

                // Initialize cURL
                $ch = curl_init($apiUrl);
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                curl_setopt($ch, CURLOPT_USERPWD, $xenditSecretKey . ':');
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($invoiceData));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

                // Execute cURL
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                // Check if request was successful
                if ($httpCode == 200) {
                    $responseData = json_decode($response, true);
                    $paymentUrl = $responseData['invoice_url'];

                    // Send email with payment link
                    \Illuminate\Support\Facades\Mail::to($user->email)->send(new \App\Mail\PaymentLinkMail(
                        $user,
                        $payment,
                        $subscription,
                        $paymentUrl
                    ));

                    // Log success
                    \Illuminate\Support\Facades\Log::info('Payment link email sent', [
                        'payment_id' => $payment->id,
                        'user_email' => $user->email,
                        'payment_url' => $paymentUrl
                    ]);

                    // Redirect to payment page
                    return redirect()->away($paymentUrl);
                } else {
                    // Log error
                    \Illuminate\Support\Facades\Log::error('Failed to create Xendit invoice', [
                        'payment_id' => $payment->id,
                        'response' => $response,
                        'http_code' => $httpCode
                    ]);
                }
            } catch (\Exception $e) {
                // Log exception
                \Illuminate\Support\Facades\Log::error('Error creating Xendit invoice: ' . $e->getMessage(), [
                    'payment_id' => $payment->id,
                    'exception' => $e
                ]);
            }
        }

        // Redirect ke halaman pembayaran (fallback)
        return redirect()->route('payments.show', $payment->id);
    }

    /**
     * Cancel the current subscription.
     */
    public function cancel(Request $request)
    {
        $user = Auth::user();
        $subscription = $user->currentSubscription;

        if (!$subscription || $subscription->isCancelled()) {
            return redirect()->route('subscriptions.index')
                ->with('error', 'Tidak ada langganan aktif yang dapat dibatalkan.');
        }

        // Validasi input
        $request->validate([
            'cancellation_reason' => 'required|string|max:255',
        ]);

        // Update subscription
        $subscription->cancelled_at = now();
        $subscription->cancellation_reason = $request->cancellation_reason;
        $subscription->auto_renew = false;
        $subscription->save();

        return redirect()->route('subscriptions.show')
            ->with('success', 'Langganan berhasil dibatalkan. Anda masih dapat menggunakan layanan hingga akhir periode langganan.');
    }

    /**
     * Start the trial period for the user.
     */
    public function startTrial()
    {
        $user = Auth::user();

        if ($user->has_used_trial) {
            return redirect()->route('subscriptions.index')
                ->with('error', 'Anda sudah pernah menggunakan masa uji coba.');
        }

        // Cari paket Enterprise
        $enterprisePlan = SubscriptionPlan::where('slug', 'enterprise')->first();

        if (!$enterprisePlan) {
            return redirect()->route('subscriptions.index')
                ->with('error', 'Paket Enterprise tidak ditemukan.');
        }

        // Buat subscription trial
        $startDate = now();
        $endDate = now()->addDays(15); // 15 hari trial

        $subscription = new \App\Models\Subscription([
            'user_id' => $user->id,
            'subscription_plan_id' => $enterprisePlan->id,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => 'active',
            'is_trial' => true,
            'trial_ends_at' => $endDate,
            'auto_renew' => false,
        ]);

        $subscription->save();

        // Update user
        \App\Models\User::where('id', $user->id)->update([
            'current_subscription_id' => $subscription->id,
            'has_used_trial' => true,
            'trial_started_at' => $startDate,
            'trial_ends_at' => $endDate
        ]);

        return redirect()->route('subscriptions.show')
            ->with('success', 'Masa uji coba 15 hari untuk paket Enterprise berhasil diaktifkan.');
    }

    /**
     * Show the history of user subscriptions.
     */
    public function history()
    {
        $user = Auth::user();
        $subscriptions = \App\Models\Subscription::where('user_id', $user->id)
            ->with('plan')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('subscriptions.history', compact('subscriptions'));
    }

    /**
     * Handle successful payment.
     */
    public function success(Request $request)
    {
        // Get payment gateway
        $paymentGateway = \App\Models\SystemSetting::getValue('payment_gateway', 'midtrans');

        if ($paymentGateway === 'midtrans') {
            // For Midtrans, the payment is handled by the callback
            return view('subscriptions.success', [
                'message' => 'Pembayaran berhasil! Langganan Anda telah diaktifkan.'
            ]);
        } else if ($paymentGateway === 'xendit') {
            // For Xendit, check if the payment is successful
            $externalId = $request->query('external_id');

            if ($externalId) {
                $payment = \App\Models\Payment::where('payment_id', $externalId)->first();

                if ($payment) {
                    if ($payment->status === 'completed') {
                        return view('subscriptions.success', [
                            'message' => 'Pembayaran berhasil! Langganan Anda telah diaktifkan.',
                            'payment' => $payment
                        ]);
                    } else {
                        // Payment found but not completed
                        return view('subscriptions.success', [
                            'message' => 'Pembayaran sedang diproses. Langganan Anda akan diaktifkan setelah pembayaran berhasil diverifikasi.',
                            'payment' => $payment
                        ]);
                    }
                }
            }

            // If payment is not found, show generic message
            return view('subscriptions.success', [
                'message' => 'Pembayaran sedang diproses. Langganan Anda akan diaktifkan setelah pembayaran berhasil diverifikasi.'
            ]);
        }

        return view('subscriptions.success', [
            'message' => 'Terima kasih atas pembayaran Anda. Status langganan Anda akan diperbarui segera.'
        ]);
    }

    /**
     * Handle failed payment.
     */
    public function error()
    {
        return view('subscriptions.error', [
            'message' => 'Pembayaran gagal. Silakan coba lagi atau hubungi customer support kami.'
        ]);
    }
}
