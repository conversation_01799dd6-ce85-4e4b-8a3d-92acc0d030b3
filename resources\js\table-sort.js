// Table Sorting Functionality
let currentSortColumn = -1;
let currentSortDirection = "asc";

// Function to sort table with Tailwind CSS styling
function sortTable(columnIndex, tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const tbody = table.querySelector("tbody");
    const rows = Array.from(tbody.querySelectorAll("tr"));

    // Skip if there's only one row or no data
    if (rows.length <= 1 || rows[0].querySelector("td[colspan]")) return;

    // Update sort direction
    if (currentSortColumn === columnIndex) {
        currentSortDirection = currentSortDirection === "asc" ? "desc" : "asc";
    } else {
        currentSortDirection = "asc";
        currentSortColumn = columnIndex;
    }

    // Update sort icons and header styling
    updateSortUI(tableId, columnIndex);

    // Sort the rows
    rows.sort((rowA, rowB) => {
        // Get cell content
        const cellA = rowA.querySelectorAll("td")[columnIndex];
        const cellB = rowB.querySelectorAll("td")[columnIndex];

        if (!cellA || !cellB) return 0;

        let valueA, valueB;

        // Check if this is a currency column (contains Rp.)
        if (
            cellA.querySelector("span.float-left") &&
            cellA.querySelector("span.float-right")
        ) {
            // Extract the numeric value from the right span
            const spanA = cellA.querySelector("span.float-right");
            const spanB = cellB.querySelector("span.float-right");

            if (spanA && spanB) {
                // Remove commas and convert to number
                valueA = parseFloat(
                    spanA.textContent
                        .replace(/,/g, "")
                        .replace(/\./g, "")
                        .replace(/,/g, ".")
                );
                valueB = parseFloat(
                    spanB.textContent
                        .replace(/,/g, "")
                        .replace(/\./g, "")
                        .replace(/,/g, ".")
                );
            } else {
                // Fallback to text content
                valueA = cellA.textContent.trim();
                valueB = cellB.textContent.trim();
            }
        } else {
            // For non-currency columns
            valueA = cellA.textContent.trim();
            valueB = cellB.textContent.trim();

            // Try to convert to numbers if possible
            const numA = parseFloat(valueA);
            const numB = parseFloat(valueB);

            if (!isNaN(numA) && !isNaN(numB)) {
                valueA = numA;
                valueB = numB;
            }
        }

        // Compare values
        if (valueA < valueB) {
            return currentSortDirection === "asc" ? -1 : 1;
        }
        if (valueA > valueB) {
            return currentSortDirection === "asc" ? 1 : -1;
        }
        return 0;
    });

    // Animate and reorder the rows
    animateTableSort(tbody, rows);

    // Update row numbers
    updateRowNumbers(tableId);
}

// Animate table sorting with Tailwind CSS
function animateTableSort(tbody, rows) {
    // Add Tailwind transition classes to all rows
    rows.forEach((row) => {
        row.classList.add("transition-colors", "duration-300", "ease-in-out");
    });

    // Reorder the rows
    rows.forEach((row) => {
        tbody.appendChild(row);
    });

    // Add highlight effect with Tailwind classes
    setTimeout(() => {
        rows.forEach((row) => {
            // Add highlight with Tailwind classes
            row.classList.add("bg-blue-100", "dark:bg-blue-900/20");

            // Remove highlight after animation completes
            setTimeout(() => {
                row.classList.remove("bg-blue-100", "dark:bg-blue-900/20");
            }, 1000);
        });
    }, 50);
}

// Update the UI to show sorting state with Tailwind CSS
function updateSortUI(tableId, columnIndex) {
    const table = document.getElementById(tableId);
    const headers = table.querySelectorAll("thead th");

    // Reset all headers
    headers.forEach((header) => {
        // Remove active background
        header.classList.remove("bg-blue-100", "dark:bg-blue-900/20");

        const iconContainer = header.querySelector(".sort-icon");
        if (iconContainer) {
            const icon = iconContainer.querySelector("i");
            if (icon) {
                // Reset icon
                icon.className = "fas fa-sort text-gray-400 dark:text-gray-500";
            }
        }
    });

    // Update active header
    const activeHeader = headers[columnIndex];
    if (activeHeader) {
        // Add active background with Tailwind
        activeHeader.classList.add("bg-blue-100", "dark:bg-blue-900/20");

        const iconContainer = activeHeader.querySelector(".sort-icon");
        if (iconContainer) {
            const icon = iconContainer.querySelector("i");
            if (icon) {
                // Update icon with Tailwind classes
                icon.className =
                    currentSortDirection === "asc"
                        ? "fas fa-sort-up text-blue-600 dark:text-blue-400"
                        : "fas fa-sort-down text-blue-600 dark:text-blue-400";
            }
        }
    }
}

function updateRowNumbers(tableId) {
    const rows = document.querySelectorAll(`#${tableId} tbody tr`);
    rows.forEach((row, index) => {
        const firstCell = row.querySelector("td:first-child");
        if (firstCell && !firstCell.hasAttribute("colspan")) {
            firstCell.textContent = index + 1;
        }
    });
}

// Initialize sorting on tables with Tailwind CSS
function initTableSorting() {
    const tables = [
        "ahsTable",
        "upahTable",
        "bahanTable",
        "alatTable",
        "customerTable",
    ];

    tables.forEach((tableId) => {
        const table = document.getElementById(tableId);
        if (!table) return;

        const headers = table.querySelectorAll("thead th");
        headers.forEach((header, index) => {
            // Skip the action column (usually the last column)
            if (index < headers.length - 1) {
                // Add Tailwind classes for styling
                header.classList.add(
                    "cursor-pointer",
                    "select-none",
                    "transition-colors",
                    "duration-200",
                    "relative",
                    "hover:bg-blue-50",
                    "dark:hover:bg-blue-900/10",
                    "pr-8" // Extra padding for icon
                );

                // Add tooltip using title attribute
                header.setAttribute(
                    "title",
                    `Urutkan berdasarkan ${header.textContent.trim()}`
                );

                // Add sort icon with Tailwind classes
                if (!header.querySelector(".sort-icon")) {
                    const sortIcon = document.createElement("span");
                    sortIcon.className =
                        "sort-icon absolute right-2 top-1/2 transform -translate-y-1/2";
                    sortIcon.innerHTML =
                        '<i class="fas fa-sort text-gray-400 dark:text-gray-500"></i>';
                    header.appendChild(sortIcon);
                }

                // Add click event
                header.addEventListener("click", () => {
                    sortTable(index, tableId);
                });
            }
        });
    });
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", initTableSorting);
