// Customer Management JavaScript

// Variabel untuk menyimpan data customer yang sedang aktif
window.currentCustomer = null;

// Inisialisasi modal
document.addEventListener("DOMContentLoaded", function () {
    // Inisialisasi modal jika ada
    const customerModal = document.getElementById("customerModal");
    if (customerModal) {
        window.customerModal = new Modal(customerModal);
    }

    // Inisialisasi context menu
    initCustomerContextMenu();

    // Logging untuk debugging
    console.log("DOM loaded, initialized customer context menu");
});

// Fungsi untuk membuka modal customer (tambah atau edit)
window.openCustomerModal = function (
    isEdit = false,
    id = "",
    name = "",
    email = "",
    address = "",
    phone = ""
) {
    const modalTitle = document.getElementById("customerModalTitle");
    const customerForm = document.getElementById("customerForm");
    const customerId = document.getElementById("customerId");
    const customerMethod = document.getElementById("customerMethod");
    const passwordHelp = document.getElementById("passwordHelp");

    // Reset form
    customerForm.reset();

    // Reset validasi
    document.getElementById("nameError").classList.add("hidden");
    document.getElementById("emailError").classList.add("hidden");
    document.getElementById("passwordError").classList.add("hidden");
    document.getElementById("name").classList.remove("border-red-500");
    document.getElementById("email").classList.remove("border-red-500");
    document.getElementById("password").classList.remove("border-red-500");

    if (isEdit) {
        modalTitle.textContent = "Edit Customer";
        customerId.value = id;
        customerMethod.value = "PUT";
        document.getElementById("name").value = name;
        document.getElementById("email").value = email;

        // Jika data tersedia, isi field alamat dan telepon
        if (typeof address !== "undefined") {
            document.getElementById("address").value = address || "";
        }
        if (typeof phone !== "undefined") {
            document.getElementById("phone").value = phone || "";
        }

        passwordHelp.classList.remove("hidden");
        document.getElementById("password").required = false;
    } else {
        modalTitle.textContent = "Tambah Customer";
        customerId.value = "";
        customerMethod.value = "POST";
        passwordHelp.classList.add("hidden");
        document.getElementById("password").required = true;
    }

    window.customerModal.show();
};

// Fungsi untuk menyimpan data customer
window.saveCustomer = function () {
    const customerId = document.getElementById("customerId").value;
    const method = document.getElementById("customerMethod").value;
    const name = document.getElementById("name").value;
    const email = document.getElementById("email").value;
    const password = document.getElementById("password").value;
    const address = document.getElementById("address").value;
    const phone = document.getElementById("phone").value;

    // Reset semua pesan error
    document.getElementById("nameError").classList.add("hidden");
    document.getElementById("emailError").classList.add("hidden");
    document.getElementById("passwordError").classList.add("hidden");

    // Validasi form
    let isValid = true;

    if (!name) {
        document.getElementById("nameError").classList.remove("hidden");
        document.getElementById("name").classList.add("border-red-500");
        isValid = false;
    } else {
        document.getElementById("name").classList.remove("border-red-500");
    }

    if (!email) {
        document.getElementById("emailError").classList.remove("hidden");
        document.getElementById("email").classList.add("border-red-500");
        isValid = false;
    } else {
        document.getElementById("email").classList.remove("border-red-500");
    }

    if (method === "POST" && !password) {
        document.getElementById("passwordError").classList.remove("hidden");
        document.getElementById("password").classList.add("border-red-500");
        isValid = false;
    } else {
        document.getElementById("password").classList.remove("border-red-500");
    }

    if (!isValid) {
        return;
    }

    // Siapkan data
    const formData = new FormData();
    formData.append("name", name);
    formData.append("email", email);
    formData.append(
        "_token",
        document.querySelector('meta[name="csrf-token"]').content
    );

    if (password) {
        formData.append("password", password);
    }

    // Tambahkan alamat dan nomor telepon jika ada
    if (address) {
        formData.append("address", address);
    }

    if (phone) {
        formData.append("phone", phone);
    }

    // Tentukan URL dan method
    let url = "/customers";
    let fetchMethod = "POST";

    if (method === "PUT") {
        url = `/customers/${customerId}`;
        formData.append("_method", "PUT");
    }

    // Tampilkan loading
    const saveButton = document.querySelector(
        '#customerModal button[onclick="saveCustomer()"]'
    );
    const originalText = saveButton.innerHTML;
    saveButton.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
    saveButton.disabled = true;

    // Kirim request
    fetch(url, {
        method: "POST",
        body: formData,
        headers: {
            "X-Requested-With": "XMLHttpRequest",
        },
    })
        .then(async (response) => {
            // Kembalikan tombol ke keadaan semula
            saveButton.innerHTML = originalText;
            saveButton.disabled = false;

            // Coba parse response sebagai JSON
            let data;
            let isJson = false;

            try {
                const contentType = response.headers.get("content-type");
                if (contentType && contentType.includes("application/json")) {
                    data = await response.json();
                    isJson = true;
                } else {
                    // Jika bukan JSON, ambil text saja
                    const text = await response.text();
                    console.log("Response text:", text);

                    // Coba parse text sebagai JSON (kadang header content-type tidak akurat)
                    try {
                        data = JSON.parse(text);
                        isJson = true;
                    } catch (e) {
                        // Bukan JSON, gunakan text sebagai pesan error
                        if (text.includes("CSRF token mismatch")) {
                            throw new Error(
                                "CSRF token tidak valid. Silakan refresh halaman dan coba lagi."
                            );
                        } else if (text.includes("<!DOCTYPE html>")) {
                            throw new Error(
                                "Server mengembalikan halaman HTML. Mungkin terjadi error pada server."
                            );
                        } else {
                            throw new Error(
                                text || "Terjadi kesalahan pada server"
                            );
                        }
                    }
                }
            } catch (e) {
                throw e;
            }

            // Periksa status response
            if (!response.ok) {
                if (isJson && data.message) {
                    throw new Error(data.message);
                } else {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
            }

            return data;
        })
        .then((data) => {
            if (data.success) {
                window.showSuccessToast(data.message, "Berhasil");
                window.customerModal.hide();
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                window.showErrorToast(
                    data.message || "Terjadi kesalahan",
                    "Gagal"
                );
                console.error("Errors:", data.errors);
            }
        })
        .catch((error) => {
            window.showErrorToast(
                error.message ||
                    "Terjadi kesalahan saat menyimpan data. Silakan coba lagi.",
                "Error"
            );
            console.error("Error:", error);

            // Tambahkan penanganan khusus untuk email yang sudah terdaftar
            if (
                (error.message && error.message.includes("email sudah ada")) ||
                (error.message &&
                    error.message.includes("email has already been taken"))
            ) {
                // Tampilkan error di bawah field email
                const emailError = document.getElementById("emailError");
                emailError.textContent =
                    "Email sudah terdaftar. Silakan gunakan email lain.";
                emailError.classList.remove("hidden");
                document
                    .getElementById("email")
                    .classList.add("border-red-500");
            }
        });
};

// Fungsi untuk konfirmasi hapus customer
window.confirmDeleteCustomer = function (id) {
    // Buat elemen konfirmasi
    const confirmModal = document.createElement("div");
    confirmModal.className =
        "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
    confirmModal.id = "deleteConfirmModal";
    confirmModal.innerHTML = `
        <div class="bg-white dark:bg-dark-card rounded-lg shadow-lg p-6 max-w-sm mx-auto">
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-5">Konfirmasi Hapus</h3>
                <p class="text-gray-700 dark:text-gray-300 mb-6">Apakah Anda yakin ingin menghapus customer ini?</p>
                <div class="flex justify-center space-x-4">
                    <button id="btnDeleteCancel" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                        Tidak
                    </button>
                    <button id="btnDeleteConfirm" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                        Ya, Hapus
                    </button>
                </div>
            </div>
        </div>
    `;

    // Tambahkan modal ke body
    document.body.appendChild(confirmModal);

    // Tambahkan event listener
    document
        .getElementById("btnDeleteCancel")
        .addEventListener("click", function () {
            document.body.removeChild(confirmModal);
        });

    document
        .getElementById("btnDeleteConfirm")
        .addEventListener("click", function () {
            document.body.removeChild(confirmModal);
            deleteCustomer(id);
        });
};

// Fungsi untuk menghapus customer
window.deleteCustomer = function (id) {
    // Siapkan data
    const formData = new FormData();
    formData.append(
        "_token",
        document.querySelector('meta[name="csrf-token"]').content
    );
    formData.append("_method", "DELETE");

    // Kirim request
    fetch(`/customers/${id}`, {
        method: "POST",
        body: formData,
        headers: {
            "X-Requested-With": "XMLHttpRequest",
        },
    })
        .then(async (response) => {
            // Coba parse response sebagai JSON
            let data;
            let isJson = false;

            try {
                const contentType = response.headers.get("content-type");
                if (contentType && contentType.includes("application/json")) {
                    data = await response.json();
                    isJson = true;
                } else {
                    // Jika bukan JSON, ambil text saja
                    const text = await response.text();
                    console.log("Response text:", text);

                    // Coba parse text sebagai JSON (kadang header content-type tidak akurat)
                    try {
                        data = JSON.parse(text);
                        isJson = true;
                    } catch (e) {
                        // Bukan JSON, gunakan text sebagai pesan error
                        if (text.includes("CSRF token mismatch")) {
                            throw new Error(
                                "CSRF token tidak valid. Silakan refresh halaman dan coba lagi."
                            );
                        } else if (text.includes("<!DOCTYPE html>")) {
                            throw new Error(
                                "Server mengembalikan halaman HTML. Mungkin terjadi error pada server."
                            );
                        } else {
                            throw new Error(
                                text || "Terjadi kesalahan pada server"
                            );
                        }
                    }
                }
            } catch (e) {
                throw e;
            }

            // Periksa status response
            if (!response.ok) {
                if (isJson && data.message) {
                    throw new Error(data.message);
                } else {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
            }

            return data;
        })
        .then((data) => {
            if (data.success) {
                window.showSuccessToast(data.message, "Berhasil");
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                window.showErrorToast(
                    data.message || "Terjadi kesalahan",
                    "Gagal"
                );
            }
        })
        .catch((error) => {
            window.showErrorToast(
                error.message ||
                    "Terjadi kesalahan saat menghapus data. Silakan coba lagi.",
                "Error"
            );
            console.error("Error:", error);
        });
};

// Fungsi untuk menangani context menu customer
window.handleCustomerContextMenu = function (event, button) {
    event.preventDefault();
    event.stopPropagation();

    const contextMenu = document.getElementById("customer-context-menu");
    if (!contextMenu) {
        console.error(
            "Context menu element not found in handleCustomerContextMenu"
        );
        return;
    }

    // Pastikan context menu berada di body
    if (contextMenu.parentElement !== document.body) {
        document.body.appendChild(contextMenu);
    }

    // Ambil data customer dari atribut data
    const customerId = button.getAttribute("data-customer-id");
    const customerName = button.getAttribute("data-customer-name");
    const customerEmail = button.getAttribute("data-customer-email");
    const customerAddress = button.getAttribute("data-customer-address");
    const customerPhone = button.getAttribute("data-customer-phone");
    const customerViewUrl = button.getAttribute("data-customer-view-url");

    console.log("Customer data for context menu:", {
        id: customerId,
        name: customerName,
        email: customerEmail,
        viewUrl: customerViewUrl,
    });

    // Simpan data customer ke variabel global
    window.currentCustomer = {
        id: customerId,
        name: customerName,
        email: customerEmail,
        address: customerAddress,
        phone: customerPhone,
        viewUrl: customerViewUrl,
    };

    // Setel juga href untuk tombol view langsung
    const btnViewCustomer = document.getElementById("btn-view-customer");
    if (btnViewCustomer && customerViewUrl) {
        btnViewCustomer.setAttribute("href", customerViewUrl);
        console.log("Set view button href to:", customerViewUrl);
    }

    // Tampilkan context menu
    contextMenu.classList.remove("hidden");

    // Posisikan context menu
    const menuWidth = contextMenu.offsetWidth;
    const menuHeight = contextMenu.offsetHeight;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let x = event.pageX;
    let y = event.pageY;

    // Pastikan context menu tidak keluar dari viewport
    if (x + menuWidth > viewportWidth) x = viewportWidth - menuWidth;
    if (y + menuHeight > viewportHeight) y = viewportHeight - menuHeight;

    contextMenu.style.left = `${x}px`;
    contextMenu.style.top = `${y}px`;
    contextMenu.style.zIndex = "60"; // Pastikan context menu berada di depan
};

// Fungsi untuk inisialisasi context menu
function initCustomerContextMenu() {
    console.log("Initializing customer context menu");

    const contextMenu = document.getElementById("customer-context-menu");
    if (!contextMenu) {
        console.error("Context menu element not found!");
        return;
    }

    // Tombol Edit Customer
    const btnEditCustomer = document.getElementById("btn-edit-customer");
    if (btnEditCustomer) {
        btnEditCustomer.addEventListener("click", function () {
            console.log("Edit customer button clicked");
            if (!window.currentCustomer) {
                console.error("No current customer data available");
                return;
            }

            // Panggil fungsi openCustomerModal dengan parameter yang sesuai
            openCustomerModal(
                true,
                window.currentCustomer.id,
                window.currentCustomer.name,
                window.currentCustomer.email,
                window.currentCustomer.address,
                window.currentCustomer.phone
            );

            // Sembunyikan context menu
            contextMenu.classList.add("hidden");
        });
    } else {
        console.error("Edit customer button not found!");
    }

    // Tombol View Customer
    const btnViewCustomer = document.getElementById("btn-view-customer");
    if (btnViewCustomer) {
        btnViewCustomer.addEventListener("click", function (e) {
            console.log("View customer button clicked");
            e.preventDefault();

            if (!window.currentCustomer) {
                console.error("No current customer data available");
                return;
            }

            console.log("Navigating to:", window.currentCustomer.viewUrl);
            // Redirect ke halaman detail customer
            window.location.href = window.currentCustomer.viewUrl;

            // Sembunyikan context menu
            contextMenu.classList.add("hidden");
        });
    } else {
        console.error("View customer button not found!");
    }

    // Tombol Delete Customer
    const btnDeleteCustomer = document.getElementById("btn-delete-customer");
    if (btnDeleteCustomer) {
        btnDeleteCustomer.addEventListener("click", function () {
            console.log("Delete customer button clicked");
            if (!window.currentCustomer) {
                console.error("No current customer data available");
                return;
            }

            // Panggil fungsi confirmDeleteCustomer dengan parameter yang sesuai
            confirmDeleteCustomer(window.currentCustomer.id);

            // Sembunyikan context menu
            contextMenu.classList.add("hidden");
        });
    } else {
        console.error("Delete customer button not found!");
    }

    // Sembunyikan context menu saat mengklik di luar context menu
    document.addEventListener("click", function (event) {
        if (
            !contextMenu.contains(event.target) &&
            event.target.className.indexOf("action-btn-item") === -1
        ) {
            contextMenu.classList.add("hidden");
        }
    });
}
