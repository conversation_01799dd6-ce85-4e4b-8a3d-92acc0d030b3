@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2">Upload Bukti Pembayaran</h1>
            <p class="text-gray-600 dark:text-gray-400">Upload bukti pem<PERSON>aran untuk invoice {{ $payment->invoice_number }}</p>
        </div>
        <div>
            <a href="{{ route('payments.show', $payment->id) }}" class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                <i class="fas fa-arrow-left mr-1"></i> Kembali
            </a>
        </div>
    </div>

    @if(session('error'))
    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400" role="alert">
        <p>{{ session('error') }}</p>
    </div>
    @endif

    @if($errors->any())
    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400" role="alert">
        <ul class="list-disc pl-4">
            @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Detail Pembayaran -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
                <div class="bg-blue-600 dark:bg-blue-700 p-4">
                    <h2 class="text-white text-lg font-semibold">Detail Pembayaran</h2>
                </div>
                
                <div class="p-6">
                    <div class="space-y-3">
                        <div>
                            <span class="text-gray-600 dark:text-gray-400">No. Invoice:</span>
                            <span class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->invoice_number }}</span>
                        </div>
                        
                        <div>
                            <span class="text-gray-600 dark:text-gray-400">Tanggal:</span>
                            <span class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->created_at->format('d F Y') }}</span>
                        </div>
                        
                        <div>
                            <span class="text-gray-600 dark:text-gray-400">Jumlah:</span>
                            <span class="font-medium text-light-text dark:text-dark-text ml-2">{{ $payment->formatted_amount }}</span>
                        </div>
                        
                        <div>
                            <span class="text-gray-600 dark:text-gray-400">Paket:</span>
                            <span class="font-medium text-light-text dark:text-dark-text ml-2">
                                @if($payment->subscription && $payment->subscription->plan)
                                    {{ $payment->subscription->plan->name }}
                                @else
                                    -
                                @endif
                            </span>
                        </div>
                    </div>
                    
                    <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <h3 class="font-semibold text-light-text dark:text-dark-text mb-2">Petunjuk Pembayaran</h3>
                        
                        <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                            <p>Silakan transfer ke rekening berikut:</p>
                            
                            <div class="bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg">
                                <p class="font-medium text-light-text dark:text-dark-text">Bank BCA</p>
                                <p>No. Rekening: **********</p>
                                <p>Atas Nama: PT Estimator Indonesia</p>
                            </div>
                            
                            <p class="mt-3">Setelah melakukan pembayaran, silakan upload bukti pembayaran pada form di samping.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Form Upload -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
                <div class="bg-blue-600 dark:bg-blue-700 p-4">
                    <h2 class="text-white text-lg font-semibold">Upload Bukti Pembayaran</h2>
                </div>
                
                <div class="p-6">
                    <form action="{{ route('payments.upload', $payment->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="mb-6">
                            <label for="payment_proof" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bukti Pembayaran</label>
                            
                            <div class="mt-2 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-700 border-dashed rounded-lg">
                                <div class="space-y-1 text-center">
                                    <div id="preview-container" class="hidden mb-3">
                                        <img id="preview-image" src="#" alt="Preview" class="mx-auto h-32 object-cover rounded">
                                    </div>
                                    
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    
                                    <div class="flex text-sm text-gray-600 dark:text-gray-400">
                                        <label for="payment_proof" class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 focus-within:outline-none">
                                            <span>Upload file</span>
                                            <input id="payment_proof" name="payment_proof" type="file" class="sr-only" accept="image/*" onchange="previewImage(this)">
                                        </label>
                                        <p class="pl-1">atau drag and drop</p>
                                    </div>
                                    
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        PNG, JPG, JPEG hingga 2MB
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                                <i class="fas fa-upload mr-1"></i> Upload Bukti Pembayaran
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function previewImage(input) {
        const previewContainer = document.getElementById('preview-container');
        const previewImage = document.getElementById('preview-image');
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                previewContainer.classList.remove('hidden');
            }
            
            reader.readAsDataURL(input.files[0]);
        }
    }
</script>
@endsection
