// volumeLoadTemplates.js

// Helper function untuk menghasilkan tombol aksi
function generateActionButton(calc) {
    return `
    <td class="px-4 py-2 border dark:border-gray-700 text-center">
        <button type="button" class="action-btn-item px-4 text-blue-500 hover:text-blue-700 p-1 rounded-full hover:bg-blue-200 dark:text-dark-accent dark:hover:text-white dark:hover:bg-dark-accent/20 transition-colors duration-150" data-id="${calc.id}" data-keterangan="${calc.keterangan}" onclick="handleVolumeContextMenu(event, this)">
            <i class="fas fa-ellipsis-v"></i>
        </button>
    </td>
    `;
}

function getColspanSub(satuan) {
    switch (satuan) {
        case "m3":
            return 9;
        case "m2":
            return 8;
        case "m":
            return 6;
        case "kg":
            return 7;
        case "ltr":
            return 6;
        case "bh":
        case "ls":
        case "unit":
            return 5;
        default:
            return 5;
    }
}
function getColspanSum(satuan) {
    switch (satuan) {
        case "m3":
            return 7;
        case "m2":
            return 6;
        case "m":
            return 4;
        case "kg":
            return 5;
        case "ltr":
            return 4;
        case "bh":
        case "ls":
        case "unit":
            return 3;
        default:
            return 3;
    }
}

export function generatePlusRows(plusData, unit) {
    let rows = "";
    let totalPlus = 0;
    rows += `
    <tr class="bg-blue-100 dark:bg-blue-900/30 font-semibold">
      <td colspan="${getColspanSub(
          unit
      )}" class="px-4 py-2 border dark:border-gray-700">
        A. Penambahan Volume
        <button onclick="addVolumeCalculation('plus')" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white px-2 py-1 rounded float-right transition-all duration-200 transform hover:scale-105">
         <i class="fas fa-plus-circle"></i>
        </button>
      </td>
    </tr>
  `;
    if (plusData.length > 0) {
        plusData.forEach((calc, index) => {
            let rowHtml = "";
            if (unit === "m3") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.panjang
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.lebar}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.tinggi
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.volume
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            } else if (unit === "m2") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.panjang || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.lebar || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.luas || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            } else if (unit === "m") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.panjang || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            } else if (unit === "kg") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.panjang || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.berat_jenis || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            } else if (unit === "ltr") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.liter || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            } else if (unit === "bh" || unit === "ls" || unit === "unit") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            }
            rows += rowHtml;
            totalPlus += parseFloat(calc.hasil) || 0;
        });
        rows += `
      <tr class="bg-gray-100 dark:bg-gray-800 font-semibold">
        <td colspan= "${getColspanSum(
            unit
        )}" class="px-4 py-2 border dark:border-gray-700 text-right">Total Penambahan:</td>
        <td class="px-4 py-2 border dark:border-gray-700">${totalPlus.toFixed(
            2
        )}</td>
        <td class="px-4 py-2 border dark:border-gray-700"></td>
      </tr>
    `;
    } else {
        rows += `
      <tr>
        <td colspan="${getColspanSub(
            unit
        )}" class="text-center dark:text-gray-300">Tidak ada data penambahan volume</td>
      </tr>
    `;
    }
    return { rows, totalPlus };
}

export function generateMinusRows(minusData, unit) {
    let rows = "";
    let totalMinus = 0;
    rows += `
    <tr class="bg-red-100 dark:bg-red-900/30 font-semibold">
      <td colspan="${getColspanSub(
          unit
      )}" class="px-4 py-2 border dark:border-gray-700">
        B. Pengurangan Volume
        <button onclick="addVolumeCalculation('minus')" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white px-2 py-1 rounded float-right transition-all duration-200 transform hover:scale-105">
         <i class="fas fa-plus-circle"></i>
        </button>
      </td>
    </tr>
  `;
    if (minusData.length > 0) {
        minusData.forEach((calc, index) => {
            let rowHtml = "";
            if (unit === "m3") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.panjang
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.lebar}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.tinggi
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.volume
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            } else if (unit === "m2") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.panjang || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.lebar || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.luas || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            } else if (unit === "m") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.panjang || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            } else if (unit === "kg") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.panjang || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.berat_jenis || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            } else if (unit === "ltr") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.liter || ""
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            } else if (unit === "bh" || unit === "ls" || unit === "unit") {
                rowHtml = `
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
            <td class="px-4 py-2 border dark:border-gray-700">${index + 1}</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.keterangan
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${
                calc.jumlah
            }</td>
            <td class="px-4 py-2 border dark:border-gray-700">${calc.hasil}</td>
            ${generateActionButton(calc)}
          </tr>
        `;
            }
            rows += rowHtml;
            totalMinus += parseFloat(calc.hasil) || 0;
        });
        rows += `
      <tr class="bg-gray-100 dark:bg-gray-800 font-semibold">
        <td colspan="${getColspanSum(
            unit
        )}" class="px-4 py-2 border dark:border-gray-700 text-right">Total Pengurangan:</td>
        <td class="px-4 py-2 border dark:border-gray-700">${totalMinus.toFixed(
            2
        )}</td>
        <td class="px-4 py-2 border dark:border-gray-700"></td>
      </tr>
    `;
    } else {
        rows += `
      <tr>
        <td colspan="${getColspanSub(
            unit
        )}" class="text-center dark:text-gray-300">Tidak ada data pengurangan volume</td>
      </tr>
    `;
    }
    return { rows, totalMinus };
}
