import defaultTheme from "tailwindcss/defaultTheme";

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        "./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
        "./storage/framework/views/*.php",
        "./resources/**/*.blade.php",
        "./resources/**/*.js",
        "./resources/**/*.vue",
    ],
    darkMode: "class",
    theme: {
        screens: {
            xs: "475px",
            ...defaultTheme.screens,
        },
        extend: {
            fontFamily: {
                sans: ["Figtree", ...defaultTheme.fontFamily.sans],
            },
            colors: {
                // Light mode colors - menggunakan warna biru yang disarankan
                "light-bg": "#F8F9FA",
                "light-text": "#495057",
                "light-accent": "#0A558E", // Warna biru yang disarankan
                "light-navbar": "#0A558E", // Warna biru yang disarankan
                "light-sidebar": "#0A558E", // Warna biru yang disarankan
                "light-card": "#FFFFFF",
                "light-hover": "#E9ECEF",

                // Dark mode colors
                "dark-bg": "#1a1a1a", // Latar belakang lebih terang
                "dark-bg-secondary": "#252525", // Latar belakang sekunder lebih terang
                "dark-text": "#FFFFFF", // Teks utama dengan kontras maksimal
                "dark-text-secondary": "#E5E5E5", // Teks sekunder dengan kontras lebih tinggi
                "dark-accent": "#ED8936", // Amber/Orange 500 - Warna lebih gelap untuk kontras yang lebih baik
                "dark-navbar": "#222222", // Disesuaikan untuk sinkron dengan komponen lain
                "dark-sidebar": "#222222", // Disesuaikan untuk sinkron dengan komponen lain
                "dark-card": "#2c2c2c", // Card background sedikit lebih terang
                "dark-modal": "#232323", // Modal background sedikit lebih terang
                "dark-hover": "#333333", // Hover state sedikit lebih terang
                "dark-border": "#3a3a3a", // Border color sedikit lebih terang
                "dark-table-header": "#121212", // Table header background in dark mode (juga diubah menjadi hitam)
                "dark-table-row-even": "#252525", // Even rows sedikit lebih terang
                "dark-table-row-odd": "#2a2a2a", // Odd rows sedikit lebih terang
            },
            keyframes: {
                "pulse-scale": {
                    "0%, 100%": { transform: "scale(1)" },
                    "50%": { transform: "scale(1.05)" },
                },
            },
            animation: {
                "pulse-scale": "pulse-scale 2s ease-in-out infinite",
            },
        },
    },
    plugins: [],
};
