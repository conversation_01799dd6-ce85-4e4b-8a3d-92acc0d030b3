/* Custom Pagination Styles */

.pagination-container {
    perspective: 1000px;
}

/* Fixed Pagination */
.fixed-pagination {
    @apply fixed bottom-0 left-0 right-0 z-40 p-2 sm:p-3;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

/* Adjust for desktop sidebar */
@media (min-width: 640px) {
    .fixed-pagination {
        @apply left-64;
    }
}

.fixed-pagination::before {
    content: "";
    @apply absolute inset-0 bg-white/70 dark:bg-gray-800/70 -z-10;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .fixed-pagination::before {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.pagination-content {
    @apply flex flex-col sm:flex-row items-center justify-between gap-2 sm:gap-4 mx-auto;
    max-width: calc(100% - 2rem);
    width: 100%;
}

/* Hide pagination when scrolling down */
.pagination-hidden {
    transform: translateY(100%);
    opacity: 0;
}

/* No loading animation */

.pagination-info {
    @apply text-gray-700 dark:text-gray-300 bg-white/50 dark:bg-gray-800/50
           px-3 py-1.5 sm:px-4 sm:py-2 rounded-lg text-xs sm:text-sm
           transform transition-all duration-300 ease-in-out;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pagination-links {
    @apply inline-flex items-center justify-center space-x-1 sm:space-x-2 rounded-lg
           bg-white/50 dark:bg-gray-800/50 p-1.5 sm:p-2 md:p-3 shadow-sm backdrop-blur-sm
           transform transition-all duration-300 ease-in-out flex-wrap;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    min-height: 3rem;
}

.pagination-arrow {
    @apply relative inline-flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 md:w-9 md:h-9 text-xs sm:text-sm
           bg-light-accent text-white rounded-full transition-all duration-300 ease-in-out
           hover:bg-light-accent/80 hover:scale-110 hover:shadow-md
           dark:bg-dark-accent dark:hover:bg-dark-accent/80;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pagination-arrow-disabled {
    @apply relative inline-flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 md:w-9 md:h-9 text-xs sm:text-sm
           bg-gray-300 text-gray-500 rounded-full cursor-not-allowed
           dark:bg-gray-700 dark:text-gray-400;
}

.pagination-link {
    @apply relative inline-flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 md:w-9 md:h-9 text-xs sm:text-sm
           bg-white text-gray-700 rounded-full transition-all duration-300 ease-in-out
           hover:bg-light-accent hover:text-white hover:scale-110 hover:shadow-md
           dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-dark-accent dark:hover:text-white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transform: translateZ(0);
    backface-visibility: hidden;
}

.pagination-current {
    @apply relative inline-flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 md:w-9 md:h-9 text-xs sm:text-sm font-bold
           bg-light-accent text-white rounded-full shadow-md scale-110
           dark:bg-dark-accent;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: pulse-subtle 2s infinite ease-in-out;
}

@keyframes pulse-subtle {
    0%,
    100% {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    50% {
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
    }
}

.pagination-ellipsis {
    @apply relative inline-flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 md:w-9 md:h-9 text-xs sm:text-sm
           text-gray-500 dark:text-gray-400;
}

/* Hover effects */
.hover-pulse {
    animation: pagination-pulse 0.6s ease-in-out;
}

@keyframes pagination-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.15);
    }
    100% {
        transform: scale(1);
    }
}

/* Page number transitions */
.pagination-link,
.pagination-current {
    transition: all 0.3s ease-in-out;
}

/* Responsive adjustments */
@media (min-width: 640px) {
    .pagination-desktop {
        @apply flex-row;
    }
}
