<?php

namespace App\Http\Controllers;

// use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Project as Proyek;
use App\Models\Rab;
use App\Models\Ahs;
use App\Models\User;
use App\Models\Upah;
use App\Models\Bahan;
use App\Models\Alat;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
// use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
// use PhpOffice\PhpSpreadsheet\Cell\DataType;
use Barryvdh\DomPDF\Facade\Pdf;

class DownloadController extends Controller
{
    /**
     * Constructor untuk memastikan hanya user dengan role customer yang bisa mengakses
     *
     * Note: Middleware sudah ditangani di routes/web.php
     */
    public function __construct()
    {
        // Middleware sudah ditangani di routes/web.php
    }

    /**
     * Download RAB dalam format PDF
     */
    public function downloadPdf()
    {
        // Ambil proyek aktif dari user yang sedang login
        $user = Auth::user();

        // Jika user tidak login, buat user dummy
        if (!$user) {
            $user = new User();
            $user->id = 0;
            $user->name = 'Guest User';
        }

        $proyeks = Proyek::where('user_id', $user->id)->get();

        // Map nama kolom dari Project ke yang diharapkan oleh view
        $proyeks->transform(function ($proyek) {
            $proyek->nama_proyek = $proyek->name;
            $proyek->tanggal_mulai = $proyek->created_at->format('Y-m-d');
            $proyek->tanggal_selesai = $proyek->updated_at->format('Y-m-d');
            $proyek->status = 'Aktif';
            $proyek->total_rab = $proyek->rab ? $proyek->rab->total : 0;
            $proyek->lokasi = $proyek->location;
            return $proyek;
        });

        // Jika tidak ada proyek, buat proyek dummy untuk contoh
        if ($proyeks->isEmpty()) {
            $dummyProyek = new Proyek();
            $dummyProyek->id = 0;
            $dummyProyek->name = 'Contoh Proyek';
            $dummyProyek->location = 'Jakarta';
            $dummyProyek->created_at = now();
            $dummyProyek->updated_at = now();
            $dummyProyek->status = 'Contoh';
            $dummyProyek->total_rab = 0;

            // Buat beberapa RAB dummy
            $dummyRabs = [
                [
                    'uraian_pekerjaan' => 'Pekerjaan Persiapan',
                    'volume' => 1,
                    'satuan' => 'ls',
                    'harga_satuan' => 2500000,
                    'ahs_id' => 1
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Galian Tanah',
                    'volume' => 24.5,
                    'satuan' => 'm3',
                    'harga_satuan' => 94500,
                    'ahs_id' => 2
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Pondasi Batu Kali',
                    'volume' => 12.8,
                    'satuan' => 'm3',
                    'harga_satuan' => 1250000,
                    'ahs_id' => 3
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Beton Sloof 15/20',
                    'volume' => 2.4,
                    'satuan' => 'm3',
                    'harga_satuan' => 4250000,
                    'ahs_id' => 4
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Dinding Bata',
                    'volume' => 142.5,
                    'satuan' => 'm2',
                    'harga_satuan' => 135000,
                    'ahs_id' => 5
                ]
            ];

            $dummyRabCollection = collect();
            foreach ($dummyRabs as $rabData) {
                $dummyRab = new Rab();
                $dummyRab->uraian_pekerjaan = $rabData['uraian_pekerjaan'];
                $dummyRab->volume = $rabData['volume'];
                $dummyRab->satuan = $rabData['satuan'];
                $dummyRab->harga_satuan = $rabData['harga_satuan'];
                $dummyRab->ahs_id = $rabData['ahs_id'];
                $dummyRabCollection->push($dummyRab);
            }

            // Tambahkan RAB dummy ke proyek dummy
            $dummyProyek->rabs = $dummyRabCollection;

            $proyeks = collect([$dummyProyek]);
        }

        // Generate PDF
        $pdf = PDF::loadView('downloads.pdf', ['proyeks' => $proyeks]);

        return $pdf->download('rab-estimator.pdf');
    }

    /**
     * Download RAB dalam format Excel biasa
     */
    public function downloadExcel()
    {
        // Ambil proyek aktif dari user yang sedang login
        $user = Auth::user();

        // Jika user tidak login, buat user dummy
        if (!$user) {
            $user = new User();
            $user->id = 0;
            $user->name = 'Guest User';
        }

        $proyeks = Proyek::where('user_id', $user->id)->get();

        // Map nama kolom dari Project ke yang diharapkan oleh view
        $proyeks->transform(function ($proyek) {
            $proyek->nama_proyek = $proyek->name;
            $proyek->tanggal_mulai = $proyek->created_at->format('Y-m-d');
            $proyek->tanggal_selesai = $proyek->updated_at->format('Y-m-d');
            $proyek->status = 'Aktif';
            $proyek->total_rab = $proyek->rab ? $proyek->rab->total : 0;
            $proyek->lokasi = $proyek->location;
            return $proyek;
        });

        // Jika tidak ada proyek, buat proyek dummy untuk contoh
        if ($proyeks->isEmpty()) {
            $dummyProyek = new Proyek();
            $dummyProyek->id = 0;
            $dummyProyek->name = 'Contoh Proyek';
            $dummyProyek->location = 'Jakarta';
            $dummyProyek->created_at = now();
            $dummyProyek->updated_at = now();
            $dummyProyek->status = 'Contoh';
            $dummyProyek->total_rab = 0;
            $dummyProyek->nama_proyek = 'Contoh Proyek';
            $dummyProyek->tanggal_mulai = date('Y-m-d');
            $dummyProyek->tanggal_selesai = date('Y-m-d', strtotime('+30 days'));
            $dummyProyek->lokasi = 'Jakarta';

            // Buat beberapa RAB dummy
            $dummyRabs = [
                [
                    'uraian_pekerjaan' => 'Pekerjaan Persiapan',
                    'volume' => 1,
                    'satuan' => 'ls',
                    'harga_satuan' => 2500000,
                    'ahs_id' => 1
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Galian Tanah',
                    'volume' => 24.5,
                    'satuan' => 'm3',
                    'harga_satuan' => 94500,
                    'ahs_id' => 2
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Pondasi Batu Kali',
                    'volume' => 12.8,
                    'satuan' => 'm3',
                    'harga_satuan' => 1250000,
                    'ahs_id' => 3
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Beton Sloof 15/20',
                    'volume' => 2.4,
                    'satuan' => 'm3',
                    'harga_satuan' => 4250000,
                    'ahs_id' => 4
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Dinding Bata',
                    'volume' => 142.5,
                    'satuan' => 'm2',
                    'harga_satuan' => 135000,
                    'ahs_id' => 5
                ]
            ];

            $dummyRabCollection = collect();
            foreach ($dummyRabs as $rabData) {
                $dummyRab = new Rab();
                $dummyRab->uraian_pekerjaan = $rabData['uraian_pekerjaan'];
                $dummyRab->volume = $rabData['volume'];
                $dummyRab->satuan = $rabData['satuan'];
                $dummyRab->harga_satuan = $rabData['harga_satuan'];
                $dummyRab->ahs_id = $rabData['ahs_id'];
                $dummyRabCollection->push($dummyRab);
            }

            // Tambahkan RAB dummy ke proyek dummy
            $dummyProyek->rabs = $dummyRabCollection;

            $proyeks = collect([$dummyProyek]);
        }

        // Buat spreadsheet baru
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('RAB Estimator');

        // Set header
        $sheet->setCellValue('A1', 'RAB ESTIMATOR');
        $sheet->mergeCells('A1:G1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue('A3', 'No');
        $sheet->setCellValue('B3', 'Nama Proyek');
        $sheet->setCellValue('C3', 'Lokasi');
        $sheet->setCellValue('D3', 'Tanggal Mulai');
        $sheet->setCellValue('E3', 'Tanggal Selesai');
        $sheet->setCellValue('F3', 'Total RAB');
        $sheet->setCellValue('G3', 'Status');

        // Style header
        $headerStyle = [
            'font' => ['bold' => true],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'E2EFDA'],
            ],
        ];
        $sheet->getStyle('A3:G3')->applyFromArray($headerStyle);

        // Isi data
        $row = 4;
        foreach ($proyeks as $index => $proyek) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $proyek->nama_proyek);
            $sheet->setCellValue('C' . $row, $proyek->lokasi);
            $sheet->setCellValue('D' . $row, $proyek->tanggal_mulai);
            $sheet->setCellValue('E' . $row, $proyek->tanggal_selesai);
            $sheet->setCellValue('F' . $row, 'Rp ' . number_format($proyek->total_rab, 0, ',', '.'));
            $sheet->setCellValue('G' . $row, $proyek->status);
            $row++;
        }

        // Auto size kolom
        foreach (range('A', 'G') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Buat file Excel
        $writer = new Xlsx($spreadsheet);
        $filename = 'rab-estimator.xlsx';

        // Set header untuk download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    /**
     * Download RAB dalam format Excel dengan rumus
     */
    public function downloadExcelFormula()
    {
        // Ambil proyek aktif dari user yang sedang login
        $user = Auth::user();

        // Jika user tidak login, buat user dummy
        if (!$user) {
            $user = new User();
            $user->id = 0;
            $user->name = 'Guest User';
        }

        $proyeks = Proyek::where('user_id', $user->id)->get();

        // Map nama kolom dari Project ke yang diharapkan oleh view
        $proyeks->transform(function ($proyek) {
            $proyek->nama_proyek = $proyek->name;
            $proyek->tanggal_mulai = $proyek->created_at->format('Y-m-d');
            $proyek->tanggal_selesai = $proyek->updated_at->format('Y-m-d');
            $proyek->status = 'Aktif';
            $proyek->total_rab = $proyek->rab ? $proyek->rab->total : 0;
            $proyek->lokasi = $proyek->location;
            return $proyek;
        });

        // Jika tidak ada proyek, buat proyek dummy untuk contoh
        if ($proyeks->isEmpty()) {
            $dummyProyek = new Proyek();
            $dummyProyek->id = 0;
            $dummyProyek->name = 'Contoh Proyek';
            $dummyProyek->location = 'Jakarta';
            $dummyProyek->created_at = now();
            $dummyProyek->updated_at = now();
            $dummyProyek->status = 'Contoh';
            $dummyProyek->total_rab = 0;
            $dummyProyek->nama_proyek = 'Contoh Proyek';
            $dummyProyek->tanggal_mulai = date('Y-m-d');
            $dummyProyek->tanggal_selesai = date('Y-m-d', strtotime('+30 days'));
            $dummyProyek->lokasi = 'Jakarta';

            // Buat beberapa RAB dummy
            $dummyRabs = [
                [
                    'uraian_pekerjaan' => 'Pekerjaan Persiapan',
                    'volume' => 1,
                    'satuan' => 'ls',
                    'harga_satuan' => 2500000,
                    'ahs_id' => 1
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Galian Tanah',
                    'volume' => 24.5,
                    'satuan' => 'm3',
                    'harga_satuan' => 94500,
                    'ahs_id' => 2
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Pondasi Batu Kali',
                    'volume' => 12.8,
                    'satuan' => 'm3',
                    'harga_satuan' => 1250000,
                    'ahs_id' => 3
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Beton Sloof 15/20',
                    'volume' => 2.4,
                    'satuan' => 'm3',
                    'harga_satuan' => 4250000,
                    'ahs_id' => 4
                ],
                [
                    'uraian_pekerjaan' => 'Pekerjaan Dinding Bata',
                    'volume' => 142.5,
                    'satuan' => 'm2',
                    'harga_satuan' => 135000,
                    'ahs_id' => 5
                ]
            ];

            $dummyRabCollection = collect();
            foreach ($dummyRabs as $rabData) {
                $dummyRab = new Rab();
                $dummyRab->uraian_pekerjaan = $rabData['uraian_pekerjaan'];
                $dummyRab->volume = $rabData['volume'];
                $dummyRab->satuan = $rabData['satuan'];
                $dummyRab->harga_satuan = $rabData['harga_satuan'];
                $dummyRab->ahs_id = $rabData['ahs_id'];
                $dummyRabCollection->push($dummyRab);
            }

            // Tambahkan RAB dummy ke proyek dummy
            $dummyProyek->rabs = $dummyRabCollection;

            $proyeks = collect([$dummyProyek]);
        }

        // Buat spreadsheet baru
        $spreadsheet = new Spreadsheet();

        // Untuk setiap proyek, buat sheet baru
        foreach ($proyeks as $index => $proyek) {
            // Jika ini bukan proyek pertama, tambahkan sheet baru
            if ($index > 0) {
                $spreadsheet->createSheet();
            }

            // Set sheet aktif
            $spreadsheet->setActiveSheetIndex($index);
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle(substr($proyek->nama_proyek, 0, 31)); // Excel membatasi nama sheet maksimal 31 karakter

            // Set header proyek
            $sheet->setCellValue('A1', 'RAB ESTIMATOR - ' . $proyek->nama_proyek);
            $sheet->mergeCells('A1:G1');
            $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
            $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

            $sheet->setCellValue('A3', 'Nama Proyek:');
            $sheet->setCellValue('B3', $proyek->nama_proyek);
            $sheet->setCellValue('A4', 'Lokasi:');
            $sheet->setCellValue('B4', $proyek->lokasi);
            $sheet->setCellValue('A5', 'Tanggal Mulai:');
            $sheet->setCellValue('B5', $proyek->tanggal_mulai);
            $sheet->setCellValue('A6', 'Tanggal Selesai:');
            $sheet->setCellValue('B6', $proyek->tanggal_selesai);

            // Header untuk RAB
            $sheet->setCellValue('A8', 'No');
            $sheet->setCellValue('B8', 'Uraian Pekerjaan');
            $sheet->setCellValue('C8', 'Volume');
            $sheet->setCellValue('D8', 'Satuan');
            $sheet->setCellValue('E8', 'Harga Satuan (Rp)');
            $sheet->setCellValue('F8', 'Jumlah Harga (Rp)');

            // Style header
            $headerStyle = [
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E2EFDA'],
                ],
            ];
            $sheet->getStyle('A8:F8')->applyFromArray($headerStyle);

            // Ambil data RAB untuk proyek ini
            $rabs = Rab::where('proyek_id', $proyek->id)->orWhere('project_id', $proyek->id)->get();

            // Isi data RAB
            $row = 9;
            $startRow = $row;
            foreach ($rabs as $rabIndex => $rab) {
                $sheet->setCellValue('A' . $row, $rabIndex + 1);
                $sheet->setCellValue('B' . $row, $rab->uraian_pekerjaan);
                $sheet->setCellValue('C' . $row, $rab->volume);
                $sheet->setCellValue('D' . $row, $rab->satuan);
                $sheet->setCellValue('E' . $row, $rab->harga_satuan);

                // Rumus untuk menghitung jumlah harga
                $sheet->setCellValue('F' . $row, '=C' . $row . '*E' . $row);

                $row++;
            }
            $endRow = $row - 1;

            // Tambahkan total
            $sheet->setCellValue('E' . $row, 'TOTAL:');
            $sheet->getStyle('E' . $row)->getFont()->setBold(true);
            $sheet->getStyle('E' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            // Rumus untuk menghitung total
            if ($startRow <= $endRow) {
                $sheet->setCellValue('F' . $row, '=SUM(F' . $startRow . ':F' . $endRow . ')');
            } else {
                $sheet->setCellValue('F' . $row, 0);
            }
            $sheet->getStyle('F' . $row)->getFont()->setBold(true);

            // Format angka untuk kolom harga
            $sheet->getStyle('E' . $startRow . ':F' . $row)->getNumberFormat()->setFormatCode('#,##0');

            // Style untuk seluruh data
            $dataStyle = [
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN],
                ],
            ];
            $sheet->getStyle('A' . $startRow . ':F' . $row)->applyFromArray($dataStyle);

            // Auto size kolom
            foreach (range('A', 'F') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }

            // Tambahkan sheet untuk detail AHSP jika ada RAB
            if ($rabs->isNotEmpty()) {
                $spreadsheet->createSheet();
                $spreadsheet->setActiveSheetIndex($index * 2 + 1);
                $detailSheet = $spreadsheet->getActiveSheet();
                $detailSheet->setTitle(substr($proyek->nama_proyek . ' - Detail', 0, 31));

                // Set header detail
                $detailSheet->setCellValue('A1', 'DETAIL AHSP - ' . $proyek->nama_proyek);
                $detailSheet->mergeCells('A1:G1');
                $detailSheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
                $detailSheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

                // Header untuk detail AHSP
                $detailSheet->setCellValue('A3', 'No');
                $detailSheet->setCellValue('B3', 'Uraian Pekerjaan');
                $detailSheet->setCellValue('C3', 'Koefisien');
                $detailSheet->setCellValue('D3', 'Satuan');
                $detailSheet->setCellValue('E3', 'Harga Satuan (Rp)');
                $detailSheet->setCellValue('F3', 'Jumlah Harga (Rp)');

                // Style header
                $detailSheet->getStyle('A3:F3')->applyFromArray($headerStyle);

                // Isi data detail AHSP
                $detailRow = 4;
                foreach ($rabs as $rabIndex => $rab) {
                    // Ambil AHSP untuk RAB ini
                    $ahs = Ahs::find($rab->ahs_id);

                    // Jika AHS tidak ditemukan, buat dummy data
                    if (!$ahs) {
                        $ahs = new Ahs();
                        $ahs->id = $rab->ahs_id ?? 0;
                        $ahs->judul = $rab->uraian_pekerjaan ?? 'Tidak ada judul';
                        $ahs->satuan = $rab->satuan ?? 'Tidak ada satuan';
                        $ahs->overhead = 10;
                        $ahs->grand_total = $rab->harga_satuan ?? 0;

                        // Buat komponen upah dummy
                        $upahDummy = [
                            [
                                'nama_upah' => 'Pekerja',
                                'satuan' => 'OH',
                                'harga_satuan' => 85000,
                                'koefisien' => 0.7
                            ],
                            [
                                'nama_upah' => 'Tukang',
                                'satuan' => 'OH',
                                'harga_satuan' => 110000,
                                'koefisien' => 0.5
                            ],
                            [
                                'nama_upah' => 'Kepala Tukang',
                                'satuan' => 'OH',
                                'harga_satuan' => 125000,
                                'koefisien' => 0.05
                            ],
                            [
                                'nama_upah' => 'Mandor',
                                'satuan' => 'OH',
                                'harga_satuan' => 135000,
                                'koefisien' => 0.035
                            ]
                        ];

                        // Buat komponen bahan dummy
                        $bahanDummy = [
                            [
                                'nama_bahan' => 'Semen Portland',
                                'satuan' => 'Zak',
                                'harga_satuan' => 75000,
                                'koefisien' => 8.0
                            ],
                            [
                                'nama_bahan' => 'Pasir Pasang',
                                'satuan' => 'm3',
                                'harga_satuan' => 225000,
                                'koefisien' => 0.54
                            ],
                            [
                                'nama_bahan' => 'Batu Bata',
                                'satuan' => 'buah',
                                'harga_satuan' => 1000,
                                'koefisien' => 70
                            ]
                        ];

                        // Buat komponen alat dummy
                        $alatDummy = [
                            [
                                'nama_alat' => 'Molen',
                                'satuan' => 'jam',
                                'harga_satuan' => 65000,
                                'koefisien' => 0.2
                            ],
                            [
                                'nama_alat' => 'Alat Bantu',
                                'satuan' => 'ls',
                                'harga_satuan' => 25000,
                                'koefisien' => 1
                            ]
                        ];

                        // Buat koleksi upah dummy
                        $upahCollection = collect();
                        foreach ($upahDummy as $index => $upahData) {
                            $upah = new Upah();
                            $upah->id = $index + 1;
                            $upah->nama_upah = $upahData['nama_upah'];
                            $upah->satuan = $upahData['satuan'];
                            $upah->harga_satuan = $upahData['harga_satuan'];
                            $upah->pivot = (object) ['koefisien' => $upahData['koefisien']];
                            $upahCollection->push($upah);
                        }

                        // Buat koleksi bahan dummy
                        $bahanCollection = collect();
                        foreach ($bahanDummy as $index => $bahanData) {
                            $bahan = new Bahan();
                            $bahan->id = $index + 1;
                            $bahan->nama_bahan = $bahanData['nama_bahan'];
                            $bahan->satuan = $bahanData['satuan'];
                            $bahan->harga_satuan = $bahanData['harga_satuan'];
                            $bahan->pivot = (object) ['koefisien' => $bahanData['koefisien']];
                            $bahanCollection->push($bahan);
                        }

                        // Buat koleksi alat dummy
                        $alatCollection = collect();
                        foreach ($alatDummy as $index => $alatData) {
                            $alat = new Alat();
                            $alat->id = $index + 1;
                            $alat->nama_alat = $alatData['nama_alat'];
                            $alat->satuan = $alatData['satuan'];
                            $alat->harga_satuan = $alatData['harga_satuan'];
                            $alat->pivot = (object) ['koefisien' => $alatData['koefisien']];
                            $alatCollection->push($alat);
                        }

                        // Tambahkan koleksi ke AHS
                        $ahs->upah = $upahCollection;
                        $ahs->bahan = $bahanCollection;
                        $ahs->alat = $alatCollection;
                    }

                    if ($ahs) {
                        // Header untuk setiap RAB
                        $detailSheet->setCellValue('A' . $detailRow, ($rabIndex + 1) . '. ' . $rab->uraian_pekerjaan);
                        $detailSheet->mergeCells('A' . $detailRow . ':F' . $detailRow);
                        $detailSheet->getStyle('A' . $detailRow)->getFont()->setBold(true);
                        $detailSheet->getStyle('A' . $detailRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9D9D9');
                        $detailRow++;

                        // Komponen Upah
                        if ($ahs->upah->isNotEmpty()) {
                            $detailSheet->setCellValue('A' . $detailRow, 'A.');
                            $detailSheet->setCellValue('B' . $detailRow, 'TENAGA KERJA');
                            $detailSheet->mergeCells('B' . $detailRow . ':F' . $detailRow);
                            $detailSheet->getStyle('A' . $detailRow . ':F' . $detailRow)->getFont()->setBold(true);
                            $detailRow++;

                            $upahStartRow = $detailRow;
                            foreach ($ahs->upah as $upahIndex => $upah) {
                                $detailSheet->setCellValue('A' . $detailRow, $upahIndex + 1);
                                $detailSheet->setCellValue('B' . $detailRow, $upah->nama_upah);
                                $detailSheet->setCellValue('C' . $detailRow, $upah->pivot->koefisien);
                                $detailSheet->setCellValue('D' . $detailRow, $upah->satuan);
                                $detailSheet->setCellValue('E' . $detailRow, $upah->harga_satuan);

                                // Rumus untuk menghitung jumlah harga
                                $detailSheet->setCellValue('F' . $detailRow, '=C' . $detailRow . '*E' . $detailRow);

                                $detailRow++;
                            }
                            $upahEndRow = $detailRow - 1;

                            // Subtotal Upah
                            $detailSheet->setCellValue('A' . $detailRow, '');
                            $detailSheet->setCellValue('B' . $detailRow, 'SUBTOTAL TENAGA KERJA');
                            $detailSheet->mergeCells('B' . $detailRow . ':E' . $detailRow);
                            $detailSheet->getStyle('B' . $detailRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                            $detailSheet->getStyle('B' . $detailRow)->getFont()->setBold(true);

                            // Rumus untuk menghitung subtotal upah
                            if ($upahStartRow <= $upahEndRow) {
                                $detailSheet->setCellValue('F' . $detailRow, '=SUM(F' . $upahStartRow . ':F' . $upahEndRow . ')');
                            } else {
                                $detailSheet->setCellValue('F' . $detailRow, 0);
                            }
                            $detailSheet->getStyle('F' . $detailRow)->getFont()->setBold(true);
                            $detailRow++;
                        }

                        // Komponen Bahan
                        if ($ahs->bahan->isNotEmpty()) {
                            $detailSheet->setCellValue('A' . $detailRow, 'B.');
                            $detailSheet->setCellValue('B' . $detailRow, 'BAHAN');
                            $detailSheet->mergeCells('B' . $detailRow . ':F' . $detailRow);
                            $detailSheet->getStyle('A' . $detailRow . ':F' . $detailRow)->getFont()->setBold(true);
                            $detailRow++;

                            $bahanStartRow = $detailRow;
                            foreach ($ahs->bahan as $bahanIndex => $bahan) {
                                $detailSheet->setCellValue('A' . $detailRow, $bahanIndex + 1);
                                $detailSheet->setCellValue('B' . $detailRow, $bahan->nama_bahan);
                                $detailSheet->setCellValue('C' . $detailRow, $bahan->pivot->koefisien);
                                $detailSheet->setCellValue('D' . $detailRow, $bahan->satuan);
                                $detailSheet->setCellValue('E' . $detailRow, $bahan->harga_satuan);

                                // Rumus untuk menghitung jumlah harga
                                $detailSheet->setCellValue('F' . $detailRow, '=C' . $detailRow . '*E' . $detailRow);

                                $detailRow++;
                            }
                            $bahanEndRow = $detailRow - 1;

                            // Subtotal Bahan
                            $detailSheet->setCellValue('A' . $detailRow, '');
                            $detailSheet->setCellValue('B' . $detailRow, 'SUBTOTAL BAHAN');
                            $detailSheet->mergeCells('B' . $detailRow . ':E' . $detailRow);
                            $detailSheet->getStyle('B' . $detailRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                            $detailSheet->getStyle('B' . $detailRow)->getFont()->setBold(true);

                            // Rumus untuk menghitung subtotal bahan
                            if ($bahanStartRow <= $bahanEndRow) {
                                $detailSheet->setCellValue('F' . $detailRow, '=SUM(F' . $bahanStartRow . ':F' . $bahanEndRow . ')');
                            } else {
                                $detailSheet->setCellValue('F' . $detailRow, 0);
                            }
                            $detailSheet->getStyle('F' . $detailRow)->getFont()->setBold(true);
                            $detailRow++;
                        }

                        // Komponen Alat
                        if ($ahs->alat->isNotEmpty()) {
                            $detailSheet->setCellValue('A' . $detailRow, 'C.');
                            $detailSheet->setCellValue('B' . $detailRow, 'ALAT');
                            $detailSheet->mergeCells('B' . $detailRow . ':F' . $detailRow);
                            $detailSheet->getStyle('A' . $detailRow . ':F' . $detailRow)->getFont()->setBold(true);
                            $detailRow++;

                            $alatStartRow = $detailRow;
                            foreach ($ahs->alat as $alatIndex => $alat) {
                                $detailSheet->setCellValue('A' . $detailRow, $alatIndex + 1);
                                $detailSheet->setCellValue('B' . $detailRow, $alat->nama_alat);
                                $detailSheet->setCellValue('C' . $detailRow, $alat->pivot->koefisien);
                                $detailSheet->setCellValue('D' . $detailRow, $alat->satuan);
                                $detailSheet->setCellValue('E' . $detailRow, $alat->harga_satuan);

                                // Rumus untuk menghitung jumlah harga
                                $detailSheet->setCellValue('F' . $detailRow, '=C' . $detailRow . '*E' . $detailRow);

                                $detailRow++;
                            }
                            $alatEndRow = $detailRow - 1;

                            // Subtotal Alat
                            $detailSheet->setCellValue('A' . $detailRow, '');
                            $detailSheet->setCellValue('B' . $detailRow, 'SUBTOTAL ALAT');
                            $detailSheet->mergeCells('B' . $detailRow . ':E' . $detailRow);
                            $detailSheet->getStyle('B' . $detailRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                            $detailSheet->getStyle('B' . $detailRow)->getFont()->setBold(true);

                            // Rumus untuk menghitung subtotal alat
                            if ($alatStartRow <= $alatEndRow) {
                                $detailSheet->setCellValue('F' . $detailRow, '=SUM(F' . $alatStartRow . ':F' . $alatEndRow . ')');
                            } else {
                                $detailSheet->setCellValue('F' . $detailRow, 0);
                            }
                            $detailSheet->getStyle('F' . $detailRow)->getFont()->setBold(true);
                            $detailRow++;
                        }

                        // Total AHSP
                        $detailSheet->setCellValue('A' . $detailRow, '');
                        $detailSheet->setCellValue('B' . $detailRow, 'TOTAL HARGA SATUAN');
                        $detailSheet->mergeCells('B' . $detailRow . ':E' . $detailRow);
                        $detailSheet->getStyle('B' . $detailRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                        $detailSheet->getStyle('B' . $detailRow)->getFont()->setBold(true);
                        $detailSheet->getStyle('B' . $detailRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9D9D9');

                        // Rumus untuk menghitung total AHSP
                        $detailSheet->setCellValue('F' . $detailRow, '=' . $ahs->harga_satuan);
                        $detailSheet->getStyle('F' . $detailRow)->getFont()->setBold(true);
                        $detailSheet->getStyle('F' . $detailRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9D9D9');
                        $detailRow++;

                        // Tambahkan baris kosong untuk pemisah
                        $detailRow++;
                    }
                }

                // Format angka untuk kolom harga
                $detailSheet->getStyle('E4:F' . ($detailRow - 1))->getNumberFormat()->setFormatCode('#,##0');

                // Auto size kolom
                foreach (range('A', 'F') as $col) {
                    $detailSheet->getColumnDimension($col)->setAutoSize(true);
                }
            }
        }

        // Set sheet pertama sebagai active sheet
        $spreadsheet->setActiveSheetIndex(0);

        // Buat file Excel
        $writer = new Xlsx($spreadsheet);
        $filename = 'rab-estimator-dengan-rumus.xlsx';

        // Set header untuk download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
}
