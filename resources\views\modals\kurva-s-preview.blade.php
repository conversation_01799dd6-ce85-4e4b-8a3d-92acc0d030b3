@if(isset($timeSchedules) && count($timeSchedules) > 0)
<div class="p-4 md:p-5 space-y-4">
    <h2 class="text-xl font-bold text-gray-900 dark:text-white">KURVA S</h2>
    @php
        // Determine project start and end dates
        $projectStartDate = $timeSchedules->min('tanggal_mulai');
        $projectEndDate = $timeSchedules->max('tanggal_selesai');

        // Calculate total weeks
        $totalWeeks = ceil($projectStartDate->diffInDays($projectEndDate) / 7) + 1;
        $totalWeeks = min($totalWeeks, 52); // Limit to 52 weeks (1 year)

        // Generate week labels
        $weekLabels = [];
        $currentDate = clone $projectStartDate;
        for ($i = 0; $i < $totalWeeks; $i++) {
            $weekEnd = (clone $currentDate)->addDays(6);
            $weekLabels[] = $currentDate->format('d/m') . '-' . $weekEnd->format('d/m');
            $currentDate->addDays(7);
        }

        // Calculate cumulative progress per week
        $weeklyProgress = array_fill(0, $totalWeeks, 0);
        $weeklyPlan = array_fill(0, $totalWeeks, 0);

        foreach ($timeSchedules as $schedule) {
            $scheduleStart = $schedule->tanggal_mulai;
            $scheduleEnd = $schedule->tanggal_selesai;
            $scheduleDuration = $scheduleStart->diffInDays($scheduleEnd) + 1;
            $scheduleBobot = $schedule->bobot;
            $scheduleProgress = $schedule->progress;

            // Calculate daily weight
            $dailyWeight = $scheduleDuration > 0 ? $scheduleBobot / $scheduleDuration : 0;

            // Distribute weight across weeks
            for ($i = 0; $i < $totalWeeks; $i++) {
                $weekStart = (clone $projectStartDate)->addDays($i * 7);
                $weekEnd = (clone $weekStart)->addDays(6);

                // Check if schedule overlaps with this week
                if ($scheduleStart <= $weekEnd && $scheduleEnd >= $weekStart) {
                    // Calculate overlap days
                    $overlapStart = max($scheduleStart, $weekStart);
                    $overlapEnd = min($scheduleEnd, $weekEnd);
                    $overlapDays = $overlapStart->diffInDays($overlapEnd) + 1;

                    // Add weight to this week
                    $weekWeight = $dailyWeight * $overlapDays;
                    $weeklyPlan[$i] += $weekWeight;

                    // Add actual progress to this week (proportional to completion)
                    $weeklyProgress[$i] += $weekWeight * $scheduleProgress / 100;
                }
            }
        }

        // Calculate cumulative values
        $cumulativePlan = [];
        $cumulativeProgress = [];
        $runningPlan = 0;
        $runningProgress = 0;

        for ($i = 0; $i < $totalWeeks; $i++) {
            $runningPlan += $weeklyPlan[$i];
            $runningProgress += $weeklyProgress[$i];
            $cumulativePlan[$i] = $runningPlan;
            $cumulativeProgress[$i] = $runningProgress;
        }
    @endphp

    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Minggu</th>
                    <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Rencana (%)</th>
                    <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Kumulatif Rencana (%)</th>
                    <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Realisasi (%)</th>
                    <th scope="col" class="px-4 py-2 border border-gray-300 dark:border-gray-600">Kumulatif Realisasi (%)</th>
                </tr>
            </thead>
            <tbody>
                @for($i = 0; $i < $totalWeeks; $i++)
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ $weekLabels[$i] }}</td>
                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ number_format($weeklyPlan[$i], 2) }}</td>
                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ number_format($cumulativePlan[$i], 2) }}</td>
                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ number_format($weeklyProgress[$i], 2) }}</td>
                        <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center">{{ number_format($cumulativeProgress[$i], 2) }}</td>
                    </tr>
                @endfor
            </tbody>
            <tfoot>
                <tr class="bg-gray-50 dark:bg-gray-700">
                    <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-right font-bold">TOTAL</td>
                    <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center font-bold">{{ number_format(array_sum($weeklyPlan), 2) }}</td>
                    <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center font-bold">{{ number_format(end($cumulativePlan), 2) }}</td>
                    <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center font-bold">{{ number_format(array_sum($weeklyProgress), 2) }}</td>
                    <td class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-center font-bold">{{ number_format(end($cumulativeProgress), 2) }}</td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>

<div class="p-4 md:p-5 space-y-4">
    <h2 class="text-xl font-bold text-gray-900 dark:text-white">GRAFIK KURVA S</h2>
    <div class="relative h-80 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800">
        <!-- Y-axis labels -->
        <div class="absolute left-0 top-0 h-full w-10 border-r border-gray-300 dark:border-gray-600">
            @for($i = 0; $i <= 10; $i++)
                <div class="absolute left-0 bottom-[{{ $i * 10 }}%] w-full text-right pr-1 text-xs text-gray-500 dark:text-gray-400">
                    {{ $i * 10 }}%
                </div>
                <div class="absolute left-10 bottom-[{{ $i * 10 }}%] w-full border-t border-dashed border-gray-300 dark:border-gray-600"></div>
            @endfor
        </div>

        <!-- X-axis -->
        <div class="absolute left-10 bottom-0 right-0 h-6 border-t border-gray-300 dark:border-gray-600">
            @foreach($weekLabels as $index => $week)
                @if($index % max(1, intval($totalWeeks / 10)) === 0 || $index == $totalWeeks - 1)
                    <div class="absolute left-[{{ $index / max(1, $totalWeeks - 1) * 100 }}%] transform -translate-x-1/2 text-center text-xs text-gray-500 dark:text-gray-400 top-1">
                        {{ $week }}
                    </div>
                    <div class="absolute left-[{{ $index / max(1, $totalWeeks - 1) * 100 }}%] h-2 border-l border-gray-300 dark:border-gray-600"></div>
                @endif
            @endforeach
        </div>

        <!-- Chart area -->
        <div class="absolute left-10 top-0 right-0 bottom-6 overflow-hidden">
            <!-- Plan line (blue) -->
            <svg class="absolute inset-0 w-full h-full">
                <polyline
                    points="
                        @for($i = 0; $i < $totalWeeks; $i++)
                            {{ $i / max(1, $totalWeeks - 1) * 100 }}% {{ 100 - min(100, $cumulativePlan[$i]) }}%
                        @endfor
                    "
                    class="fill-none stroke-blue-500 stroke-2"
                />
            </svg>

            <!-- Progress line (red) -->
            <svg class="absolute inset-0 w-full h-full">
                <polyline
                    points="
                        @for($i = 0; $i < $totalWeeks; $i++)
                            {{ $i / max(1, $totalWeeks - 1) * 100 }}% {{ 100 - min(100, $cumulativeProgress[$i]) }}%
                        @endfor
                    "
                    class="fill-none stroke-red-500 stroke-2"
                />
            </svg>
        </div>
    </div>

    <!-- Legend -->
    <div class="flex justify-center mt-4">
        <div class="mr-6 flex items-center">
            <span class="inline-block w-6 h-1 bg-blue-500 mr-2"></span>
            <span class="text-sm text-gray-700 dark:text-gray-300">Rencana</span>
        </div>
        <div class="flex items-center">
            <span class="inline-block w-6 h-1 bg-red-500 mr-2"></span>
            <span class="text-sm text-gray-700 dark:text-gray-300">Realisasi</span>
        </div>
    </div>
</div>
@endif
