<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>RAB {{ $project->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10pt;
            line-height: 1.3;
            margin: 0;
            padding: 0;
        }
        .page-break {
            page-break-after: always;
        }
        h1 {
            font-size: 16pt;
            text-align: center;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 14pt;
            margin-top: 20px;
            margin-bottom: 10px;
            page-break-before: auto;
        }
        h3 {
            font-size: 12pt;
            margin-top: 15px;
            margin-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        table, th, td {
            border: 1px solid #000;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
            padding: 5px;
        }
        td {
            padding: 5px;
            text-align: left;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .header {
            margin-bottom: 20px;
        }
        .footer {
            margin-top: 20px;
            font-size: 9pt;
        }
        .total-section {
            margin-top: 10px;
        }
        .total-row {
            font-weight: bold;
        }
        .currency {
            text-align: right;
            position: relative;
        }
        .currency::before {
            content: "Rp. ";
            position: absolute;
            left: 5px;
        }
        .no-border {
            border: none;
        }
        .section-title {
            background-color: #e6e6e6;
            font-weight: bold;
            padding: 5px;
        }
    </style>
</head>
<body>
    @if(!isset($exportOptions) || in_array('rekap', $exportOptions))
    <!-- REKAPITULASI RAB Table (First Page) -->
    <div class="header">
        <h1>ESTIMATE ENGINEERING DETAIL</h1>
        <h2 style="text-align: center; margin-top: 0;">REKAPITULASI RENCANA ANGGARAN BIAYA</h2>
        <table class="no-border">
            <tr>
                <td class="no-border" width="150">Nama Proyek</td>
                <td class="no-border" width="10">:</td>
                <td class="no-border">{{ $project->name }}</td>
            </tr>
            <tr>
                <td class="no-border">Lokasi</td>
                <td class="no-border">:</td>
                <td class="no-border">{{ $project->lokasi ?? '-' }}</td>
            </tr>
            <tr>
                <td class="no-border">Tanggal</td>
                <td class="no-border">:</td>
                <td class="no-border">{{ date('d F Y', strtotime($project->created_at)) }}</td>
            </tr>
        </table>
    </div>
    <table>
        <thead>
            <tr>
                <th width="10%">No</th>
                <th width="60%">Uraian Pekerjaan</th>
                <th width="30%">Jumlah Harga (Rp)</th>
            </tr>
        </thead>
        <tbody>
            @php
                $rekapNo = 0;
                $totalRekapitulasi = 0;
                $letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
            @endphp
            @foreach($kategoriPekerjaans as $kategori)
                @php
                    $kategoriTotal = 0;
                    foreach($kategori->items as $item) {
                        $kategoriTotal += $item->harga_total;
                    }
                    $totalRekapitulasi += $kategoriTotal;
                    $letter = isset($letters[$rekapNo]) ? $letters[$rekapNo] : $rekapNo + 1;
                    $rekapNo++;
                @endphp
                <tr>
                    <td>{{ $letter }}</td>
                    <td>{{ strtoupper($kategori->nama_kategori) }}</td>
                    <td class="currency">{{ number_format($kategoriTotal, 2, ',', '.') }}</td>
                </tr>
            @endforeach
        </tbody>
        <tfoot>
            <tr class="total-row">
                <td colspan="2" class="text-right">JUMLAH</td>
                <td class="currency">{{ number_format($totalRekapitulasi, 2, ',', '.') }}</td>
            </tr>
            <tr>
                <td colspan="2" class="text-right">PPN {{ $ppn }}%</td>
                <td class="currency">{{ number_format($ppnHarga, 2, ',', '.') }}</td>
            </tr>
            <tr class="total-row">
                <td colspan="2" class="text-right">TOTAL</td>
                <td class="currency">{{ number_format($totalHarga, 2, ',', '.') }}</td>
            </tr>
            <tr class="total-row">
                <td colspan="2" class="text-right">DIBULATKAN</td>
                <td class="currency">{{ number_format($dibulatkanHarga, 2, ',', '.') }}</td>
            </tr>
            <tr>
                <td colspan="3" class="text-right"><i>Terbilang: {{ $terbilangTotal }}</i></td>
            </tr>
        </tfoot>
    </table>
    @endif

    @if(!isset($exportOptions) || in_array('rab', $exportOptions))
    <div class="page-break"></div>

    <!-- RAB Table (Second Page) -->
    <div class="header">
        <h1>RENCANA ANGGARAN BIAYA (RAB)</h1>
        <table class="no-border">
            <tr>
                <td class="no-border" width="150">Nama Proyek</td>
                <td class="no-border" width="10">:</td>
                <td class="no-border">{{ $project->name }}</td>
            </tr>
            <tr>
                <td class="no-border">Lokasi</td>
                <td class="no-border">:</td>
                <td class="no-border">{{ $project->lokasi ?? '-' }}</td>
            </tr>
            <tr>
                <td class="no-border">Tanggal</td>
                <td class="no-border">:</td>
                <td class="no-border">{{ date('d F Y', strtotime($project->created_at)) }}</td>
            </tr>
        </table>
    </div>

    <table>
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="40%">Uraian Pekerjaan</th>
                <th width="10%">Volume</th>
                <th width="10%">Satuan</th>
                <th width="15%">Harga Satuan (Rp)</th>
                <th width="20%">Jumlah Harga (Rp)</th>
            </tr>
        </thead>
        <tbody>
            @php
                $kategoriNo = 0;
                $letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
            @endphp
            @foreach($kategoriPekerjaans as $kategori)
                @php
                    $letter = isset($letters[$kategoriNo]) ? $letters[$kategoriNo] : $kategoriNo + 1;
                    $kategoriNo++;
                    $itemNo = 1; // Reset item number for each category
                @endphp
                <tr>
                    <td class="section-title">{{ $letter }}</td>
                    <td class="section-title" colspan="5">{{ strtoupper($kategori->nama_kategori) }}</td>
                </tr>
                @foreach($kategori->items as $item)
                    <tr>
                        <td class="text-center">{{ $itemNo++ }}</td>
                        <td>{{ $item->uraian_item }}</td>
                        <td class="text-center">{{ number_format($item->volume, 2) }}</td>
                        <td class="text-center">{{ $item->satuan }}</td>
                        <td class="currency">{{ number_format($item->harga_satuan, 2, ',', '.') }}</td>
                        <td class="currency">{{ number_format($item->harga_total, 2, ',', '.') }}</td>
                    </tr>
                @endforeach
            @endforeach
        </tbody>
        <!-- No footer for RAB table -->
    </table>
    @endif

    @if(!isset($exportOptions) || in_array('ahsp', $exportOptions))
    <div class="page-break"></div>

    <!-- AHS Tables -->
    <h2>ANALISA HARGA SATUAN PEKERJAAN</h2>
    @foreach($ahsRecords as $ahs)
        <h3>{{ $ahs->kode }} - {{ $ahs->judul }}</h3>
        <table>
            <thead>
                <tr>
                    <th width="5%">No</th>
                    <th width="40%">Uraian</th>
                    <th width="10%">Koefisien</th>
                    <th width="10%">Satuan</th>
                    <th width="15%">Harga Satuan (Rp)</th>
                    <th width="20%">Jumlah Harga (Rp)</th>
                </tr>
            </thead>
            <tbody>
                @php
                    $upahDetails = $ahsDetails->where('ahs_id', $ahs->id)->filter(function($detail) {
                        return strtolower($detail->kategori) === 'upah';
                    });
                    $bahanDetails = $ahsDetails->where('ahs_id', $ahs->id)->filter(function($detail) {
                        return strtolower($detail->kategori) === 'bahan';
                    });
                    $alatDetails = $ahsDetails->where('ahs_id', $ahs->id)->filter(function($detail) {
                        return strtolower($detail->kategori) === 'alat';
                    });
                    $detailNo = 1;
                @endphp

                <!-- Upah Section - always show -->
                <tr>
                    <td class="section-title" colspan="6">A. TENAGA KERJA</td>
                </tr>
                @if($upahDetails->count() > 0)
                    @foreach($upahDetails as $detail)
                        @php
                            $upah = $upahItems->where('id', $detail->item_id)->first();
                            $hargaSatuan = $upah ? $upah->harga : 0;
                            $jumlahHarga = $detail->koefisien * $hargaSatuan;
                        @endphp
                        <tr>
                            <td class="text-center">{{ $detailNo++ }}</td>
                            <td>{{ $upah ? $upah->uraian_tenaga : 'Upah tidak ditemukan' }}</td>
                            <td class="text-center">{{ number_format($detail->koefisien, 4) }}</td>
                            <td class="text-center">{{ $upah ? $upah->satuan : '-' }}</td>
                            <td class="currency">{{ number_format($hargaSatuan, 2, ',', '.') }}</td>
                            <td class="currency">{{ number_format($jumlahHarga, 2, ',', '.') }}</td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <td colspan="6" class="text-center">Tidak ada data tenaga kerja</td>
                    </tr>
                @endif

                <!-- Bahan Section - always show -->
                <tr>
                    <td class="section-title" colspan="6">B. BAHAN</td>
                </tr>
                @if($bahanDetails->count() > 0)
                    @foreach($bahanDetails as $detail)
                        @php
                            $bahan = $bahanItems->where('id', $detail->item_id)->first();
                            $hargaSatuan = $bahan ? $bahan->harga_bahan : 0;
                            $jumlahHarga = $detail->koefisien * $hargaSatuan;
                        @endphp
                        <tr>
                            <td class="text-center">{{ $detailNo++ }}</td>
                            <td>{{ $bahan ? $bahan->uraian_bahan : 'Bahan tidak ditemukan' }}</td>
                            <td class="text-center">{{ number_format($detail->koefisien, 4) }}</td>
                            <td class="text-center">{{ $bahan ? $bahan->satuan : '-' }}</td>
                            <td class="currency">{{ number_format($hargaSatuan, 2, ',', '.') }}</td>
                            <td class="currency">{{ number_format($jumlahHarga, 2, ',', '.') }}</td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <td colspan="6" class="text-center">Tidak ada data bahan</td>
                    </tr>
                @endif

                <!-- Alat Section - always show -->
                <tr>
                    <td class="section-title" colspan="6">C. ALAT</td>
                </tr>
                @if($alatDetails->count() > 0)
                    @foreach($alatDetails as $detail)
                        @php
                            $alat = $alatItems->where('id', $detail->item_id)->first();
                            $hargaSatuan = $alat ? $alat->harga_alat : 0;
                            $jumlahHarga = $detail->koefisien * $hargaSatuan;
                        @endphp
                        <tr>
                            <td class="text-center">{{ $detailNo++ }}</td>
                            <td>{{ $alat ? $alat->uraian_alat : 'Alat tidak ditemukan' }}</td>
                            <td class="text-center">{{ number_format($detail->koefisien, 4) }}</td>
                            <td class="text-center">{{ $alat ? $alat->satuan : '-' }}</td>
                            <td class="currency">{{ number_format($hargaSatuan, 2, ',', '.') }}</td>
                            <td class="currency">{{ number_format($jumlahHarga, 2, ',', '.') }}</td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <td colspan="6" class="text-center">Tidak ada data alat</td>
                    </tr>
                @endif

                <!-- Subtotal and Overhead -->
                @php
                    $subtotal = 0;
                    foreach($ahsDetails->where('ahs_id', $ahs->id) as $detail) {
                        $resourceId = $detail->item_id;
                        $hargaSatuan = 0;

                        $kategori = strtolower($detail->kategori);
                        if($kategori == 'upah') {
                            $resource = $upahItems->where('id', $resourceId)->first();
                            $hargaSatuan = $resource ? $resource->harga : 0;
                        } elseif($kategori == 'bahan') {
                            $resource = $bahanItems->where('id', $resourceId)->first();
                            $hargaSatuan = $resource ? $resource->harga_bahan : 0;
                        } elseif($kategori == 'alat') {
                            $resource = $alatItems->where('id', $resourceId)->first();
                            $hargaSatuan = $resource ? $resource->harga_alat : 0;
                        }

                        $subtotal += $detail->koefisien * $hargaSatuan;
                    }

                    $overhead = ($subtotal * $ahs->overhead) / 100;
                    $grandTotal = $subtotal + $overhead;
                @endphp

                <tr class="total-row">
                    <td colspan="5" class="text-right">SUBTOTAL</td>
                    <td class="currency">{{ number_format($subtotal, 2, ',', '.') }}</td>
                </tr>
                <tr>
                    <td colspan="5" class="text-right">OVERHEAD {{ $ahs->overhead }}%</td>
                    <td class="currency">{{ number_format($overhead, 2, ',', '.') }}</td>
                </tr>
                <tr class="total-row">
                    <td colspan="5" class="text-right">HARGA SATUAN PEKERJAAN</td>
                    <td class="currency">{{ number_format($grandTotal, 2, ',', '.') }}</td>
                </tr>
            </tbody>
        </table>

        @if(!$loop->last)
            <div class="page-break"></div>
        @endif
    @endforeach
    @endif

    @if(!isset($exportOptions) || in_array('upah', $exportOptions))
    <div class="page-break"></div>

    <!-- Upah Table -->
    <h2>DAFTAR HARGA SATUAN UPAH</h2>
    <table>
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="55%">Uraian Upah</th>
                <th width="15%">Satuan</th>
                <th width="25%">Harga Satuan (Rp)</th>
            </tr>
        </thead>
        <tbody>
            @forelse($upahItems as $index => $upah)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>{{ $upah->uraian_tenaga }}</td>
                    <td class="text-center">{{ $upah->satuan }}</td>
                    <td class="currency">{{ number_format($upah->harga, 2, ',', '.') }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="4" class="text-center">Tidak ada data upah</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    @endif

    @if(!isset($exportOptions) || in_array('bahan', $exportOptions))
    <div class="page-break"></div>

    <!-- Bahan Table -->
    <h2>DAFTAR HARGA SATUAN BAHAN</h2>
    <table>
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="55%">Uraian Bahan</th>
                <th width="15%">Satuan</th>
                <th width="25%">Harga Satuan (Rp)</th>
            </tr>
        </thead>
        <tbody>
            @forelse($bahanItems as $index => $bahan)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>{{ $bahan->uraian_bahan }}</td>
                    <td class="text-center">{{ $bahan->satuan }}</td>
                    <td class="currency">{{ number_format($bahan->harga_bahan, 2, ',', '.') }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="4" class="text-center">Tidak ada data bahan</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    @endif

    @if(!isset($exportOptions) || in_array('alat', $exportOptions))
    <div class="page-break"></div>

    <!-- Alat Table -->
    <h2>DAFTAR HARGA SATUAN ALAT</h2>
    <table>
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="55%">Uraian Alat</th>
                <th width="15%">Satuan</th>
                <th width="25%">Harga Satuan (Rp)</th>
            </tr>
        </thead>
        <tbody>
            @forelse($alatItems as $index => $alat)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>{{ $alat->uraian_alat }}</td>
                    <td class="text-center">{{ $alat->satuan }}</td>
                    <td class="currency">{{ number_format($alat->harga_alat, 2, ',', '.') }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="4" class="text-center">Tidak ada data alat</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    @endif

    @if(!isset($exportOptions) || in_array('time-schedule', $exportOptions))
    <div class="page-break"></div>

    @include('pdf.time-schedule')

    @include('pdf.kurva-s')
    @endif

    <div class="footer">
        <p>Dicetak pada: {{ date('d F Y H:i:s') }}</p>
    </div>
</body>
</html>
