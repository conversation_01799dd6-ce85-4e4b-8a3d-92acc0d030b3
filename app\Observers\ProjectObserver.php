<?php

namespace App\Observers;

use App\Models\Project;
use App\Models\Rab;

class ProjectObserver
{
    public function updated(Project $project)
    {
        // Jika nama project berubah, update judul RAB
        if ($project->isDirty('name')) {
            $rab = Rab::where('project_id', $project->id)->first();
            if ($rab) {
                $rab->judul = $project->name;
                $rab->save();
            }
        }
    }
}
