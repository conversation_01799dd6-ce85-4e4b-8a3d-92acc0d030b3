<?php

namespace App\Observers;

use App\Models\Ahs;
use App\Models\ItemPekerjaan;

class AhsObserver
{
    /**
     * Ketika record AHS diupdate, perbarui data item_pekerjaans yang berelasi.
     */
    public function updated(Ahs $ahs)
    {
        // Jika kolom grand_total atau satuan berubah
        if ($ahs->wasChanged('grand_total') || $ahs->wasChanged('satuan')) {
            $items = ItemPekerjaan::where('ahs_id', $ahs->id)->get();
            foreach ($items as $item) {
                $item->harga_satuan = $ahs->grand_total;
                $item->satuan = $ahs->satuan;
                $volume = $item->volume ?? 0;
                // Jika volume > 0, harga_total = volume * harga_satuan; jika volume 0, set harga_total sama dengan harga_satuan
                $item->harga_total = $volume > 0 ? $volume * $item->harga_satuan : $item->harga_satuan;
                $item->save();
            }
        }
    }
}
