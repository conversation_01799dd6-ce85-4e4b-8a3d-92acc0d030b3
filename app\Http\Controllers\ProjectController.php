<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\Project;
use App\Models\KategoriPekerjaan;
use App\Models\ItemPekerjaan;
use App\Models\ProjectShare;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class ProjectController extends Controller
{
    public function index()
    {
        try {
            $user = Auth::user();

            // Get user's own projects
            $ownProjects = Project::where('user_id', Auth::id())->with('rab')->get();

            // Get projects shared with the user
            $sharedProjects = $this->getSharedProjects($user);

            // Combine projects
            $projects = $ownProjects->concat($sharedProjects);

            return view('proyek', compact('projects', 'ownProjects', 'sharedProjects'));
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Error loading project: ' . $e->getMessage());
            return response()->view('errors.500', ['message' => $e->getMessage()], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            // Log request data for debugging
            Log::info('Project store request data:', $request->all());

            // Cek terlebih dahulu batas proyek
            $user = Auth::user();

            // Admin selalu memiliki akses penuh
            if ($user->role !== 'admin') {
                // Jika user tidak memiliki langganan aktif dan tidak dalam masa trial
                $hasActiveSubscription = ($user->currentSubscription &&
                    $user->currentSubscription->status === 'active' &&
                    $user->currentSubscription->end_date > now());

                $isOnTrial = $user->trial_ends_at && $user->trial_ends_at > now();

                if (!$hasActiveSubscription && !$isOnTrial) {
                    if ($request->ajax()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Anda memerlukan langganan aktif untuk mengakses fitur ini.'
                        ], 403);
                    }

                    return redirect()->route('subscriptions.index')
                        ->with('error', 'Anda memerlukan langganan aktif untuk mengakses fitur ini.');
                }

                // Hitung jumlah proyek yang dimiliki user
                $ownProjectCount = Project::where('user_id', $user->id)->count();

                // Hitung jumlah proyek yang dishare dengan user
                $sharedProjectCount = ProjectShare::where('user_id', $user->id)->count();

                // Total proyek yang dimiliki + dishare
                $projectCount = $ownProjectCount + $sharedProjectCount;

                // Dapatkan batas proyek dari paket langganan
                $subscription = $user->currentSubscription;
                $plan = $subscription ? $subscription->plan : null;

                if ($plan && $projectCount >= $plan->project_limit) {
                    if ($request->ajax()) {
                        // Simpan pesan error ke session
                        session()->flash('project_limit_error', 'Anda telah mencapai batas jumlah proyek untuk paket langganan Anda. Silakan upgrade paket Anda untuk membuat lebih banyak proyek.');

                        return response()->json([
                            'success' => false,
                            'message' => 'Anda telah mencapai batas jumlah proyek untuk paket langganan Anda.',
                            'reload' => true // Tandai untuk reload halaman
                        ], 403);
                    }

                    return redirect()->route('proyek')
                        ->with('project_limit_error', 'Anda telah mencapai batas jumlah proyek untuk paket langganan Anda. Silakan upgrade paket Anda untuk membuat lebih banyak proyek.');
                }
            }

            // Sanitize input data to remove problematic characters
            $sanitizedData = [];
            foreach ($request->all() as $key => $value) {
                if (is_string($value)) {
                    // Remove problematic characters that could break JSON
                    $sanitizedData[$key] = preg_replace('/[\x00-\x1F\x7F-\x9F]/', '', $value);
                } else {
                    $sanitizedData[$key] = $value;
                }
            }

            // Replace request data with sanitized data
            $request->replace($sanitizedData);

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'cover_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
                'province' => 'required|string|max:255',
                'city' => 'required|string|max:255',
                'district' => 'required|string|max:255',
                'project_owner' => 'required|string|max:255',
                'year' => 'required|integer|min:2000|max:2100',
                'ppn' => 'required|numeric|min:0|max:100',
            ]);

            $project = new Project();
            $project->name = $validated['name'];
            $project->province = $validated['province'];
            $project->city = $validated['city'];
            $project->district = $validated['district'];
            $project->project_owner = $validated['project_owner'];
            $project->year = $validated['year'];
            $project->ppn = $validated['ppn'];
            $project->user_id = Auth::id();

            if ($request->hasFile('cover_photo')) {
                try {
                    // Make sure the directory exists
                    $directory = 'cover_photos';
                    if (!Storage::disk('public')->exists($directory)) {
                        Storage::disk('public')->makeDirectory($directory);
                    }

                    // Store new photo with a unique name
                    $file = $request->file('cover_photo');
                    $filename = uniqid() . '_' . time() . '.' . $file->getClientOriginalExtension();
                    $path = $file->storeAs($directory, $filename, 'public');

                    // Check if the file was stored successfully
                    if (!Storage::disk('public')->exists($path)) {
                        throw new \Exception('Failed to store the cover photo');
                    }

                    $project->cover_photo = $path;
                } catch (\Exception $e) {
                    // Log the error
                    Log::error('Error uploading cover photo: ' . $e->getMessage());

                    if ($request->ajax()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Gagal mengunggah foto sampul: ' . $e->getMessage()
                        ], 500);
                    }

                    return back()->withInput()->with('error', 'Gagal mengunggah foto sampul: ' . $e->getMessage());
                }
            }

            $project->save();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Proyek berhasil ditambahkan.',
                    'project' => $project
                ]);
            }

            return redirect()->route('projects.index')->with('success', 'Proyek berhasil ditambahkan.');
        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $e->errors()
                ], 422);
            }
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            Log::error('Error creating project: ' . $e->getMessage());
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat menambahkan proyek: ' . $e->getMessage()
                ], 500);
            }
            return back()->withInput()->with('error', 'Terjadi kesalahan saat menambahkan proyek: ' . $e->getMessage());
        }
    }

    public function update(Request $request, Project $project)
    {
        try {
            // Log request data for debugging
            Log::info('Project update request data:', $request->except('cover_photo'));

            // Sanitize input data to remove problematic characters
            $sanitizedData = [];
            foreach ($request->all() as $key => $value) {
                if (is_string($value)) {
                    // Remove problematic characters that could break JSON
                    $sanitizedData[$key] = preg_replace('/[\x00-\x1F\x7F-\x9F]/', '', $value);
                } else {
                    $sanitizedData[$key] = $value;
                }
            }

            // Replace request data with sanitized data
            $request->replace($sanitizedData);

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'cover_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
                'province' => 'required|string|max:255',
                'city' => 'required|string|max:255',
                'district' => 'required|string|max:255',
                'project_owner' => 'required|string|max:255',
                'year' => 'required|integer|min:2000|max:2100',
                'ppn' => 'required|numeric|min:0|max:100',
                'delete_photo' => 'nullable|boolean',
            ]);

            $project->name = $validated['name'];
            $project->province = $validated['province'];
            $project->city = $validated['city'];
            $project->district = $validated['district'];
            $project->project_owner = $validated['project_owner'];
            $project->year = $validated['year'];
            $project->ppn = $validated['ppn'];

            if ($request->hasFile('cover_photo')) {
                try {
                    // Delete old photo if exists
                    if ($project->cover_photo) {
                        Storage::disk('public')->delete($project->cover_photo);
                    }

                    // Make sure the directory exists
                    $directory = 'cover_photos';
                    if (!Storage::disk('public')->exists($directory)) {
                        Storage::disk('public')->makeDirectory($directory);
                    }

                    // Store new photo with a unique name
                    $file = $request->file('cover_photo');
                    $filename = uniqid() . '_' . time() . '.' . $file->getClientOriginalExtension();
                    $path = $file->storeAs($directory, $filename, 'public');

                    // Check if the file was stored successfully
                    if (!Storage::disk('public')->exists($path)) {
                        throw new \Exception('Failed to store the cover photo');
                    }

                    $project->cover_photo = $path;
                } catch (\Exception $e) {
                    // Log the error
                    Log::error('Error uploading cover photo: ' . $e->getMessage());

                    if ($request->ajax()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Gagal mengunggah foto sampul: ' . $e->getMessage()
                        ], 500);
                    }

                    return back()->withInput()->with('error', 'Gagal mengunggah foto sampul: ' . $e->getMessage());
                }
            } elseif ($request->has('delete_photo') && $request->delete_photo) {
                try {
                    // Delete photo if delete_photo is true
                    if ($project->cover_photo) {
                        Storage::disk('public')->delete($project->cover_photo);
                    }
                    $project->cover_photo = null;
                } catch (\Exception $e) {
                    // Log the error
                    Log::error('Error deleting cover photo: ' . $e->getMessage());

                    if ($request->ajax()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Gagal menghapus foto sampul: ' . $e->getMessage()
                        ], 500);
                    }

                    return back()->withInput()->with('error', 'Gagal menghapus foto sampul: ' . $e->getMessage());
                }
            }

            $project->save();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Proyek berhasil diperbarui.',
                    'project' => $project
                ]);
            }

            return redirect()->route('projects.index')->with('success', 'Proyek berhasil diperbarui.');
        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $e->errors()
                ], 422);
            }
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            Log::error('Error updating project: ' . $e->getMessage());
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat memperbarui proyek: ' . $e->getMessage()
                ], 500);
            }
            return back()->withInput()->with('error', 'Terjadi kesalahan saat memperbarui proyek: ' . $e->getMessage());
        }
    }

    public function destroy(Project $project)
    {
        $project->delete();
        return redirect()->route('proyek')->with('success', 'Proyek berhasil dihapus.');
    }

    public function duplicate(Project $project)
    {
        try {
            // Periksa batas proyek berdasarkan paket langganan
            $user = Auth::user();

            // Admin selalu memiliki akses penuh
            if ($user->role !== 'admin') {
                // Jika user tidak dalam masa trial dan tidak memiliki langganan aktif
                if ((!$user->trial_ends_at || $user->trial_ends_at <= now()) &&
                    (!$user->currentSubscription ||
                        !($user->currentSubscription->status === 'active' && $user->currentSubscription->end_date > now()))
                ) {
                    return redirect()->route('proyek')->with('error', 'Anda memerlukan langganan aktif untuk menduplikasi proyek.');
                }

                // Hitung jumlah proyek yang dimiliki user
                $ownProjectCount = Project::where('user_id', $user->id)->count();

                // Hitung jumlah proyek yang dishare dengan user
                $sharedProjectCount = ProjectShare::where('user_id', $user->id)->count();

                // Total proyek yang dimiliki + dishare
                $projectCount = $ownProjectCount + $sharedProjectCount;

                // Dapatkan batas proyek dari paket langganan
                $subscription = $user->currentSubscription;
                $plan = $subscription ? $subscription->plan : null;

                // Jika sudah mencapai batas proyek
                if ($plan && $projectCount >= $plan->project_limit) {
                    $message = 'Anda telah mencapai batas jumlah proyek untuk paket langganan Anda. Silakan upgrade paket untuk membuat lebih banyak proyek.';
                    return redirect()->route('proyek')
                        ->with('project_limit_error', $message)
                        ->with('error', $message);
                }
            }

            // Duplicate the project
            $newProject = $project->replicate();
            $newProject->name = $project->name . ' (Salinan)';
            $newProject->user_id = $user->id; // Pastikan user_id diatur dengan benar

            // Duplicate photos if they exist
            if ($project->cover_photo) {
                try {
                    $originalPath = $project->cover_photo;

                    // Make sure the directory exists
                    $directory = 'cover_photos';
                    if (!Storage::disk('public')->exists($directory)) {
                        Storage::disk('public')->makeDirectory($directory);
                    }

                    $extension = pathinfo($originalPath, PATHINFO_EXTENSION);
                    $newFilename = $directory . '/' . uniqid() . '_' . time() . '.' . $extension;

                    // Check if original file exists
                    if (!Storage::disk('public')->exists($originalPath)) {
                        Log::warning('Original cover photo not found: ' . $originalPath);
                    } else {
                        // Copy the photo
                        Storage::disk('public')->copy($originalPath, $newFilename);

                        // Check if the file was copied successfully
                        if (!Storage::disk('public')->exists($newFilename)) {
                            throw new \Exception('Failed to copy the cover photo');
                        }

                        $newProject->cover_photo = $newFilename;
                    }
                } catch (\Exception $e) {
                    // Log the error
                    Log::error('Error duplicating cover photo: ' . $e->getMessage());
                    // Continue without the photo
                }
            }

            $newProject->save();

            // Get all kategori pekerjaan from the original project
            $kategoriPekerjaans = KategoriPekerjaan::where('project_id', $project->id)->get();

            foreach ($kategoriPekerjaans as $kategori) {
                // Duplicate kategori pekerjaan
                $newKategori = $kategori->replicate();
                $newKategori->project_id = $newProject->id;
                $newKategori->save();

                // Get all item pekerjaan from the original kategori
                $itemPekerjaans = ItemPekerjaan::where('kategori_pekerjaan_id', $kategori->id)->get();

                foreach ($itemPekerjaans as $item) {
                    // Duplicate item pekerjaan
                    $newItem = $item->replicate();
                    $newItem->kategori_pekerjaan_id = $newKategori->id;
                    $newItem->save();
                }
            }

            return redirect()->route('proyek')->with('success', 'Proyek berhasil diduplikasi beserta semua data terkait.');
        } catch (\Exception $e) {
            Log::error('Error duplicating project: ' . $e->getMessage());
            return redirect()->route('proyek')->with('error', 'Terjadi kesalahan saat menduplikasi proyek: ' . $e->getMessage());
        }
    }

    /**
     * Show the share form for a project
     */
    public function showShareForm(Project $project)
    {
        // Check if user is the owner of the project
            if ($project->user_id !== Auth::id()) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses untuk membagikan proyek ini.'
                ]);
            }
            return redirect()->route('proyek')->with('error', 'Anda tidak memiliki akses untuk membagikan proyek ini.');
        }

        // Get current user
            $user = Auth::user();

        // Get all users that the project can be shared with (only customers)
        $users = User::where('role', 'customer')
            ->where('id', '!=', Auth::id()) // Exclude current user
            ->whereNotIn('id', function ($query) use ($project) {
                $query->select('user_id')
                    ->from('project_shares')
                    ->where('project_id', $project->id);
            })
            ->get();

        // For each user, check if they have reached their project limit
        foreach ($users as $potentialUser) {
            // Count user's own projects
            $ownProjectCount = Project::where('user_id', $potentialUser->id)->count();

            // Count shared projects
            $sharedProjectCount = ProjectShare::where('user_id', $potentialUser->id)->count();

            // Total projects
            $totalProjects = $ownProjectCount + $sharedProjectCount;

            // Get user's subscription plan limit
            $projectLimit = 1; // Default limit
            if ($potentialUser->currentSubscription && $potentialUser->currentSubscription->plan) {
                $projectLimit = $potentialUser->currentSubscription->plan->project_limit;
            }

            // Check if user has reached their limit
            $potentialUser->maxProjectReached = ($totalProjects >= $projectLimit);
        }

        // Get current shares
        $shares = ProjectShare::with('user')
            ->where('project_id', $project->id)
                ->get();

        // Get remaining shares
        $remainingShares = $this->getRemainingSharesForProject($user, $project->id);

        // Check if user can share more projects based on subscription
        $canShareMore = $this->canShareMoreProjects($user, $project->id);

        // Check if this is an AJAX request
        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'project' => [
                    'id' => $project->id,
                    'name' => $project->name,
                    'users' => $users,
                    'remainingShares' => $remainingShares,
                    'canShareMore' => $canShareMore
                ],
                'users' => $users,
                'shares' => $shares,
                'remainingShares' => $remainingShares,
                'canShareMore' => $canShareMore
            ]);
        }

        // For non-AJAX requests, check if can share more
        if (!$canShareMore) {
            return redirect()->route('proyek')->with('error', 'Anda telah mencapai batas maksimum pengguna yang dapat berbagi proyek ini berdasarkan paket langganan Anda.');
        }

        return view('projects.share', compact('project', 'users', 'shares', 'remainingShares'));
    }

    /**
     * Share a project with another user
     */
    public function share(Request $request, Project $project)
    {
        // Check if user is the owner of the project
            if ($project->user_id !== Auth::id()) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses untuk membagikan proyek ini.'
                ]);
            }
            return redirect()->route('proyek')->with('error', 'Anda tidak memiliki akses untuk membagikan proyek ini.');
        }

        // Validate request
        if ($request->has('email')) {
            $request->validate([
                'email' => 'required|email|exists:users,email',
                'role' => 'required|in:editor,viewer',
            ]);

            // Get user by email
            $user = User::where('email', $request->email)->first();
            if (!$user) {
                if ($request->ajax()) {
            return response()->json([
                'success' => false,
                        'message' => 'Pengguna dengan email tersebut tidak ditemukan.'
                    ]);
                }
                return redirect()->route('projects.share', $project->id)
                    ->with('error', 'Pengguna dengan email tersebut tidak ditemukan.');
            }

            $userId = $user->id;
        } else {
            $request->validate([
                'user_id' => 'required|exists:users,id',
                'role' => 'required|in:editor,viewer',
            ]);

            $userId = $request->user_id;
        }

        // Get current user
            $user = Auth::user();

        // Check if user can share more projects based on subscription
        if (!$this->canShareMoreProjects($user, $project->id)) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda telah mencapai batas maksimum pengguna yang dapat berbagi proyek ini berdasarkan paket langganan Anda.'
                ]);
            }
            return redirect()->route('proyek')->with('error', 'Anda telah mencapai batas maksimum pengguna yang dapat berbagi proyek ini berdasarkan paket langganan Anda.');
        }

        // Check if target user has reached their project limit
        $targetUser = User::find($userId);
        $ownProjectCount = Project::where('user_id', $targetUser->id)->count();
        $sharedProjectCount = ProjectShare::where('user_id', $targetUser->id)->count();
        $totalProjects = $ownProjectCount + $sharedProjectCount;

        // Get user's subscription plan limit
        $projectLimit = 1; // Default limit
        if ($targetUser->currentSubscription && $targetUser->currentSubscription->plan) {
            $projectLimit = $targetUser->currentSubscription->plan->project_limit;
        }

        if ($totalProjects >= $projectLimit) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pengguna ini telah mencapai batas maksimum proyek berdasarkan paket langganan mereka.'
                ]);
            }
            return redirect()->route('projects.share', $project->id)
                ->with('error', 'Pengguna ini telah mencapai batas maksimum proyek berdasarkan paket langganan mereka.');
        }

        // Check if project is already shared with this user
        if ($project->isSharedWith($userId)) {
            if ($request->ajax()) {
                return response()->json(['success' => false, 'message' => 'Proyek sudah dibagikan dengan pengguna ini.']);
            }
            return redirect()->route('projects.share', $project->id)->with('error', 'Proyek sudah dibagikan dengan pengguna ini.');
        }

        // Create new share
        ProjectShare::create([
            'project_id' => $project->id,
            'user_id' => $userId,
            'role' => $request->role,
        ]);

        if ($request->ajax()) {
            return response()->json(['success' => true, 'message' => 'Proyek berhasil dibagikan.']);
        }
        return redirect()->route('projects.share', $project->id)->with('success', 'Proyek berhasil dibagikan.');
    }

    /**
     * Remove a share
     */
    public function removeShare(Project $project, $shareId)
    {
        try {
            // Check if user is the owner of the project
            if ($project->user_id !== Auth::id()) {
                if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                        'message' => 'Anda tidak memiliki akses untuk menghapus berbagi proyek ini.'
                    ]);
                }
                return redirect()->route('proyek')->with('error', 'Anda tidak memiliki akses untuk menghapus berbagi proyek ini.');
            }

            // Find the share
            $share = ProjectShare::where('id', $shareId)
                ->where('project_id', $project->id)
                ->first();

            // If share not found but we need to continue, return success
            if (!$share) {
                if (request()->ajax()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Berbagi proyek sudah tidak ada.'
                    ]);
                }
                return redirect()->route('projects.share', $project->id)->with('success', 'Berbagi proyek sudah tidak ada.');
            }

            // Delete the share
            $share->delete();

            if (request()->ajax()) {
            return response()->json([
                'success' => true,
                    'message' => 'Berbagi proyek berhasil dihapus.'
            ]);
            }
            return redirect()->route('projects.share', $project->id)->with('success', 'Berbagi proyek berhasil dihapus.');
        } catch (\Exception $e) {
            // Log error
            Log::error('Error removing project share: ' . $e->getMessage());

            if (request()->ajax()) {
            return response()->json([
                'success' => false,
                    'message' => 'Terjadi kesalahan saat menghapus akses: ' . $e->getMessage()
            ], 500);
            }
            return redirect()->route('projects.share', $project->id)
                ->with('error', 'Terjadi kesalahan saat menghapus akses: ' . $e->getMessage());
        }
    }

    /**
     * Update a share role
     */
    public function updateShareRole(Request $request, Project $project, $shareId)
    {
        // Check if user is the owner of the project
        if ($project->user_id !== Auth::id()) {
            return redirect()->route('proyek')->with('error', 'Anda tidak memiliki akses untuk memperbarui berbagi proyek ini.');
        }

        // Validate request
        $request->validate([
            'role' => 'required|in:editor,viewer',
        ]);

        // Find the share
        $share = ProjectShare::findOrFail($shareId);

        // Check if share belongs to this project
        if ($share->project_id !== $project->id) {
            if ($request->ajax()) {
                return response()->json(['success' => false, 'message' => 'Berbagi proyek tidak valid.']);
            }
            return redirect()->route('projects.share', $project->id)->with('error', 'Berbagi proyek tidak valid.');
        }

        // Update the share
        $share->role = $request->role;
        $share->save();

        if ($request->ajax()) {
            return response()->json(['success' => true, 'message' => 'Peran berbagi proyek berhasil diperbarui.']);
        }
        return redirect()->route('projects.share', $project->id)->with('success', 'Peran berbagi proyek berhasil diperbarui.');
    }

    /**
     * Helper method to check if a user can share more projects based on subscription limits
     */
    private function canShareMoreProjects($user, $projectId)
    {
        if (!$user->currentSubscription || !$user->currentSubscription->plan) {
            return false;
        }

        $maxUsers = $user->currentSubscription->plan->max_users;

        // If max_users is 1, user can't share projects
        if ($maxUsers <= 1) {
            return false;
        }

        // Count current shares for this project
        $currentShares = ProjectShare::where('project_id', $projectId)->count();

        // Check if we can add more shares
        return $currentShares < ($maxUsers - 1); // -1 because the owner counts as one user
    }

    /**
     * Helper method to get the number of remaining shares available for a project
     */
    private function getRemainingSharesForProject($user, $projectId)
    {
        if (!$user->currentSubscription || !$user->currentSubscription->plan) {
            return 0;
        }

        $maxUsers = $user->currentSubscription->plan->max_users;

        // If max_users is 1, user can't share projects
        if ($maxUsers <= 1) {
            return 0;
        }

        // Count current shares for this project
        $currentShares = ProjectShare::where('project_id', $projectId)->count();

        // Calculate remaining shares
        $remainingShares = $maxUsers - 1 - $currentShares; // -1 because the owner counts as one user

        return max(0, $remainingShares);
    }

    public function show(Project $project)
    {
        try {
            // Check if the project exists
            if (!$project) {
                return response()->json([
                    'success' => false,
                    'message' => 'Proyek tidak ditemukan'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $project->id,
                    'name' => $project->name,
                    'province' => $project->province,
                    'city' => $project->city,
                    'district' => $project->district,
                    'project_owner' => $project->project_owner,
                    'year' => $project->year,
                    'ppn' => $project->ppn,
                    'cover_photo' => $project->cover_photo ? asset('storage/' . $project->cover_photo) : null
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data proyek',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updatePPN(Request $request, Project $project)
    {
        try {
            // Log request data for debugging
            Log::info('Project updatePPN request data:', $request->all());

            // Sanitize input data to remove problematic characters
            $sanitizedData = [];
            foreach ($request->all() as $key => $value) {
                if (is_string($value)) {
                    // Remove problematic characters that could break JSON
                    $sanitizedData[$key] = preg_replace('/[\x00-\x1F\x7F-\x9F]/', '', $value);
                } else {
                    $sanitizedData[$key] = $value;
                }
            }

            // Replace request data with sanitized data
            $request->replace($sanitizedData);

            $validated = $request->validate([
                'ppn' => 'required|numeric|min:0|max:100'
            ]);

            $project->ppn = $validated['ppn'];
            $project->save();

            return response()->json([
                'success' => true,
                'message' => 'PPN berhasil diperbarui',
                'ppn' => $project->ppn
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating PPN: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui PPN: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper method to get projects shared with a user
     */
    private function getSharedProjects($user)
    {
        $sharedProjects = DB::table('project_shares')
            ->join('projects', 'project_shares.project_id', '=', 'projects.id')
            ->leftJoin('users', 'projects.user_id', '=', 'users.id')
            ->where('project_shares.user_id', $user->id)
            ->select(
                'projects.*',
                'users.name as user_name',
                'users.email as user_email',
                'project_shares.role as share_role'
            )
            ->get();

        // Untuk setiap proyek yang dibagikan, ambil data RAB terkait jika ada
        foreach ($sharedProjects as $project) {
            $rab = DB::table('rabs')
                ->where('proyek_id', $project->id)
                ->first();

            if ($rab) {
                $project->rab_id = $rab->id;
                $project->rab = $rab;
            } else {
                $project->rab_id = null;
                $project->rab = null;
            }
        }

        return $sharedProjects;
    }

    /**
     * Remove user's own access from a shared project
     */
    public function removeOwnAccess($projectId)
    {
        try {
            $user = Auth::user();

            // Find the share record
            $share = ProjectShare::where('project_id', $projectId)
                ->where('user_id', $user->id)
                ->first();

            // If share not found, return success since the end result is the same
            if (!$share) {
                if (request()->ajax()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Akses proyek sudah tidak ada.'
                    ]);
                }
                return redirect()->route('proyek')->with('success', 'Akses proyek sudah tidak ada.');
            }

            // Delete the share record
            $share->delete();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Akses ke proyek berhasil dihapus.'
                ]);
            }

            return redirect()->route('proyek')->with('success', 'Akses ke proyek berhasil dihapus.');
        } catch (\Exception $e) {
            // Log error
            Log::error('Error removing own project access: ' . $e->getMessage());

            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat menghapus akses: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->route('proyek')->with('error', 'Terjadi kesalahan saat menghapus akses: ' . $e->getMessage());
        }
    }
}
