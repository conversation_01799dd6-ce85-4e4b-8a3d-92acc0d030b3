/* ===== Layout utama: dua kolom ===== */
:root {
    --cell-width-1: 300px;
    --cell-width-2: 100px;
    --cell-width-3: 100px;
    --cell-width-4: 100px;
    --cell-width-5: 50px;
    --row-height: 45px; /* Increased row height from 30px to 45px */
    --header-height: 40px; /* Standard header height */
}

body,
html {
    margin: 0;
    padding: 0;
    height: 100%;
}

#app {
    display: flex;
    flex-direction: column;
    height: 700px;
    background-color: #fff;
    border-radius: 0.5rem;
    overflow: hidden;
    position: relative;
    /* Hapus resize: vertical untuk mencegah resize bawaan browser */
    min-height: 500px;
    /* max-height akan diatur oleh JavaScript */
    padding-bottom: 15px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.dark #app {
    background-color: #1f2937;
    border-color: #374151;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2),
        0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

/* Resize handle for vertical resizing */
.resize-handle {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 10px;
    cursor: ns-resize;
    background-color: transparent;
    z-index: 40;
    /* Limit width to content area */
    max-width: 100%;
}

.resize-handle:hover {
    background-color: rgba(59, 130, 246, 0.2);
}

.dark .resize-handle:hover {
    background-color: rgba(59, 130, 246, 0.3);
}

/* ===== Left container for grid and header ===== */
.left-container {
    flex: 0 0 420px; /* Changed from 600px to 420px */
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Hilangkan fitur scroll */
    height: 100%;
    background-color: #fff;
    position: relative;
    border-right: 1px solid #e5e7eb;
    z-index: 10;
    transition: flex-basis 0.3s ease;
}

.dark .left-container {
    background-color: #1f2937;
    border-right-color: #374151;
}

/* ===== Header untuk gantt chart ===== */
.gantt-header {
    flex: 0 0 auto;
    border-bottom: 1px solid #e5e7eb;
    overflow: visible;
    position: sticky;
    top: 0;
    z-index: 30;
    background-color: #fff;
    width: 100%;
}

.dark .gantt-header {
    background-color: #1f2937;
    border-bottom-color: #374151;
}

.header-row {
    display: flex;
    height: var(--header-height);
    align-items: stretch;
    position: relative;
    width: 100%; /* Ensure header row takes full width */
    box-sizing: border-box;
}

.header-cell {
    flex: 1 1 auto; /* Allow cells to grow and shrink */
    padding: 10px;
    font-weight: 600;
    background: #bfdbfe; /* bg-blue-200 equivalent - sama dengan thead pada RAB */
    border-right: 1px solid #e5e7eb;
    border-bottom: 1px solid #e5e7eb;
    font-size: 14px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    height: var(--header-height);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: sticky;
    top: 0;
    z-index: 5;
    transition: all 0.3s ease;
    color: #374151;
}

.header-cell:nth-child(1) {
    width: var(--cell-width-1);
}

.header-cell:nth-child(2) {
    width: var(--cell-width-2);
}

.header-cell:nth-child(3) {
    width: var(--cell-width-3);
}

.header-cell:nth-child(4) {
    width: var(--cell-width-4);
}

.header-cell:nth-child(5) {
    width: var(--cell-width-5);
    text-align: center; /* Center content for action column */
}

.dark .header-cell {
    background: #121212 !important; /* dark-table-header */
    color: #e5e7eb;
    border-right-color: #3a3a3a;
    border-bottom-color: #3a3a3a;
}

.timeline-container {
    flex: 0 0 auto;
    overflow: visible;
    z-index: 40;
    background-color: #f9fafb;
    width: max-content;
    min-width: 100%;
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .timeline-container {
    background-color: #111827;
}

.timeline-month,
.timeline-day {
    display: flex;
    background: #bfdbfe; /* bg-blue-200 equivalent - sama dengan header pada RAB */
    height: calc(var(--header-height) / 2);
    position: sticky;
    top: 0;
    z-index: 40;
}

.dark .timeline-month,
.dark .timeline-day {
    background: #121212 !important; /* dark-table-header */
    color: #fff;
}

.timeline-month .cell,
.timeline-day .cell {
    flex-shrink: 0;
    border-right: 1px solid #e5e7eb;
    text-align: center;
    padding: 8px 4px;
    font-weight: 500;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Class untuk menyembunyikan teks pada sel yang terlalu kecil */
.cell.text-hidden {
    color: transparent;
    position: relative;
}

.cell.text-hidden::after {
    content: "...";
    position: absolute;
    color: #6b7280;
    opacity: 0.7;
    font-size: 10px;
}

.dark .cell.text-hidden::after {
    color: #d1d5db;
}

.dark .timeline-month .cell,
.dark .timeline-day .cell {
    border-right-color: #374151;
}

.timeline-month {
    border-bottom: 1px solid #e5e7eb;
    top: 0;
}

.dark .timeline-month {
    border-bottom-color: #374151;
}

.timeline-day {
    top: calc(var(--header-height) / 2);
}

.timeline-month .cell {
    font-weight: 600;
}

/* ===== Body content ===== */
.gantt-body {
    flex: 1;
    display: flex;
    overflow: hidden;
    position: relative;
    height: calc(
        100% - var(--header-height)
    ); /* Ensure it takes remaining height */
}

/* ===== Grid kiri: custom <chart> sebagai tabel ===== */
#grid {
    flex: 1;
    overflow: visible;
    position: relative;
    height: calc(100% - var(--header-height));
    z-index: 10;
    background-color: #fff; /* Sama dengan warna chart-container */
}

.dark #grid {
    background-color: #252525; /* Sama dengan warna chart-container pada mode dark */
    border-right-color: #374151;
}

/* Resizer element for grid */
.grid-resizer {
    position: absolute;
    top: 0;
    right: -5px;
    width: 10px;
    height: 100%;
    cursor: col-resize;
    z-index: 20;
    background-color: transparent;
    /* Height will be set dynamically by JavaScript to match grid-body */
}

.grid-resizer:hover,
.grid-resizer.active {
    background-color: rgba(59, 130, 246, 0.2);
}

.dark .grid-resizer:hover,
.dark .grid-resizer.active {
    background-color: rgba(59, 130, 246, 0.3);
}

chart {
    display: table;
    border-collapse: collapse;
    width: 100%;
    table-layout: fixed; /* For consistent column widths */
}

chart-head,
chart-body,
chart-foot {
    display: table-header-group;
    display: table-row-group;
    display: table-footer-group;
}

/* Add strict style for chart-rows and task-rows to ensure exact height */
chart-row {
    display: table-row;
    height: var(--row-height);
    min-height: var(--row-height);
    max-height: var(--row-height);
    position: relative;
    box-sizing: border-box;
    overflow: hidden; /* Prevent content from breaking layout */
}

chart-cell {
    display: table-cell;
    border: 1px solid #e5e7eb;
    padding: 8px;
    font-size: 14px;
    white-space: nowrap;
    box-sizing: border-box;
    height: var(--row-height);
    min-height: var(--row-height);
    max-height: var(--row-height);
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: calc(var(--row-height) - 16px); /* Account for the padding */
    transition: all 0.3s ease;
    color: #374151;
    background-color: #ffffff; /* Light mode background color - sama dengan tabel body pada RAB */
}

/* Styles for expanded mode */
.header-cell.expanded {
    flex: 1 1 auto;
}

.header-cell.expanded:nth-child(5) {
    flex: 0 0 60px;
}

chart-cell.expanded {
    display: block; /* Change from table-cell to block for better alignment */
    flex: 1 1 auto;
}

chart-cell.expanded:nth-child(5) {
    flex: 0 0 60px;
}

/* When in expanded mode, make chart-row a flex container */
chart-row.expanded {
    display: flex !important;
    width: 100%;
}

chart-cell:nth-child(1) {
    width: var(--cell-width-1);
}

chart-cell:nth-child(2) {
    width: var(--cell-width-2);
}

chart-cell:nth-child(3) {
    width: var(--cell-width-3);
}

chart-cell:nth-child(4) {
    width: var(--cell-width-4);
}

chart-cell:nth-child(5) {
    width: var(--cell-width-5);
    text-align: center; /* Center content for action button cell */
}

.dark chart-cell {
    border-color: #3a3a3a;
    color: #e5e7eb;
    background-color: #252525; /* dark-table-row-odd */
}

chart-body chart-row:nth-child(even) chart-cell {
    background: #f9fafb; /* Warna latar belakang baris genap pada tabel RAB */
    transition: all 0.3s ease;
}

.dark chart-body chart-row:nth-child(even) chart-cell {
    background: #2a2a2a; /* dark-table-row-even */
}

chart-body chart-row:hover chart-cell {
    background: #ebf5ff; /* bg-blue-50 - sama dengan hover pada halaman AHSP */
    transition: all 0.3s ease;
}

.dark chart-body chart-row:hover chart-cell {
    background: #333333; /* dark-hover */
}

chart-cell[footer] {
    background: #bfdbfe; /* bg-blue-200 equivalent - sama dengan header-row */
    font-weight: bold;
    border-top: 2px solid #e5e7eb;
}

.dark chart-cell[footer] {
    background: #121212 !important; /* dark-table-header */
    border-top-color: #3a3a3a;
}

/* ===== Scrollable container for timeline and chart ===== */
.scrollable-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    height: 100%;
    background-color: #fff;
    position: relative;
    border-left: 1px solid #e5e7eb;
    margin-left: -1px;
    z-index: 5;
}

.dark .scrollable-container {
    background-color: #252525; /* dark-table-row-odd */
}

/* ===== Panel kanan: Gantt chart ===== */
#chart-container {
    flex: 1;
    position: relative;
    height: 100%;
    min-width: 100%;
    width: max-content;
    background-color: #fff;
    overflow: visible;
}

.dark #chart-container {
    background-color: #252525; /* dark-table-row-odd */
}

.gantt-grid {
    display: grid;
    grid-template-rows: auto auto 1fr;
}

.tasks {
    position: absolute;
    height: 100%;
    top: 0;
    left: 0;
    /* Set pointer-events to none by default */
    pointer-events: none;
    min-width: 100%;
}

/* ===== Bar tugas & resizer ===== */
.task-row {
    position: absolute;
    width: 100%;
    height: var(--row-height);
    min-height: var(--row-height);
    max-height: var(--row-height);
    left: 0;
    border-bottom: 1px solid #f3f4f6;
    box-sizing: border-box;
    z-index: 15;
    pointer-events: auto;
    line-height: var(--row-height);
    overflow: hidden;
}

.dark .task-row {
    border-bottom-color: #374151;
}

.task-bar {
    position: absolute;
    background: linear-gradient(to bottom, #0a558e, #0a558e); /* light-accent */
    border-radius: 6px;
    height: 32px; /* Increased height to match new row-height */
    top: 50%;
    transform: translateY(-50%); /* Center vertically within the row */
    color: #fff;
    font-size: 14px; /* Increased font size for better readability */
    padding: 0 12px; /* Increased padding for better aesthetics */
    cursor: move;
    user-select: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 32px; /* Match line-height to height */
    z-index: 20;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15); /* Enhanced shadow */
    max-width: 95%; /* Prevent bar from being too wide */
    pointer-events: auto;
    transition: all 0.3s ease;
    display: flex;
    align-items: center; /* Center content vertically */
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .task-bar {
    background: linear-gradient(to bottom, #ed8936, #ed8936); /* dark-accent */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.task-bar:hover {
    background: rgba(10, 85, 142, 0.8); /* hover:bg-light-accent/80 */
    transform: translateY(-50%) scale(1.02);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Styles for dragging state */
.task-bar.dragging {
    opacity: 0.8;
    background: #0a558e; /* light-accent */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 30;
    cursor: grabbing;
}

/* Styles for resizing state */
.task-bar.resizing {
    opacity: 0.8;
    background: #0a558e; /* light-accent */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 30;
}

.task-bar.resizing-left {
    cursor: w-resize;
}

.task-bar.resizing-right {
    cursor: e-resize;
}

/* Ghost effect to show original position */
.task-bar-ghost {
    position: absolute;
    background: rgba(59, 130, 246, 0.3);
    border-radius: 6px; /* Match task-bar border-radius */
    height: 32px; /* Match task-bar height */
    top: 50%;
    transform: translateY(-50%);
    z-index: 15;
    pointer-events: none;
    border: 1px dashed rgba(59, 130, 246, 0.5);
}

.dark .task-bar-ghost {
    background: rgba(59, 130, 246, 0.2);
    border: 1px dashed rgba(59, 130, 246, 0.4);
}

/* Updating indicator */
.updating-indicator {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.6;
    }
}

/* Visual feedback for real-time updates */
.task-bar.updating {
    border: 1px dashed rgba(255, 255, 255, 0.5);
}

.task-bar.update-success {
    background: #10b981; /* Green for success */
    transition: background-color 0.5s ease;
}

.task-bar.update-error {
    background: #ef4444; /* Red for error */
    transition: background-color 0.5s ease;
}

/* Hover state for task bars */
.task-bar:hover {
    background: rgba(10, 85, 142, 0.8); /* hover:bg-light-accent/80 */
    cursor: grab;
}

.resizer {
    position: absolute;
    width: 8px;
    top: 0;
    bottom: 0;
    cursor: ew-resize;
}

.resizer.left {
    left: 0;
}

.resizer.right {
    right: 0;
}

/* ===== Grid vertikal di belakang bar ===== */
.grid-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none; /* Allow clicking through to tasks */
    z-index: 5;
}

.grid-lines .line {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 1px;
    background-color: #e5e7eb; /* Sama dengan border pada tabel RAB */
    height: 100%; /* Ensure lines cover full height */
    pointer-events: none;
}

/* Stronger border for week lines */
.grid-lines .week-line {
    width: 2px;
    background-color: #9ca3af; /* Warna yang lebih gelap untuk garis minggu */
    z-index: 2;
}

.dark .grid-lines .line {
    background: #374151;
}

.dark .grid-lines .week-line {
    background: #6b7280;
}

/* Horizontal grid lines for row alignment */
.horizontal-grid-line {
    position: absolute;
    height: 1px;
    background: #e5e7eb; /* Sama dengan border pada tabel RAB */
    left: 0;
    right: 0;
    pointer-events: none;
    z-index: 1; /* Below the task bars */
}

.dark .horizontal-grid-line {
    background: #374151;
}

/* ===== Canvas S-curve overlay ===== */
canvas.scurve {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
}

/* ===== Scroll syncing ===== */
#chart-container,
#grid {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
}

.dark #chart-container,
.dark #grid {
    scrollbar-color: #4b5563 #1f2937;
}

#chart-container::-webkit-scrollbar,
#grid::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

#chart-container::-webkit-scrollbar-track,
#grid::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 4px;
    margin: 2px;
}

.dark #chart-container::-webkit-scrollbar-track,
.dark #grid::-webkit-scrollbar-track {
    background: #1f2937;
}

#chart-container::-webkit-scrollbar-thumb,
#grid::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
    border: 2px solid #f3f4f6;
}

.dark #chart-container::-webkit-scrollbar-thumb,
.dark #grid::-webkit-scrollbar-thumb {
    background: #4b5563;
    border: 2px solid #1f2937;
}

#chart-container::-webkit-scrollbar-corner,
#grid::-webkit-scrollbar-corner {
    background: #f3f4f6;
}

.dark #chart-container::-webkit-scrollbar-corner,
.dark #grid::-webkit-scrollbar-corner {
    background: #1f2937;
}

/* Add CSS for action buttons and menu */
/* Styling untuk tombol action di dalam chart-cell */
.action-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    color: #3b82f6;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    margin: 0 auto; /* Center horizontally */
    text-align: center; /* Ensure icon is centered */
}

.action-btn:hover {
    background-color: #bfdbfe; /* bg-blue-200 - sama dengan hover pada action button di RAB */
    color: #1e40af; /* text-blue-700 - sama dengan hover pada action button di RAB */
}

#task-action-menu {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    animation: fadeIn 0.2s ease-in-out;
    border: 1px solid #e5e7eb;
    position: fixed; /* Ensure it's positioned relative to viewport */
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark mode styles for action menu */
.dark #task-action-menu {
    background-color: #252525; /* dark-table-row-odd */
    color: #f3f4f6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(237, 137, 54, 0.2); /* dark-accent with low opacity */
}

.dark .action-btn:hover {
    background-color: rgba(
        237,
        137,
        54,
        0.15
    ); /* dark-accent with low opacity */
    color: #ed8936;
}

/* Styling untuk task-bar dalam mode read-only */
.task-bar.task-readonly {
    cursor: not-allowed !important;
    opacity: 0.8 !important;
    pointer-events: none !important;
}

.task-bar.task-readonly .resizer {
    display: none !important;
}

/* Tombol tergembok di time-schedule */
.action-btn.locked-btn {
    cursor: not-allowed !important;
    opacity: 0.7 !important;
    position: relative !important;
    pointer-events: all !important;
}

.action-btn.locked-btn:hover::after {
    content: "\f023"; /* Font Awesome lock icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    color: #6b7280; /* gray-500 */
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    padding: 4px;
    z-index: 20;
}
