<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $feature = null): Response
    {
        $user = Auth::user();

        // Admin selalu memiliki akses penuh
        if ($user->role === 'admin') {
            return $next($request);
        }

        // Cek apakah user dalam masa trial
        $isOnTrial = $user->trial_ends_at && $user->trial_ends_at > Carbon::now();

        // Cek apakah user memiliki langganan aktif
        $hasActiveSubscription = false;
        if ($user->currentSubscription) {
            $hasActiveSubscription = $user->currentSubscription->status === 'active' &&
                $user->currentSubscription->end_date > Carbon::now();
        }

        // Jika user tidak memiliki langganan aktif dan tidak dalam masa trial
        if (!$hasActiveSubscription && !$isOnTrial) {
            // Jika request adalah AJAX, kembalikan response JSON
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda memerlukan langganan aktif untuk mengakses fitur ini.'
                ], 403);
            }

            // Redirect ke halaman langganan dengan pesan error
            return redirect()->route('subscriptions.index')
                ->with('error', 'Anda memerlukan langganan aktif untuk mengakses fitur ini.');
        }

        // Jika tidak ada fitur spesifik yang perlu dicek, lanjutkan request
        if (!$feature) {
            return $next($request);
        }

        // Cek apakah paket langganan user memiliki fitur yang diminta
        $subscription = $user->currentSubscription;
        $plan = $subscription ? $subscription->plan : null;

        // Jika user dalam masa trial, berikan akses ke semua fitur
        if ($isOnTrial) {
            return $next($request);
        }

        // Jika tidak ada plan atau fitur tidak tersedia dalam plan
        if (!$plan || !$this->planHasFeature($plan, $feature)) {
            // Jika request adalah AJAX, kembalikan response JSON
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Fitur ini tidak tersedia dalam paket langganan Anda. Silakan upgrade paket Anda.'
                ], 403);
            }

            // Redirect ke halaman langganan dengan pesan error
            return redirect()->route('subscriptions.index')
                ->with('error', 'Fitur ini tidak tersedia dalam paket langganan Anda. Silakan upgrade paket Anda.');
        }

        return $next($request);
    }

    /**
     * Check if a subscription plan has a specific feature.
     */
    private function planHasFeature($plan, $feature)
    {
        // Cek fitur berdasarkan nama fitur
        switch ($feature) {
            case 'Export ke Excel':
                return $plan->can_export_excel;
            case 'Export ke Excel dan PDF':
                return $plan->can_export_excel_formula;
            case 'Export ke PDF':
                return $plan->can_export_pdf;
            case 'Time Schedule':
                return $plan->can_use_time_schedule;
            case 'AHSP Empiris':
                return $plan->can_use_empirical_ahsp;
            default:
                // Jika fitur tidak dikenali, cek di array features
                if (!$plan->features) {
                    return false;
                }
                return in_array($feature, $plan->features);
        }
    }
}
