<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class <PERSON>han extends Model
{
    use HasFactory;

    protected $table = 'bahans';

    protected $fillable = [
        'uraian_bahan',
        'satuan',
        'harga_bahan',
        'sumber',
        'alamat'
    ];

    public function details()
    {
        return $this->morphMany(\App\Models\AhspDetail::class, 'itemable');
    }

    // Accessor untuk download Excel dengan rumus
    public function getNamaBahanAttribute()
    {
        return $this->uraian_bahan;
    }

    public function getHargaSatuanAttribute()
    {
        return $this->harga_bahan;
    }
}
