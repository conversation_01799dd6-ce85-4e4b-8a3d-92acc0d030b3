<?php

// app/Models/ItemPekerjaan.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ItemPekerjaan extends Model
{
    protected $fillable = [
        'ahs_id',
        'kategori_pekerjaan_id',
        'uraian_item',
        'satuan',
        'harga_satuan',
        'volume',
        'harga_total'
    ];

    // Jika ada relasi balik, misalnya:
    public function kategoriPekerjaan()
    {
        return $this->belongsTo(KategoriPekerjaan::class);
    }

    public function volumeCalculations()
    {
        return $this->hasMany(VolumeCalculation::class, 'item_pekerjaan_id');
    }

    protected $casts = [
        'harga_satuan' => 'float',
    ];

    public function ahs()
    {
        return $this->belongsTo(Ahs::class, 'ahs_id');
    }

    public function timeSchedule()
    {
        return $this->hasOne(TimeSchedule::class, 'item_pekerjaan_id');
    }
}
