<?php

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;

class UpdateSubscriptionPlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update Basic plan
        $basic = SubscriptionPlan::where('slug', 'basic')->first();
        if ($basic) {
            $basic->update([
                'can_export_excel' => true,
                'can_export_excel_formula' => false,
                'can_export_pdf' => true,
                'can_use_time_schedule' => false,
                'can_use_empirical_ahsp' => false,
            ]);
        }

        // Update Pro plan
        $pro = SubscriptionPlan::where('slug', 'pro')->first();
        if ($pro) {
            $pro->update([
                'can_export_excel' => true,
                'can_export_excel_formula' => true,
                'can_export_pdf' => true,
                'can_use_time_schedule' => true,
                'can_use_empirical_ahsp' => false,
            ]);
        }

        // Update Enterprise plan
        $enterprise = SubscriptionPlan::where('slug', 'enterprise')->first();
        if ($enterprise) {
            $enterprise->update([
                'can_export_excel' => true,
                'can_export_excel_formula' => true,
                'can_export_pdf' => true,
                'can_use_time_schedule' => true,
                'can_use_empirical_ahsp' => true,
            ]);
        }

        // Update Yearly plan
        $yearly = SubscriptionPlan::where('slug', 'yearly')->first();
        if ($yearly) {
            $yearly->update([
                'can_export_excel' => true,
                'can_export_excel_formula' => true,
                'can_export_pdf' => true,
                'can_use_time_schedule' => true,
                'can_use_empirical_ahsp' => true,
            ]);
        }
    }
}
