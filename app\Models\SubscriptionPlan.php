<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    use \Illuminate\Database\Eloquent\Factories\HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'duration_days',
        'features',
        'is_active',
        'is_featured',
        'project_limit',
        'max_users',
        'can_export_excel',
        'can_export_excel_formula',
        'can_export_pdf',
        'can_use_time_schedule',
        'can_use_empirical_ahsp'
    ];

    protected $casts = [
        'price' => 'float',
        'features' => 'array',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'project_limit' => 'integer',
        'max_users' => 'integer',
        'duration_days' => 'integer',
        'can_export_excel' => 'boolean',
        'can_export_excel_formula' => 'boolean',
        'can_export_pdf' => 'boolean',
        'can_use_time_schedule' => 'boolean',
        'can_use_empirical_ahsp' => 'boolean'
    ];

    /**
     * Get all subscriptions for this plan
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute()
    {
        return 'Rp. ' . number_format($this->price, 0, ',', '.');
    }

    /**
     * Get duration in months
     */
    public function getDurationInMonthsAttribute()
    {
        return ceil($this->duration_days / 30);
    }

    /**
     * Get all durations for this plan
     */
    public function durations()
    {
        return $this->hasMany(SubscriptionDuration::class)->orderBy('sort_order');
    }

    /**
     * Get active durations for this plan
     */
    public function activeDurations()
    {
        return $this->durations()->where('is_active', true);
    }
}
