/**
 * Enhanced Pagination Functionality
 */
document.addEventListener("DOMContentLoaded", function () {
    initPagination();
});

/**
 * Initialize pagination functionality
 */
function initPagination() {
    const paginationContainer = document.getElementById("pagination-container");
    if (!paginationContainer) return;

    // Add smooth scrolling when clicking pagination links
    const paginationLinks = document.querySelectorAll(
        ".pagination-link, .pagination-arrow"
    );

    paginationLinks.forEach((link) => {
        if (
            !link.classList.contains("pagination-arrow-disabled") &&
            !link.classList.contains("pagination-btn-disabled")
        ) {
            // Navigation with clean URL
            link.addEventListener("click", function (e) {
                e.preventDefault();

                // Get the URL from the link
                const url = this.getAttribute("href");
                if (!url) return;

                // Extract page number from URL
                const pageMatch = url.match(/page=(\d+)/);
                const pageNumber = pageMatch ? pageMatch[1] : 1;

                // Scroll to top of the content
                window.scrollTo({
                    top: 0,
                    behavior: "smooth",
                });

                // Store page number in sessionStorage
                sessionStorage.setItem("currentPage", pageNumber);

                // Set a flag in localStorage to indicate this is a pagination navigation
                localStorage.setItem("isPaginationNavigation", "true");
                localStorage.setItem(
                    "paginationTimestamp",
                    Date.now().toString()
                );

                // Create a form to submit the page parameter without showing in URL
                const form = document.createElement("form");
                form.method = "POST";
                form.action = window.location.pathname; // Clean URL
                form.style.display = "none";

                // Add attribute to disable loading animation
                form.setAttribute("data-no-loading", "true");

                // Add CSRF token
                const csrfToken = document.querySelector(
                    'meta[name="csrf-token"]'
                );
                if (csrfToken) {
                    const csrfInput = document.createElement("input");
                    csrfInput.type = "hidden";
                    csrfInput.name = "_token";
                    csrfInput.value = csrfToken.getAttribute("content");
                    form.appendChild(csrfInput);
                }

                // Add method spoofing for GET request
                const methodInput = document.createElement("input");
                methodInput.type = "hidden";
                methodInput.name = "_method";
                methodInput.value = "GET";
                form.appendChild(methodInput);

                // Add page parameter
                const pageInput = document.createElement("input");
                pageInput.type = "hidden";
                pageInput.name = "page";
                pageInput.value = pageNumber;
                form.appendChild(pageInput);

                // Disable loading animation for pagination
                if (window.hideLoading) {
                    // Ensure any existing loading is hidden
                    window.hideLoading();
                }

                // Add form to body and submit
                document.body.appendChild(form);
                form.submit();
            });
        }
    });

    // Add hover animations
    paginationLinks.forEach((element) => {
        if (
            !element.classList.contains("pagination-arrow-disabled") &&
            !element.classList.contains("pagination-current")
        ) {
            // Add pulse effect on hover
            element.addEventListener("mouseenter", function () {
                this.classList.add("hover-pulse");
            });

            element.addEventListener("mouseleave", function () {
                this.classList.remove("hover-pulse");
            });
        }
    });

    // Add entrance animation
    paginationContainer.style.opacity = "0";
    paginationContainer.style.transform = "translateY(20px)";

    setTimeout(() => {
        paginationContainer.style.transition = "all 0.5s ease-out";
        paginationContainer.style.opacity = "1";
        paginationContainer.style.transform = "translateY(0)";
    }, 100);

    // Add scroll behavior to hide/show pagination
    let lastScrollTop = 0;
    let scrollThreshold = 10;
    let scrollTimeout;

    // Add padding to the bottom of the content container to prevent content from being hidden behind the fixed pagination
    const contentContainer = document.querySelector(
        ".p-4.sm\\:ml-64 > .p-4.mt-14"
    );
    if (contentContainer) {
        // Wait for pagination to be fully rendered
        setTimeout(() => {
            contentContainer.style.paddingBottom =
                paginationContainer.offsetHeight + 16 + "px";
        }, 100);
    }

    window.addEventListener("scroll", function () {
        clearTimeout(scrollTimeout);

        scrollTimeout = setTimeout(function () {
            const st = window.pageYOffset || document.documentElement.scrollTop;

            // If we're at the top or bottom of the page, always show pagination
            if (
                st <= 0 ||
                window.innerHeight + st >= document.body.offsetHeight - 50
            ) {
                paginationContainer.classList.remove("pagination-hidden");
                return;
            }

            // Hide when scrolling down, show when scrolling up
            if (st > lastScrollTop + scrollThreshold) {
                // Scrolling down
                paginationContainer.classList.add("pagination-hidden");
            } else if (st < lastScrollTop - scrollThreshold) {
                // Scrolling up
                paginationContainer.classList.remove("pagination-hidden");
            }

            lastScrollTop = st <= 0 ? 0 : st; // For Mobile or negative scrolling
        }, 100);
    });

    // Show pagination when hovering near the bottom of the screen
    document.addEventListener("mousemove", function (e) {
        if (e.clientY > window.innerHeight - 100) {
            paginationContainer.classList.remove("pagination-hidden");
        }
    });

    // Handle browser back/forward buttons
    window.addEventListener("popstate", function (event) {
        // Get page number from state or default to 1
        const pageNumber = event.state ? event.state.page : 1;

        // Store page number in sessionStorage
        sessionStorage.setItem("currentPage", pageNumber);

        // Set a flag in localStorage to indicate this is a pagination navigation
        localStorage.setItem("isPaginationNavigation", "true");
        localStorage.setItem("paginationTimestamp", Date.now().toString());

        // Create a form to submit the page parameter without showing in URL
        const form = document.createElement("form");
        form.method = "POST";
        form.action = window.location.pathname; // Clean URL
        form.style.display = "none";

        // Add attribute to disable loading animation
        form.setAttribute("data-no-loading", "true");

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement("input");
            csrfInput.type = "hidden";
            csrfInput.name = "_token";
            csrfInput.value = csrfToken.getAttribute("content");
            form.appendChild(csrfInput);
        }

        // Add method spoofing for GET request
        const methodInput = document.createElement("input");
        methodInput.type = "hidden";
        methodInput.name = "_method";
        methodInput.value = "GET";
        form.appendChild(methodInput);

        // Add page parameter
        const pageInput = document.createElement("input");
        pageInput.type = "hidden";
        pageInput.name = "page";
        pageInput.value = pageNumber;
        form.appendChild(pageInput);

        // Disable loading animation for pagination
        if (window.hideLoading) {
            // Ensure any existing loading is hidden
            window.hideLoading();
        }

        // Add form to body and submit
        document.body.appendChild(form);
        form.submit();
    });
}
