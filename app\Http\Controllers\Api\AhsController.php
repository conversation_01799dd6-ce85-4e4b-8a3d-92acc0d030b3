<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Ahs;
use App\Models\AhspDetail;
use App\Models\ItemPekerjaan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class AhsController extends Controller
{
    /**
     * Update AHS without reloading the page
     */
    public function updateAjax(Request $request, $id)
    {
        $validated = $request->validate([
            'judul'  => 'required|string',
            'satuan'  => 'required|string',
            'kode'   => 'required|string',
            'overhead' => 'required|numeric|min:0',
            'sumber' => 'required|string',
            'detail' => 'required|array',
        ]);

        $detailData = $validated['detail'];
        $subtotal = 0;

        foreach (['bahan', 'upah', 'alat'] as $kategori) {
            if (isset($detailData[$kategori]) && is_array($detailData[$kategori])) {
                foreach ($detailData[$kategori] as $item) {
                    $subtotal += $item['harga_satuan'];
                }
            }
        }

        $overheadPercent = $validated['overhead'];
        $overhead = $subtotal * ($overheadPercent / 100);
        $grandTotal = $subtotal + $overhead;

        // Set source based on user input rather than overriding for non-admin users
        $source = $validated['sumber'];

        $ahs = Ahs::findOrFail($id);

        // Ubah logika: Hanya tambahkan AHS baru jika pembuat aslinya adalah admin dan user saat ini adalah customer
        $currentUserIsAdmin = Auth::user()->role === 'admin';
        $originalCreatorIsAdmin = $ahs->creator_role === 'admin';

        // Cek apakah ada flag create_new_ahs dari request
        $createNewAhs = $request->has('create_new_ahs') ? $request->input('create_new_ahs') : false;

        // Buat data AHSP baru jika:
        // 1. Pembuat aslinya admin dan user saat ini customer, ATAU
        // 2. Flag create_new_ahs bernilai true dan user saat ini bukan admin
        if (($originalCreatorIsAdmin && !$currentUserIsAdmin) || ($createNewAhs && !$currentUserIsAdmin)) {
            $ahs = Ahs::create([
                'kode'        => $validated['kode'],
                'judul'       => $validated['judul'],
                'satuan'      => $validated['satuan'],
                'overhead'    => $overheadPercent,
                'grand_total' => $grandTotal,
                'sumber'      => $source,
                'user_id'     => Auth::id(),
                'creator_role' => Auth::user()->role,
                'created_by'  => Auth::id(),
            ]);

            // Update item_pekerjaans dengan ahs_id baru
            ItemPekerjaan::where('ahs_id', $id)->update([
                'ahs_id' => $ahs->id,
                'harga_satuan' => $grandTotal,
                'harga_total' => DB::raw('volume * ' . $grandTotal),
            ]);
        } else {
            // Selain itu, update data yang ada
            $ahs->update([
                'kode'        => $validated['kode'],
                'judul'       => $validated['judul'],
                'satuan'      => $validated['satuan'],
                'overhead'    => $overheadPercent,
                'grand_total' => $grandTotal,
                'sumber'      => $source,
            ]);

            // Hapus detail lama
            $ahs->details()->delete();

            // Update item_pekerjaans dengan harga baru
            ItemPekerjaan::where('ahs_id', $id)->update([
                'harga_satuan' => $grandTotal,
                'harga_total' => DB::raw('volume * ' . $grandTotal),
            ]);
        }

        foreach (['bahan', 'upah', 'alat'] as $kategori) {
            if (isset($detailData[$kategori]) && is_array($detailData[$kategori])) {
                foreach ($detailData[$kategori] as $item) {
                    AhspDetail::create([
                        'ahs_id'       => $ahs->id,
                        'kategori'     => $kategori,
                        'item_id'      => $item['item_id'],
                        'kode'         => $validated['kode'],
                        'judul'        => $validated['judul'],
                        'item_text'    => $item['item_text'] ?? '',
                        'satuan'       => $item['satuan'] ?? '',
                        'harga_dasar'  => $item['harga_dasar'] ?? 0,
                        'koefisien'    => $item['koefisien'],
                        'harga_satuan' => $item['harga_satuan'],
                    ]);
                }
            }
        }

        // Get the updated item_pekerjaan data
        $itemPekerjaan = ItemPekerjaan::where('ahs_id', $ahs->id)->first();

        return response()->json([
            'success' => true,
            'message' => 'Data AHSP berhasil diperbarui.',
            'data' => $ahs,
            'item_pekerjaan' => $itemPekerjaan,
            'grand_total' => $grandTotal,
            'formatted_grand_total' => number_format($grandTotal, 2, '.', ',')
        ]);
    }

    /**
     * Get the current grand total for an AHS
     */
    public function getGrandTotal($id)
    {
        $ahs = Ahs::findOrFail($id);
        return response()->json([
            'success' => true,
            'grand_total' => $ahs->grand_total,
            'formatted_grand_total' => number_format($ahs->grand_total, 2, '.', ',')
        ]);
    }
}
