// Preview Download Modal Functions
document.addEventListener("DOMContentLoaded", function () {
    // Fungsi untuk menangani tombol tutup pada previewDownloadModal
    const previewDownloadModal = document.getElementById(
        "previewDownloadModal"
    );
    if (previewDownloadModal) {
        // Tambahkan event listener untuk tombol close
        const closeButtons = document.querySelectorAll(
            'button[onclick="closePreviewDownloadModal()"]'
        );
        closeButtons.forEach((button) => {
            button.addEventListener("click", function () {
                // Change URL back to /rab when modal is closed
                if (window.location.href.includes("showPreviewDownload")) {
                    const newUrl = window.location.href.split("?")[0];
                    window.history.pushState({}, "", newUrl);
                }
            });
        });
    }
});

// Fungsi untuk membuka modal preview download
window.openPreviewDownloadModal = function () {
    const modal = document.getElementById("previewDownloadModal");
    if (modal) {
        // Tambahkan class flex untuk menampilkan modal dengan flexbox
        modal.classList.remove("hidden");
        modal.classList.add("flex");
    }
};

// Fungsi untuk menutup modal preview download
window.closePreviewDownloadModal = function () {
    const modal = document.getElementById("previewDownloadModal");
    if (modal) {
        // Hapus class flex dan tambahkan hidden untuk menyembunyikan modal
        modal.classList.add("hidden");
        modal.classList.remove("flex");

        // Change URL back to /rab when modal is closed
        if (window.location.href.includes("showPreviewDownload")) {
            const newUrl = window.location.href.split("?")[0];
            window.history.pushState({}, "", newUrl);
        }
    }
};
