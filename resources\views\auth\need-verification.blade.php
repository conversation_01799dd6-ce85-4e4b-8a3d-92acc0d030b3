@extends('layouts.auth')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-light-bg dark:bg-dark-bg transition-colors duration-200">
    <div class="max-w-md w-full space-y-8 p-8 bg-white dark:bg-dark-card rounded-lg shadow-lg transition-colors duration-200">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
                V<PERSON><PERSON><PERSON><PERSON><PERSON>
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                Sebelum melanjutkan, Anda perlu memverifikasi alamat email Anda.
                <PERSON><PERSON><PERSON> periksa email Anda untuk link verifikasi.
            </p>
        </div>

        @if (session('warning'))
            <div class="bg-yellow-100 dark:bg-yellow-900 border border-yellow-400 text-yellow-700 dark:text-yellow-300 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('warning') }}</span>
            </div>
        @endif

        @if (session('status'))
            <div class="bg-green-100 dark:bg-green-900 border border-green-400 text-green-700 dark:text-green-300 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('status') }}</span>
            </div>
        @endif

        @if($errors->any())
            <div class="bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-300 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ $errors->first() }}</span>
            </div>
        @endif

        <form class="mt-8 space-y-6" action="{{ route('verification.need.send') }}" method="POST">
            @csrf
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Alamat Email
                </label>
                <div class="mt-1">
                    <input id="email" name="email" type="email" required
                        value="{{ session('email') ?? old('email') }}"
                        class="appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-dark-border placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-dark-bg-secondary rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm transition-colors duration-200"
                        placeholder="Masukkan email Anda">
                </div>
            </div>
            
            <div>
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Kirim Link Verifikasi
                </button>
            </div>
        </form>

        <div class="flex mt-4 justify-between">
            <div>
                <a href="{{ route('login') }}" class="text-sm text-indigo-600 dark:text-blue-400 hover:text-indigo-500 dark:hover:text-blue-300 transition-colors duration-200">
                    Kembali ke halaman login
                </a>
            </div>
            <div>
                <a href="{{ route('register') }}" class="text-sm text-indigo-600 dark:text-blue-400 hover:text-indigo-500 dark:hover:text-blue-300 transition-colors duration-200">
                    Daftar akun baru
                </a>
            </div>
        </div>
    </div>
</div>
@endsection 