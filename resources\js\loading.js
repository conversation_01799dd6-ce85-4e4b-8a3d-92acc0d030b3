/**
 * Sistem loading untuk aplikasi RAB Estimator
 */

let isLoading = false;
let activeProgressInterval = null;

// Fungsi untuk memeriksa apakah halaman saat ini adalah halaman login atau register
window.isAuthPage = function () {
    // Periksa URL saat ini
    const currentPath = window.location.pathname;
    return (
        currentPath.includes("/login") ||
        currentPath.includes("/register") ||
        currentPath === "/" || // Jika root redirect ke login
        currentPath.includes("/password/reset")
    );
};

// Fungsi untuk menampilkan dan menyembunyikan loading overlay
window.showLoading = function () {
    if (isLoading) return; // Hindari menampilkan multiple loading indicators

    // Hanya tampilkan loading overlay pada halaman login dan register
    if (!window.isAuthPage()) {
        return;
    }

    isLoading = true;
    document.getElementById("loading-overlay").classList.remove("hidden");
    // Tambahkan partikel ketika loading ditampilkan
    createParticles();
};

window.hideLoading = function () {
    // Jika bukan halaman auth, tidak perlu melakukan apa-apa
    if (!window.isAuthPage() && !isLoading) {
        return;
    }

    isLoading = false;
    document.getElementById("loading-overlay").classList.add("hidden");
    // Hapus partikel ketika loading disembunyikan
    const particlesContainer = document.getElementById("loading-particles");
    if (particlesContainer) {
        particlesContainer.innerHTML = "";
    }

    // Clear progress interval jika ada
    if (activeProgressInterval) {
        clearInterval(activeProgressInterval);
        activeProgressInterval = null;
    }
};

// Fungsi untuk membuat partikel floating
function createParticles() {
    const particlesContainer = document.getElementById("loading-particles");
    if (!particlesContainer) return;

    particlesContainer.innerHTML = "";

    const particleCount = 20;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement("div");
        particle.className = "particle";

        // Set ukuran acak
        const size = Math.random() * 10 + 5;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;

        // Set posisi X acak
        const posX = Math.random() * 100;
        particle.style.left = `${posX}%`;

        // Set kecepatan animasi acak
        const duration = Math.random() * 10 + 5;
        particle.style.animationDuration = `${duration}s`;

        // Set delay acak
        const delay = Math.random() * 5;
        particle.style.animationDelay = `${delay}s`;

        particlesContainer.appendChild(particle);
    }
}

// Fungsi untuk menampilkan progress bar
window.startProgress = function () {
    // Jika bukan halaman auth, tidak perlu menampilkan progress
    if (!window.isAuthPage()) {
        return null;
    }

    const bar = document.getElementById("progress-bar");
    bar.style.width = "0%";

    // Hentikan interval sebelumnya jika ada
    if (activeProgressInterval) {
        clearInterval(activeProgressInterval);
    }

    // Animasi progress dari 0% ke 90%
    let width = 0;
    activeProgressInterval = setInterval(() => {
        if (width >= 90) {
            clearInterval(activeProgressInterval);
        } else {
            width += 5;
            bar.style.width = width + "%";
        }
    }, 100);

    return activeProgressInterval;
};

window.completeProgress = function (interval) {
    // Jika bukan halaman auth atau interval tidak valid, tidak perlu melakukan apa-apa
    if (!window.isAuthPage() || !interval) {
        return;
    }

    clearInterval(interval);
    const bar = document.getElementById("progress-bar");
    bar.style.width = "100%";

    // Sembunyikan setelah selesai
    setTimeout(() => {
        bar.style.width = "0%";
    }, 300);
};

// Fungsi untuk menampilkan status loading pada button
window.setButtonLoading = function (
    button,
    isLoading,
    text = "Simpan",
    icon = "fa-save"
) {
    if (isLoading) {
        button.innerHTML =
            '<i class="fas fa-spinner fa-spin spinner mr-2"></i> <span>Memproses...</span>';
        button.disabled = true;
        button.classList.add("loading-btn");
    } else {
        button.innerHTML = '<i class="fas ' + icon + '"></i> ' + text;
        button.disabled = false;
        button.classList.remove("loading-btn");
    }
};

// Fungsi untuk membuat skeleton loader
window.createSkeletonLoader = function (container, count = 3) {
    const skeletonHTML = Array(count)
        .fill('<div class="h-8 skeleton-pulse mb-2"></div>')
        .join("");
    container.innerHTML = skeletonHTML;
};

// Menambahkan interceptor untuk fetch API - DINONAKTIFKAN
const originalFetch = window.fetch;
window.fetch = function () {
    // Tidak otomatis menampilkan loading
    // showLoading();
    // const progressInterval = startProgress();

    return originalFetch
        .apply(this, arguments)
        .then((response) => {
            // completeProgress(progressInterval);
            // hideLoading();
            return response;
        })
        .catch((error) => {
            // completeProgress(progressInterval);
            // hideLoading();
            throw error;
        });
};

// Fungsi untuk menangani pergantian halaman - DINONAKTIFKAN
function setupPageTransitionLoading() {
    // Kode ini dinonaktifkan untuk menghilangkan loading otomatis
    /*
    // Tangkap klik pada semua link internal
    document.addEventListener("click", function (e) {
        // Cari elemen anchor terdekat
        const link = e.target.closest("a");

        // Jika ini adalah link internal yang valid
        if (
            link &&
            link.href &&
            link.href.startsWith(window.location.origin) &&
            !link.hasAttribute("data-no-loading") &&
            !link.hasAttribute("target") &&
            !e.ctrlKey &&
            !e.metaKey
        ) {
            // Tampilkan loading hanya pada halaman login dan register
            if (window.isAuthPage()) {
                showLoading();
                const progressInterval = startProgress();

                // Setel timeout untuk menyelesaikan loading jika terlalu lama
                setTimeout(() => {
                    if (
                        document.getElementById("loading-overlay") &&
                        !document
                            .getElementById("loading-overlay")
                            .classList.contains("hidden")
                    ) {
                        completeProgress(progressInterval);
                        hideLoading();
                    }
                }, 8000); // 8 detik timeout
            }
        }
    });

    // Tangkap submisi form secara umum
    document.addEventListener("submit", function (e) {
        const form = e.target;

        // Jika form melakukan submit secara normal (bukan AJAX/fetch)
        if (form && !form.hasAttribute("data-no-loading")) {
            // Tampilkan loading hanya pada halaman login dan register
            if (window.isAuthPage()) {
                showLoading();
                startProgress();
            }
        }
    });

    // Tangkap kejadian sebelum halaman ditinggalkan (misalnya saat reload)
    window.addEventListener("beforeunload", function () {
        // Tidak menampilkan progress bar karena akan terlihat aneh saat reload
        // Tampilkan loading hanya pada halaman login dan register
        if (window.isAuthPage()) {
            showLoading();
        }
    });
    */
}

// Pastikan tidak ada event listener beforeunload yang menampilkan loading overlay
window.addEventListener("beforeunload", function (e) {
    // Jika bukan halaman login atau register, pastikan loading overlay tidak muncul
    if (!window.isAuthPage()) {
        const loadingOverlay = document.getElementById("loading-overlay");
        if (loadingOverlay) {
            loadingOverlay.classList.add("hidden");
            loadingOverlay.style.display = "none";
        }

        const progressBar = document.getElementById("progress-bar");
        if (progressBar) {
            progressBar.style.width = "0%";
            progressBar.style.display = "none";
        }
    }
});

// Fungsi untuk tombol btnUpdate dan btnUpdate1
window.addUpdateButtonLoadingHandlers = function () {
    // Button update pertama
    const btnUpdate = document.getElementById("btnUpdate");
    if (btnUpdate) {
        btnUpdate.addEventListener("click", function (event) {
            // Mendapatkan tombol yang diklik
            const button = event.currentTarget;

            // Menampilkan animasi loading
            setButtonLoading(button, true, "Simpan Perubahan");

            // Jika ada fungsi asli yang perlu dipanggil, bisa ditambahkan di sini

            // Opsional: kembalikan tombol ke normal setelah beberapa detik
            setTimeout(() => {
                setButtonLoading(button, false, "Simpan Perubahan");
            }, 3000);
        });
    }

    // Button update kedua
    const btnUpdate1 = document.getElementById("btnUpdate1");
    if (btnUpdate1) {
        btnUpdate1.addEventListener("click", function (event) {
            // Mendapatkan tombol yang diklik
            const button = event.currentTarget;

            // Menampilkan animasi loading
            setButtonLoading(button, true, "Simpan Perubahan 1");

            // Jika ada fungsi asli yang perlu dipanggil, bisa ditambahkan di sini

            // Opsional: kembalikan tombol ke normal setelah beberapa detik
            setTimeout(() => {
                setButtonLoading(button, false, "Simpan Perubahan 1");
            }, 3000);
        });
    }
};

// Wrapper untuk animasi loading pada saveVolumeCalculation
window.saveVolumeCalculationWithLoading = function (event) {
    // Mencegah default behavior
    event.preventDefault();

    // Mendapatkan tombol yang diklik
    const button = event.currentTarget;

    // Menampilkan animasi loading
    setButtonLoading(button, true, "Simpan");

    // Dapatkan form dan submit form secara manual
    const form = document.getElementById("volumeForm");
    if (form) {
        // Trigger submit event pada form
        const submitEvent = new Event("submit", {
            bubbles: true,
            cancelable: true,
        });
        form.dispatchEvent(submitEvent);
    } else {
        console.error("Form volumeForm tidak ditemukan");
        // Kembalikan tombol ke normal jika form tidak ditemukan
        setButtonLoading(button, false, "Simpan");
    }
};

// Wrapper untuk animasi loading pada saveSelectedItems
window.saveSelectedItemsWithLoading = function (event) {
    // Tidak perlu lagi fungsi wrapper ini karena sudah diimplementasikan di saveSelectedItems
    // Langsung panggil fungsi asli
    saveSelectedItems();
};

// Wrapper untuk animasi loading pada btnTambah
window.btnTambahWithLoading = function (event) {
    // Mendapatkan tombol yang diklik
    const button = event.currentTarget;

    // Menampilkan animasi loading pada tombol
    setButtonLoading(button, true, "Tambah");

    // Jika ada fungsi handler asli, panggil di sini
    // Sebagai contoh, jika ada onclick handler asli:
    // const originalOnclick = button.getAttribute('data-original-onclick');
    // if (originalOnclick) {
    //     eval(originalOnclick);
    // }

    // Opsional: kembalikan tombol ke normal setelah beberapa detik
    setTimeout(() => {
        setButtonLoading(button, false, "Tambah", "fa-plus-circle");

        // Cek jika ini adalah tombol dalam modal volume
        if (button.closest("#inputVolumeModal")) {
            closeInputVolumeModal();
        }
    }, 2000);
};

// Function to check if this is a pagination navigation
window.isPaginationNavigation = function () {
    const isPagination =
        localStorage.getItem("isPaginationNavigation") === "true";
    const timestamp = parseInt(
        localStorage.getItem("paginationTimestamp") || "0"
    );
    const now = Date.now();
    const isRecent = now - timestamp < 5000; // Within 5 seconds

    return isPagination && isRecent;
};

document.addEventListener("DOMContentLoaded", function () {
    // Periksa apakah ini adalah halaman login atau register
    const isAuth = window.isAuthPage();

    // Skip loading if this is a pagination navigation
    if (window.isPaginationNavigation()) {
        console.log(
            "Pagination navigation detected in loading.js - hiding loading overlay"
        );
        if (isAuth) {
            hideLoading();
        }
    }

    // Skip loading if this is a reload from profile modal
    const isProfileReload = localStorage.getItem("profileReload") === "true";
    const profileReloadTime = parseInt(
        localStorage.getItem("profileReloadTime") || "0"
    );
    const now = Date.now();
    const isRecent = now - profileReloadTime < 5000; // Within 5 seconds

    if (isProfileReload && isRecent) {
        console.log("Profile reload detected - hiding loading overlay");
        if (isAuth) {
            hideLoading();
        }

        // Clear the flag
        localStorage.removeItem("profileReload");
        localStorage.removeItem("profileReloadTime");
    }

    // Tambahkan event listener untuk semua form submit
    document.querySelectorAll("form").forEach((form) => {
        form.addEventListener("submit", function (e) {
            // Skip loading animation for pagination forms
            if (form.hasAttribute("data-no-loading")) {
                // Set flag for pagination navigation
                localStorage.setItem("isPaginationNavigation", "true");
                localStorage.setItem(
                    "paginationTimestamp",
                    Date.now().toString()
                );
                return; // Don't show loading for pagination
            }

            // Hanya tampilkan loading pada halaman login dan register
            if (!isAuth) {
                return;
            }

            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                setButtonLoading(submitBtn, true);
            }
            showLoading();
        });
    });

    // Setup loading untuk pergantian halaman - dinonaktifkan
    // setupPageTransitionLoading();

    // Tambahkan handler untuk tombol update
    addUpdateButtonLoadingHandlers();

    // Tambahkan handler pada tombol dengan atribut onclick closeVolumeModal
    document
        .querySelectorAll('button[onclick*="closeVolumeModal"]')
        .forEach((button) => {
            button.removeAttribute("onclick");
            button.addEventListener("click", closeVolumeModal);
        });

    // Tidak perlu lagi menambahkan handler pada tombol saveVolumeCalculation
    // karena form sudah memiliki event listener submit yang menangani ini

    // Tambahkan handler untuk tombol btnTambahKategori
    const btnTambahKategori = document.getElementById("btnTambahKategori");
    if (btnTambahKategori) {
        // Event listener untuk btnTambahKategori dihapus karena sudah diterapkan di rab.js
    }

    // Tambahkan handler untuk tombol btnTambah
    const btnTambah = document.getElementById("btnTambah");
    if (btnTambah) {
        btnTambah.addEventListener("click", btnTambahWithLoading);
    }

    // Sembunyikan loading saat halaman sudah selesai dimuat
    if (document.readyState === "complete") {
        if (isAuth) {
            hideLoading();
        }
    } else {
        window.addEventListener("load", function () {
            if (isAuth) {
                hideLoading();
            }
        });
    }
});
