<div class="theme-switch-container p-3 rounded-xl bg-white/5 shadow-inner backdrop-blur-sm">
    <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
            <!-- Sun icon -->
            <div class="text-white text-opacity-80">
                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="5"></circle>
                    <line x1="12" y1="1" x2="12" y2="3"></line>
                    <line x1="12" y1="21" x2="12" y2="23"></line>
                    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                    <line x1="1" y1="12" x2="3" y2="12"></line>
                    <line x1="21" y1="12" x2="23" y2="12"></line>
                    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                </svg>
            </div>

            <!-- Toggle switch -->
            <button id="{{ $toggleId ?? 'theme-toggle-component' }}" type="button" class="theme-toggle-elegant relative w-14 h-7 rounded-full bg-gradient-to-r from-yellow-400 to-light-accent dark:from-amber-700 dark:to-amber-900 transition-all duration-300 cursor-pointer">
                <span class="sr-only">Toggle theme</span>
                <div id="{{ $toggleIconId ?? 'theme-toggle-component-icon' }}" class="absolute top-1 left-1 h-5 w-5 transform rounded-full bg-white shadow-md transition-transform duration-300 flex items-center justify-center overflow-hidden">
                    <svg class="moon-icon h-3 w-3 text-amber-500 absolute transition-opacity duration-200" viewBox="0 0 24 24" fill="none">
                        <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z" fill="currentColor"></path>
                    </svg>
                    <svg class="sun-icon h-3 w-3 text-yellow-500 absolute transition-opacity duration-200" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="5" fill="currentColor"></circle>
                        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" stroke="currentColor" stroke-width="2"></path>
                    </svg>
                </div>
            </button>

            <!-- Moon icon -->
            <div class="text-white text-opacity-80">
                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                </svg>
            </div>
        </div>

        <div class="text-sm text-white/80 font-medium">
            <span class="dark-mode-label">{{ $label ?? 'Mode' }}</span>
        </div>
    </div>
</div>

<script>
    // Pastikan script ini hanya dijalankan sekali per toggle
    document.addEventListener('DOMContentLoaded', function() {
        const toggleId = '{{ $toggleId ?? 'theme-toggle-component' }}';
        const toggleIconId = '{{ $toggleIconId ?? 'theme-toggle-component-icon' }}';
        const iconTransform = '28px';

        // Cek apakah toggle sudah diinisialisasi
        if (!window.initializedToggles) {
            window.initializedToggles = {};
        }

        if (!window.initializedToggles[toggleId]) {
            const toggleBtn = document.getElementById(toggleId);
            const toggleIcon = document.getElementById(toggleIconId);

            if (toggleBtn && toggleIcon) {
                // Set posisi awal icon berdasarkan tema saat ini
                if (document.documentElement.classList.contains('dark')) {
                    toggleIcon.style.transform = `translateX(${iconTransform})`;
                } else {
                    toggleIcon.style.transform = 'translateX(0)';
                }

                // Tambahkan event listener
                toggleBtn.addEventListener('click', function() {
                    // Toggle dark class pada document element
                    document.documentElement.classList.toggle('dark');

                    // Update posisi icon
                    if (document.documentElement.classList.contains('dark')) {
                        toggleIcon.style.transform = `translateX(${iconTransform})`;
                        localStorage.setItem('theme', 'dark');
                    } else {
                        toggleIcon.style.transform = 'translateX(0)';
                        localStorage.setItem('theme', 'light');
                    }

                    // Update semua toggle lain jika ada
                    document.querySelectorAll('[id$="-toggle-icon"]').forEach(icon => {
                        if (icon.id !== toggleIconId) {
                            if (document.documentElement.classList.contains('dark')) {
                                icon.style.transform = `translateX(${iconTransform})`;
                            } else {
                                icon.style.transform = 'translateX(0)';
                            }
                        }
                    });
                });

                // Tandai toggle ini sudah diinisialisasi
                window.initializedToggles[toggleId] = true;
            }
        }
    });
</script>