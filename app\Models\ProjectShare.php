<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProjectShare extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'user_id',
        'role'
    ];

    /**
     * Get the project that is shared
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the user that the project is shared with
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the user has editor role
     */
    public function isEditor()
    {
        return $this->role === 'editor';
    }

    /**
     * Check if the user has viewer role
     */
    public function isViewer()
    {
        return $this->role === 'viewer';
    }
}
