<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\PaymentLinkMail;
use App\Mail\PaymentStatusMail;

class PaymentController extends Controller
{
    /**
     * Display a listing of the user's payments.
     */
    public function index()
    {
        $user = Auth::user();
        $payments = \App\Models\Payment::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('payments.index', compact('payments'));
    }

    /**
     * Show the payment details.
     */
    public function show($id)
    {
        $payment = \App\Models\Payment::findOrFail($id);

        // Pastikan user hanya bisa melihat pembayarannya sendiri
        if ($payment->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        return view('payments.show', compact('payment'));
    }

    /**
     * Show the form for uploading payment proof.
     */
    public function uploadForm($id)
    {
        $payment = \App\Models\Payment::findOrFail($id);

        // Pastikan user hanya bisa mengupload bukti pembayarannya sendiri
        if ($payment->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Cek mode verifikasi pembayaran
        $paymentVerificationMode = \App\Models\SystemSetting::getValue('payment_verification_mode', 'manual');

        // Pastikan payment masih pending atau dalam proses (jika mode manual)
        if (!($payment->status === 'pending' ||
            ($payment->status === 'processing' && $paymentVerificationMode === 'manual' && !$payment->payment_proof))) {
            return redirect()->route('payments.show', $payment->id)
                ->with('error', 'Bukti pembayaran hanya dapat diunggah untuk pembayaran yang masih pending atau sedang diproses.');
        }

        return view('payments.upload', compact('payment'));
    }

    /**
     * Process the payment proof upload.
     */
    public function upload(Request $request, $id)
    {
        $payment = \App\Models\Payment::findOrFail($id);

        // Pastikan user hanya bisa mengupload bukti pembayarannya sendiri
        if ($payment->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Cek mode verifikasi pembayaran
        $paymentVerificationMode = \App\Models\SystemSetting::getValue('payment_verification_mode', 'manual');

        // Pastikan payment masih pending atau dalam proses (jika mode manual)
        if (!($payment->status === 'pending' ||
            ($payment->status === 'processing' && $paymentVerificationMode === 'manual' && !$payment->payment_proof))) {
            return redirect()->route('payments.show', $payment->id)
                ->with('error', 'Bukti pembayaran hanya dapat diunggah untuk pembayaran yang masih pending atau sedang diproses.');
        }

        // Validasi input
        $request->validate([
            'payment_proof' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        // Upload bukti pembayaran
        $path = $request->file('payment_proof')->store('payment_proofs', 'public');

        // Store previous status for comparison
        $previousStatus = $payment->status;

        // Update payment
        $payment->payment_proof = $path;

        // Hanya ubah status jika sebelumnya pending
        if ($payment->status === 'pending') {
            $payment->status = 'processing'; // Status berubah menjadi processing, menunggu verifikasi admin
        }

        $payment->save();

        // Send notification email if status has changed
        if ($previousStatus !== $payment->status) {
            $this->sendPaymentStatusNotification($payment);
        }

        return redirect()->route('payments.show', $payment->id)
            ->with('success', 'Bukti pembayaran berhasil diunggah. Pembayaran Anda sedang diproses.');
    }

    /**
     * Payment Gateway callback.
     */
    public function callback(Request $request)
    {
        try {
            // Log request untuk debugging
            Log::info('Payment callback received', [
                'headers' => $request->headers->all(),
                'request_method' => $request->method(),
                'request_data' => $request->getContent()
            ]);

            // Validate callback request
            $isValid = $this->validatePaymentCallback($request);
            if (!$isValid) {
                Log::error('Invalid payment callback', [
                    'headers' => $request->headers->all(),
                    'request_data' => $request->getContent()
                ]);
                return response()->json(['status' => 'error', 'message' => 'Invalid callback'], 400);
            }

            // Process callback based on payment gateway
            $paymentGateway = \App\Models\SystemSetting::getValue('payment_gateway', 'xendit');

            if ($paymentGateway === 'xendit') {
                return $this->processXenditCallback($request);
            }

            return response()->json(['status' => 'error', 'message' => 'Unsupported payment gateway'], 400);
        } catch (\Exception $e) {
            Log::error('Error processing payment callback: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Send payment status notification email.
     */
    private function sendPaymentStatusNotification($payment)
    {
        try {
            $user = $payment->user;

            if (!$user || !$user->email) {
                Log::error('Cannot send payment status notification: User or email not found', [
                    'payment_id' => $payment->id,
                    'payment_id_value' => $payment->payment_id,
                    'user_id' => $payment->user_id
                ]);
                return;
            }

            Log::info('Preparing to send payment status notification', [
                'payment_id' => $payment->id,
                'payment_id_value' => $payment->payment_id,
                'user_id' => $user->id,
                'user_email' => $user->email,
                'payment_status' => $payment->status,
                'payment_amount' => $payment->amount,
                'subscription_id' => $payment->subscription_id
            ]);

            Mail::to($user->email)->send(new PaymentStatusMail($user, $payment));

            Log::info('Payment status notification sent', [
                'payment_id' => $payment->id,
                'payment_id_value' => $payment->payment_id,
                'user_id' => $user->id,
                'status' => $payment->status
            ]);
        } catch (\Exception $e) {
            Log::error('Error sending payment status notification: ' . $e->getMessage(), [
                'payment_id' => $payment->id,
                'payment_id_value' => $payment->payment_id,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Process Xendit callback.
     */
    private function processXenditCallback(Request $request)
    {
        try {
            // Log seluruh request untuk debugging
            Log::info('Xendit callback received', [
                'headers' => $request->headers->all(),
                'content' => $request->getContent(),
                'ip' => $request->ip()
            ]);

            // Get external_id from Xendit notification
            $externalId = $request->input('external_id');

            // Jika tidak ada external_id, coba cari dari format lain
            if (!$externalId) {
                $externalId = $request->input('invoice_id');
            }

            if (!$externalId) {
                $externalId = $request->input('id');
            }

            // Log data callback untuk debugging
            Log::info('Processing Xendit callback', [
                'external_id' => $externalId,
                'payload' => $request->all()
            ]);

            if (!$externalId) {
                Log::error('External ID not found in Xendit callback');
                return response()->json(['status' => 'error', 'message' => 'External ID not found'], 400);
            }

            // Find payment by payment_id
            $payment = \App\Models\Payment::where('payment_id', $externalId)->first();
            if (!$payment) {
                Log::error('Payment not found for external_id: ' . $externalId);
                return response()->json(['status' => 'error', 'message' => 'Payment not found'], 404);
            }

            Log::info('Found payment', [
                'payment_id' => $payment->id,
                'db_payment_id' => $payment->payment_id,
                'payment_status' => $payment->status,
                'payment_amount' => $payment->amount
            ]);

            // Store previous status for comparison
            $previousStatus = $payment->status;

            // Cek mode verifikasi pembayaran
            $paymentVerificationMode = \App\Models\SystemSetting::getValue('payment_verification_mode', 'manual');

            // Update payment status based on status
            $status = $request->input('status');
            Log::info('Xendit status: ' . $status);

            // Simpan detail payment terlepas dari mode verifikasi
            $payment->payment_details = $request->all();

            // Proses status pembayaran sesuai mode verifikasi
            if ($paymentVerificationMode === 'automatic') {
                // Mode otomatis: update status berdasarkan callback
                switch ($status) {
                    case 'PAID':
                    case 'SETTLED':
                    case 'COMPLETED':
                        $payment->status = 'completed';
                        $payment->paid_at = now();
                        break;

                    case 'PENDING':
                        $payment->status = 'pending';
                        break;

                    case 'EXPIRED':
                    case 'FAILED':
                        $payment->status = 'failed';
                        break;

                    default:
                        // Jika status tidak dikenal, anggap PAID jika ada paid_amount
                        if ($request->has('paid_amount') && $request->input('paid_amount') > 0) {
                            $payment->status = 'completed';
                            $payment->paid_at = now();
                        }
                        break;
                }

                Log::info('Payment status updated via automatic verification', [
                    'old_status' => $previousStatus,
                    'new_status' => $payment->status
                ]);
            } else {
                // Mode manual: catat pembayaran tapi tidak update status ke completed
                // Hanya update ke failed/expired jika memang gagal
                if ($status === 'EXPIRED' || $status === 'FAILED') {
                    $payment->status = 'failed';
                    Log::info('Payment marked as failed via callback (even in manual mode)', [
                        'old_status' => $previousStatus,
                        'new_status' => 'failed'
                    ]);
                } else if ($status === 'PAID' || $status === 'SETTLED' || $status === 'COMPLETED') {
                    // Ubah status ke processing untuk menunggu approval admin
                    $payment->status = 'processing';
                    Log::info('Payment marked as processing, waiting for admin approval', [
                        'old_status' => $previousStatus,
                        'new_status' => 'processing',
                        'xendit_status' => $status
                    ]);
                }
            }

            $payment->save();

            Log::info('Updated payment status', [
                'payment_id' => $payment->id,
                'db_payment_id' => $payment->payment_id,
                'old_status' => $previousStatus,
                'new_status' => $payment->status,
                'verification_mode' => $paymentVerificationMode
            ]);

            // If payment is completed, activate the subscription
            if ($payment->status === 'completed' && $payment->subscription) {
                $payment->subscription->status = 'active';
                $payment->subscription->save();

                // Update user current subscription
                $user = $payment->user;
                $user->current_subscription_id = $payment->subscription_id;
                $user->save();

                Log::info('Activated subscription', [
                    'subscription_id' => $payment->subscription_id,
                    'user_id' => $user->id
                ]);
            }

            // Send notification email if status has changed
            if ($previousStatus !== $payment->status) {
                $this->sendPaymentStatusNotification($payment);
            }

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            Log::error('Error processing Xendit callback: ' . $e->getMessage(), [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Validate payment callback from payment gateway.
     */
    private function validatePaymentCallback(Request $request)
    {
        $paymentGateway = \App\Models\SystemSetting::getValue('payment_gateway', 'xendit');

        if ($paymentGateway === 'xendit') {
            return $this->validateXenditCallback($request);
        }

        return false;
    }

    /**
     * Validate Xendit callback signature.
     */
    private function validateXenditCallback(Request $request)
    {
        try {
            // Get callback token from request and Xendit callback token from settings
            $callbackToken = $request->header('X-Callback-Token');
            $xenditCallbackToken = \App\Models\SystemSetting::getValue('xendit_callback_token', '');

            // Log untuk debugging
            Log::info('Validating Xendit callback', [
                'received_token' => $callbackToken,
                'stored_token' => $xenditCallbackToken
            ]);

            // Cek apakah verifikasi payment diset otomatis
            $paymentVerificationMode = \App\Models\SystemSetting::getValue('payment_verification_mode', 'manual');

            if ($paymentVerificationMode === 'automatic') {
                // Mode automatic: validasi token harus benar
                if (!$callbackToken || !$xenditCallbackToken) {
                    Log::error('Xendit callback token missing');
                    return false;
                }

                $isValid = $callbackToken === $xenditCallbackToken;

                if (!$isValid) {
                    Log::error('Xendit callback token mismatch');
                    return false;
                }
            } else {
                // Mode manual: izinkan untuk testing
                Log::info('Payment verification mode set to manual, skipping Xendit callback validation');
                return true;
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Error validating Xendit callback: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send payment link to user's email.
     */
    public function sendPaymentLink($id)
    {
        $payment = \App\Models\Payment::findOrFail($id);

        // Pastikan user hanya bisa mengirim link pembayarannya sendiri
        if ($payment->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Pastikan payment masih pending
        if ($payment->status !== 'pending') {
            return redirect()->route('payments.show', $payment->id)
                ->with('error', 'Link pembayaran hanya dapat dikirim untuk pembayaran yang masih pending.');
        }

        // Pastikan subscription ada
        if (!$payment->subscription) {
            return redirect()->route('payments.show', $payment->id)
                ->with('error', 'Tidak dapat menemukan langganan terkait dengan pembayaran ini.');
        }

        try {
            // Get payment gateway configuration
            $paymentGateway = \App\Models\SystemSetting::getValue('payment_gateway', 'xendit');

            if ($paymentGateway === 'xendit') {
                $xenditSecretKey = \App\Models\SystemSetting::getValue('xendit_secret_key');

                if (empty($xenditSecretKey)) {
                    return redirect()->route('payments.show', $payment->id)
                        ->with('error', 'Konfigurasi Xendit belum lengkap. Silakan hubungi administrator.');
                }

                // Set Xendit API URL
                $apiUrl = 'https://api.xendit.co/v2/invoices';

                // Prepare invoice data
                $invoiceData = [
                    'external_id' => $payment->payment_id,
                    'amount' => (int) $payment->amount,
                    'payer_email' => Auth::user()->email,
                    'description' => $payment->description,
                    'success_redirect_url' => route('subscriptions.success'),
                    'failure_redirect_url' => route('subscriptions.error'),
                    'callback_url' => route('payments.callback')
                ];

                // Set headers
                $headers = [
                    'Content-Type: application/json',
                    'Authorization: Basic ' . base64_encode($xenditSecretKey . ':')
                ];

                // Initialize cURL
                $ch = curl_init($apiUrl);
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                curl_setopt($ch, CURLOPT_USERPWD, $xenditSecretKey . ':');
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($invoiceData));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

                // Execute cURL
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                // Check if request was successful
                if ($httpCode == 200) {
                    $responseData = json_decode($response, true);
                    $paymentUrl = $responseData['invoice_url'];

                    // Send email with payment link
                    Mail::to(Auth::user()->email)->send(new PaymentLinkMail(
                        Auth::user(),
                        $payment,
                        $payment->subscription,
                        $paymentUrl
                    ));

                    return redirect()->route('payments.show', $payment->id)
                        ->with('success', 'Link pembayaran telah dikirim ke email Anda.');
                } else {
                    // Log error
                    Log::error('Failed to create Xendit invoice', [
                        'payment_id' => $payment->id,
                        'response' => $response,
                        'http_code' => $httpCode
                    ]);

                    return redirect()->route('payments.show', $payment->id)
                        ->with('error', 'Gagal membuat invoice Xendit. Silakan coba lagi nanti.');
                }
            } else {
                return redirect()->route('payments.show', $payment->id)
                    ->with('error', 'Payment gateway tidak didukung.');
            }
        } catch (\Exception $e) {
            Log::error('Error sending payment link: ' . $e->getMessage());
            return redirect()->route('payments.show', $payment->id)
                ->with('error', 'Terjadi kesalahan saat mengirim link pembayaran: ' . $e->getMessage());
        }
    }

    /**
     * Process Xendit webhook directly without middleware.
     */
    public function xenditWebhook(Request $request)
    {
        try {
            // Log seluruh request untuk debugging
            \Illuminate\Support\Facades\Log::info('Xendit webhook received', [
                'headers' => $request->headers->all(),
                'content' => $request->getContent(),
                'ip' => $request->ip()
            ]);

            // Get external_id from Xendit notification
            $data = $request->all();
            $externalId = $data['external_id'] ?? null;

            // Jika tidak ada external_id, coba cari dari format lain
            if (!$externalId) {
                $externalId = $data['invoice_id'] ?? null;
            }

            if (!$externalId) {
                $externalId = $data['id'] ?? null;
            }

            // Log data callback untuk debugging
            \Illuminate\Support\Facades\Log::info('Processing Xendit webhook data', [
                'external_id' => $externalId,
                'payload' => $data
            ]);

            if (!$externalId) {
                \Illuminate\Support\Facades\Log::error('External ID not found in Xendit webhook');
                return response()->json(['status' => 'error', 'message' => 'External ID not found'], 400);
            }

            // Find payment by payment_id
            $payment = \App\Models\Payment::where('payment_id', $externalId)->first();
            if (!$payment) {
                \Illuminate\Support\Facades\Log::error('Payment not found for external_id: ' . $externalId);
                return response()->json(['status' => 'error', 'message' => 'Payment not found'], 404);
            }

            \Illuminate\Support\Facades\Log::info('Found payment in webhook', [
                'payment_id' => $payment->id,
                'db_payment_id' => $payment->payment_id,
                'payment_status' => $payment->status,
                'payment_amount' => $payment->amount
            ]);

            // Store previous status for comparison
            $previousStatus = $payment->status;

            // Cek mode verifikasi pembayaran
            $paymentVerificationMode = \App\Models\SystemSetting::getValue('payment_verification_mode', 'manual');

            // Update payment status based on status
            $status = $data['status'] ?? '';
            \Illuminate\Support\Facades\Log::info('Xendit webhook status: ' . $status);

            // Simpan detail payment terlepas dari mode verifikasi
            $payment->payment_details = $data;

            // Proses status pembayaran sesuai mode verifikasi
            if ($paymentVerificationMode === 'automatic') {
                // Mode otomatis: update status berdasarkan callback
                switch ($status) {
                    case 'PAID':
                    case 'SETTLED':
                    case 'COMPLETED':
                        $payment->status = 'completed';
                        $payment->paid_at = now();
                        break;

                    case 'PENDING':
                        $payment->status = 'pending';
                        break;

                    case 'EXPIRED':
                    case 'FAILED':
                        $payment->status = 'failed';
                        break;

                    default:
                        // Jika status tidak dikenal, anggap PAID jika ada paid_amount
                        if (isset($data['paid_amount']) && $data['paid_amount'] > 0) {
                            $payment->status = 'completed';
                            $payment->paid_at = now();
                        }
                        break;
                }

                \Illuminate\Support\Facades\Log::info('Payment status updated via automatic verification', [
                    'old_status' => $previousStatus,
                    'new_status' => $payment->status
                ]);
            } else {
                // Mode manual: catat pembayaran tapi tidak update status ke completed
                // Hanya update ke failed/expired jika memang gagal
                if ($status === 'EXPIRED' || $status === 'FAILED') {
                    $payment->status = 'failed';
                    \Illuminate\Support\Facades\Log::info('Payment marked as failed via webhook (even in manual mode)', [
                        'old_status' => $previousStatus,
                        'new_status' => 'failed'
                    ]);
                } else if ($status === 'PAID' || $status === 'SETTLED' || $status === 'COMPLETED') {
                    // Ubah status ke processing untuk menunggu approval admin
                    $payment->status = 'processing';
                    \Illuminate\Support\Facades\Log::info('Payment marked as processing via webhook, waiting for admin approval', [
                        'old_status' => $previousStatus,
                        'new_status' => 'processing',
                        'xendit_status' => $status
                    ]);
                }
            }

            $payment->save();

            \Illuminate\Support\Facades\Log::info('Updated payment status from webhook', [
                'payment_id' => $payment->id,
                'db_payment_id' => $payment->payment_id,
                'old_status' => $previousStatus,
                'new_status' => $payment->status,
                'verification_mode' => $paymentVerificationMode
            ]);

            // If payment is completed, activate the subscription
            if ($payment->status === 'completed' && $payment->subscription) {
                $payment->subscription->status = 'active';
                $payment->subscription->save();

                // Update user current subscription
                $user = $payment->user;
                $user->current_subscription_id = $payment->subscription_id;
                $user->save();

                \Illuminate\Support\Facades\Log::info('Activated subscription from webhook', [
                    'subscription_id' => $payment->subscription_id,
                    'user_id' => $user->id
                ]);
            }

            // Kirim notifikasi email jika status berubah (dalam try-catch terpisah agar tidak gagal proses utama)
            try {
                if ($previousStatus !== $payment->status) {
                    $this->sendPaymentStatusNotification($payment);
                }
            } catch (\Exception $emailException) {
                \Illuminate\Support\Facades\Log::error('Error sending notification: ' . $emailException->getMessage());
                // Tidak perlu throw exception disini, biarkan proses utama tetap berjalan
            }

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error processing Xendit webhook: ' . $e->getMessage(), [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }
}
