<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Http\Requests\EmailVerificationRequest;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            // Cek apakah email sudah diverifikasi
            if ($user->email_verified_at === null) {
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();

                // Redirect ke halaman permintaan verifikasi
                return redirect()->route('verification.need')
                    ->with('email', $user->email)
                    ->with('warning', 'Email belum diverifikasi. Silakan verifikasi email Anda terlebih dahulu.');
            }

            $request->session()->regenerate();
            return redirect()->intended('proyek');
        }

        return back()->withErrors([
            'email' => 'Data login yang Anda masukkan tidak sesuai dengan catatan kami.',
        ]);
    }

    public function showRegisterForm()
    {
        return view('auth.register');
    }

    public function register(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
        ]);

        // Trigger event untuk mengirim email verifikasi
        event(new Registered($user));

        // Login user secara otomatis setelah registrasi
        Auth::login($user);

        // Redirect ke halaman verifikasi email
        return redirect()->route('verification.notice');
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }

    public function showLinkRequestForm()
    {
        return view('auth.passwords.email');
    }

    public function sendResetLinkEmail(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT
            ? back()->with(['status' => __($status)])
            : back()->withErrors(['email' => __($status)]);
    }

    public function showResetForm(Request $request, $token = null)
    {
        return view('auth.passwords.reset')->with(
            ['token' => $token, 'email' => $request->email]
        );
    }

    public function reset(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) {
                $user->forceFill([
                    'password' => Hash::make($password)
                ])->setRememberToken(Str::random(60));

                $user->save();

                event(new PasswordReset($user));
            }
        );

        return $status === Password::PASSWORD_RESET
            ? redirect()->route('login')->with('status', __($status))
            : back()->withErrors(['email' => [__($status)]]);
    }

    public function showVerificationNotice()
    {
        return view('auth.verify-email');
    }

    public function resendVerificationEmail(Request $request)
    {
        // Jika user sudah login
        if ($request->user()) {
            $user = $request->user();
        }
        // Jika user belum login, cek apakah ada email di request
        else if ($request->input('email')) {
            $user = User::where('email', $request->input('email'))->first();
            if (!$user) {
                return back()->withErrors(['email' => 'Email tidak ditemukan.']);
            }
        } else {
            return back()->withErrors(['email' => 'Email tidak ditemukan.']);
        }

        // Jika email sudah diverifikasi
        if ($user->email_verified_at !== null) {
            return back()->with('status', 'Email sudah diverifikasi sebelumnya.');
        }

        // Kirim email verifikasi
        $user->sendEmailVerificationNotification();

        return back()->with('status', 'Link verifikasi telah dikirim ulang ke email Anda.');
    }

    /**
     * Verifikasi email pengguna
     */
    public function verifyEmail(EmailVerificationRequest $request)
    {
        if ($request->authorize()) {
            $request->fulfill();
            return redirect()->route('login')->with('success', 'Email berhasil diverifikasi. Silakan login.');
        }

        return redirect()->route('login')->withErrors(['email' => 'Link verifikasi tidak valid.']);
    }

    /**
     * Tampilkan halaman permintaan verifikasi email
     */
    public function needVerification()
    {
        return view('auth.need-verification');
    }

    /**
     * Kirim email verifikasi berdasarkan alamat email
     */
    public function sendVerificationLinkByEmail(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return back()->withErrors(['email' => 'Email tidak ditemukan.']);
        }

        if ($user->email_verified_at) {
            return back()->with('status', 'Email Anda sudah diverifikasi sebelumnya.');
        }

        $user->sendEmailVerificationNotification();

        return back()->with('status', 'Link verifikasi telah dikirim ke email Anda.');
    }
}
